import { get } from 'lodash-es';

export const handleError = (error) => {
  const errorStatus = get(error, 'response.status', 0);

  const errorData = get(error, 'response.data', '');

  // skip checking request url as we use one identity login portal
  if (errorStatus === 401) {
    if (process.env.IS_ONEUI) {
      window.handleLogout?.();
    } else {
      window.location.reload();
    }
  }

  return Promise.reject({ errorStatus, error: errorData, raw: error });
};

export default handleError;
