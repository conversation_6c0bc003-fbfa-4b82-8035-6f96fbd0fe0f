import { render } from '@testing-library/react';

import PropTypes from 'prop-types';

import { StoreProvider } from '../App';

const defaultProps = {
  children: null,
};

const AllTheProviders = ({ children }) => {
  return <StoreProvider>{children}</StoreProvider>;
};

AllTheProviders.defaultProps = defaultProps;

AllTheProviders.propTypes = {
  children: PropTypes.any,
};

const customRender = (ui, options) => render(ui, { wrapper: AllTheProviders, ...options });

export { customRender as render };
