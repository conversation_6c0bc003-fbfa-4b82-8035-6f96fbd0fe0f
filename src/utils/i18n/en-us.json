{"LOCALE": "en-US", "EMAIL_ADDRESS": "Email Address", "EMAIL_PLACEHOLDER": "Enter your e-mail address...", "LOGIN_ID_PLACEHOLDER": "Enter Your Login ID...", "LOGIN_ID_LABEL": "Login ID", "LOGIN_PASSWORD_PLACEHOLDER": "Enter Your Password...", "PASSWORD_LABEL": "Password", "NEW_PASSWORD_LABEL": "New Password", "OLD_PASSWORD_LABEL": "Old Password", "CONFIRM_NEW_PASSWORD_LABEL": "Confirm New Password", "PASSWORD_PLACEHOLDER": "Enter Password", "FORGOT_PASSWORD": "Forgot Password?", "REMEMBER_ME": "Remember me", "RESET_PASSWORD": "Reset Password", "CHANGE_PASSWORD": "Change Password", "SUCCESS": "Success", "RESEND": "Resend", "SIGN_IN_LABEL": "Sign In", "INITIAL_DOMAIN_NAME": "Initial Domain Name", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "PHONE_NUMBER": "Phone number", "REQUIRED_VALIDATION_MESSAGE": "Required", "INVALID_CREDENTIALS_MESSAGE": "Invalid Login ID or Password", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "An auditor cannot log in from here", "LANGUAGE": "English (US)", "COOKIES_DISABLED": "Cookies must be allowed to use this application. Please enable your browser\"s cookie support for this site.", "COOKIES_NOT_ALLOWED": "Cookies Not Allowed", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "This browser is not supported and may break this site\"s functionality. We suggest that you update your browser to the latest version. To disregard this message, click OK.", "BROWSER_NOT_SUPPORTED": "Browser version not supported", "OK": "OK", "NEXT": "Next", "BACK": "Back", "SAVE": "Save", "SAVE_AND_NEXT": "Save and Next", "COPYRIGHT": "Copyright", "COPYRIGHT_STATEMENT": "All rights reserved.", "POWERED_BY": "Powered By", "ADMIN_PORTAL_SIGN_IN": "Admin Portal Sign In", "ALL_ORGANIZATIONS": "All Organizations", "SEARCH_PLACEHOLDER": "Search...", "GENERAL": "General", "ZPA": "Zscaler Private Access", "IAM": "Identity and Access Management", "ZIA": "Zscaler Internet Access", "ZDX": "Zscaler Digital Experience", "EC": "Edgeconnector", "CCP": "Client Connector Portal", "ZBI": "Browser Isolation", "BI": "Business Insights", "ZRA": "Risk360", "LAUNCH": "Launch", "VERIFY": "Verify", "CANCEL": "Cancel", "DONE": "Done", "RESET": "Reset", "CLEAR": "Clear", "CLEAR_ALL": "Clear All", "MY_PROFILE": "My Profile", "ZS_LOGIN": "ZSL<PERSON>in", "Z_IDENTITY": "ZIdentity", "ACCOUNT_MANAGEMENT": "Account Management", "CLOUD_CONFIGURATION": "Cloud Configuration", "PRODUCT": "Product", "CLOUD_NAME": "Cloud Name", "CLOUD_ID": "Cloud Id", "ORG_NAME": "Organization Name", "LINKED_SERVICES": "Linked Services", "LINK": "Link", "UNLINK": "Unlink", "LINKED": "Linked", "UNLINKED": "UnLinked", "LINK_TENANT": "<PERSON>", "UNLINK_TENANT": "Unlink Tenant", "ATTRIBUTE": "Attribute", "ATTRIBUTES": "Attributes", "USER_ATTRIBUTES": "User Attributes", "SESSION_ATTRIBUTE": "Session Attribute", "ADVANCED_SETTINGS": "Advanced Settings", "DIRECTORY": "Directory", "USERS": "Users", "USER_GROUPS": "User Groups", "EXTERNAL_IDENTITIES": "External Identities", "APPLICATIONS": "APPLICATIONS", "ZSCALER_SERVICES": "Zscaler Services", "SECURITY": "Security", "IP_LOCATIONS": "IP Locations", "IP_LOCATIONS_AND_GROUPS": "IP Locations & Groups", "IP_LOCATION_GROUPS": "IP Location Groups", "ADMINISTRATION": "Administration", "ADMINISTRATION_CONTROLS": "Administration Controls", "AUTHENTICATION": "Authentication", "AUTHENTICATION_METHODS": "Authentication Methods", "ENABLE_MULTIFACTOR_SETTINGS": "Enable Multifactor Settings", "ENABLE_MULTIFACTOR_AUTH_USER": "Enable Multi-Factor Authentication (MFA) for Service Enrollment", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "Enable Multi-Factor Authentication (MFA) for Administrators", "MFA_ENROLLMENT_GRACE_PERIOD": "MFA Enrollment Grace Period (in Days)", "AUDIT_LOGS": "<PERSON><PERSON>", "ADD_USER": "Add User", "IMPORT_CSV": "Import CSV", "ACTIONS": "Actions", "ACTIVATE": "Activate", "DE_ACTIVATE": "Deactivate", "BULK_ACTIVATE": "Bulk Activate", "BULK_DEACTIVATE": "Bulk Deactivate", "BULK_DELETE": "Bulk Delete", "DELETE": "Delete", "NAME": "Name", "GROUP": "Group", "SELECT": "Select", "LOGIN_ID": "Login ID", "STATUS": "Status", "GENERAL_INFORMATION": "General Information", "MY_ACCOUNT": "My Account", "FULL_NAME": "Full Name", "VERIFIED": "Verified", "LANGUAGE_LABEL": "Language", "TIMEZONE": "Timezone", "ACTIVE": "Active", "INACTIVE": "Inactive", "USER_INFORMATION": "User Information", "PRIMARY_EMAIL": "Primary Email", "SAME_AS_LOGIN_ID": "Same As Login ID", "SECONDARY_EMAIL": "Secondary Email", "ADDITIONAL_ATTRIBUTES": "Additional Attributes", "SECURITY_SETTINGS": "Security Settings", "PASSWORD_OPTION": "Password Option", "PROMPT_PASSWORD_FIRST_LOGIN": "Prompt for Password Change After the Initial Log In", "CONFIRM_PASSWORD": "Confirm Password", "ASSIGNMENT": "Assignment", "ASSIGN_GROUPS": "Assign Groups", "SET_BY_ADMIN": "Set By Administrator", "SET_BY_USER": "Set By User", "AUTO_GENERATED": "Auto-generated", "DELETE_USER": "Delete User", "EDIT_USER": "Edit User", "UPDATE": "Update", "OFF": "Off", "ON": "On", "ADD_ATTRIBUTE": "Add Attribute", "IMPORT_ATTRIBUTE": "Import Attributes", "CREATE_NEW_ATTRIBUTES": "Create New Attributes", "TABLE_NUMBER": "No.", "DISPLAY_NAME": "Display Name", "ATTRIBUTE_NAME": "Attribute Name", "DATA_TYPE": "Data Type", "TABLE_REQUIRED": "Required", "ORIGIN": "Origin", "SYSTEM_DEFINED": "system-defined", "USER_DEFINED": "user-defined", "ENABLE": "Enable", "ENABLED": "Enabled", "DISABLE": "Disable", "DISABLED": "Disabled", "INFORMATION": "Information", "ENTER_TEXT": "Enter Text...", "ATTRIBUTE_REQUIRED": "Attribute Required", "DESCRIPTION_OPTIONAL": "Description (optional)", "DESCRIPTION": "Description", "EDIT_ATTRIBUTE": "Edit Attribute", "DELETE_ATTRIBUTE": "Delete Attribute", "ADD_SESSION_ATTRIBUTE": "Add Session Attribute", "EDIT_SESSION_ATTRIBUTE": "Edit Session Attribute", "DELETE_SESSION_ATTRIBUTE": "Delete Session Attribute", "ADD_GROUP": "Add Group", "ADD_USER_GROUP": "Add User Group", "GROUP_NAME": "Group Name", "USER_NAME": "User Name", "EDIT_GROUP": "Edit Group", "DELETE_GROUP": "Delete Group", "USER_GROUP_NAME": "User Group Name", "ASSIGN_USERS": "Assign Users", "SOURCE": "Source", "STRING": "String", "INTEGER": "Integer", "BOOLEAN": "Boolean", "DATE": "Date", "DECIMAL": "Decimal", "CHANGE_PASSWORD_SETTINGS": "Change Password Settings", "ZS_SERVICES": "Zscaler Services", "CLOUD_AND_ORG_ID": "Cloud and Org ID", "APPLICATION_DETAIL": "Application Detail", "CLONE_MEMBERSHIP_FOR_APPLICATION": "Clone Membership From This Application", "ROLE": "Role", "ROLES": "Roles", "MANAGE_ROLES": "Manage Roles", "SYNC": "Sync", "USERS_ASSIGNMENT": "Users Assignment", "GROUPS_ASSIGNMENT": "User Group Assignment", "ASSIGN_ROLE": "Assign Role", "ASSIGN_SCOPE": "Assign <PERSON>", "ASSIGN_USER_GROUPS": "Groups Assignment", "ADD_MORE": "Add More", "ASSIGN_USERS_AND_GROUPS": "Assign Users and User Groups", "REVIEW": "Review", "USERS_AND_GROUPS": "User and User Groups", "TYPE": "Type", "EDIT_USERS_ROLE": "Edit Role", "EUSA_AGREEMENT": "End User Subscription Agreement", "DELETE_USERS_ROLE": "Delete User", "EDIT_GROUPS_ROLE": "Edit Role", "DELETE_GROUPS_ROLE": "Delete Group", "BULK_DELETE_USERS_GROUPS_ROLE": "Bulk Delete Users and Groups", "CONFIGURATION_TYPE": "Configuration Type", "RECOMMENDED": "Recommended", "DEFAULT": "<PERSON><PERSON><PERSON>", "CUSTOM": "Custom", "PASSWORD_COMPLEXITY": "Password Complexity", "PASSWORD_LENGTH": "Password Length", "MIN_LWR_CASE": "Minimum No. of Lowercase Letters", "MIN_UPPR_CASE": "Minimum No. of Uppercase Letters", "MIN_NUMERIC": "Minimum No. of Numeric Characters", "MIN_SPL_CHAR": "Minimum No. of Special Characters", "PASSWORD_DOES_NOT_INCLUDE": "Password must not include company name, username, first name, or last name", "REJECT_REUSE": "Reject reuse of last 5 passwords", "DEACTIVATE_USER": "Deactivate user after 10 unsuccessful attempts", "ALLOW_ADMIN_SET_PASSWORD": "Allow administrator to create or change user's password", "FORCE_PASSWORD_CHANGE": "Enforce password change after the initial login", "PASSWORD_EXPIRY": "Password expiration period (in days)", "PASSWORD_CRITERIA": "Password Criteria", "POLICY": "Policy", "POLICIES": "Policies", "AUTHENTICATION_POLICY": "Authentication Policy", "PASSWORD_POLICY": "Password Policy", "LOAD_MORE": "Load More", "EDIT_EMAIL": "Edit Email Address", "UPDATE_EMAIL": "Update Email Address", "NEW_EMAIL": "Enter New Email Address", "VALIDATE_EMAIL": "Validate Email", "VALIDATION_CODE": "Enter Validation Code", "RESEND_CODE": "Resend Code", "CURRENT_PASSWORD": "Current Password", "NEW_PASSWORD": "New Password", "CONFIRM_NEW_PASSWORD": "Confirm New Password", "MIN_LENGTH_REQUIRED": "{{value}} characters minimum", "MIN_LOWER_CASE_REQUIRED": "At least {{value}} lowercase character (a-z)", "MIN_UPPER_CASE_REQUIRED": "At least {{value}} uppercase character (A-Z)", "MIN_NUMERIC_REQUIRED": "At least {{value}} numeric character (0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "At least {{value}} special character", "SIGN_ON_POLICIES": "Admin Sign-On Policy", "ADD_RULE": "Add Rule", "RULE_ORDER": "Rule Order", "RULE_STATUS": "Rule Status", "RULE_NAME": "Rule Name", "RULE_ACTION": "Rule Action", "CRITERIA": "Criteria", "ADD_SIGN_ON_POLICY": "Add Admin Sign-On Policy", "EDIT_SIGN_ON_POLICY": "Edit Admin Sign-On Policy", "DELETE_SIGN_ON_POLICY": "Delete Admin Sign-on Policy", "LOCATION": "Location", "LOCATION_GROUP": "Location Group", "ALLOW": "Allow", "DENY": "<PERSON><PERSON>", "OPERATIONS": "Operations", "REMOVE": "Remove", "NO_DATA_FOUND": "No Data found", "NO_ITEMS_FOUND": "No items found", "BROWSE_FILE": "Browse File", "IMPORT": "Import", "NO_FILE_CHOSEN": "No File Chosen", "OVERRIDE_EXISTING_ENTRIES": "Override Existing Entries", "CSV_FILE": "CSV File", "SAMPLE_CSV_DOWNLOAD": "Download Sample", "IMPORT_USERS": "Import Users", "IMPORT_GROUPS": "Import Groups", "PRIMARY_IDENTITY_PROVIDER": "Primary Identity Provider", "SECONDARY_IDENTITY_PROVIDER": "Secondary Identity Providers", "ADD_PRIMARY_IDP": "Add Primary IdP", "ADD_SECONDARY_IDP": "Add Secondary IdP", "ADD_PRIMARY_IDENTITY_PROVIDER": "Add Primary Identity Provider", "EDIT_PRIMARY_IDENTITY_PROVIDER": "Edit Primary Identity Provider", "DELETE_PRIMARY_IDENTITY_PROVIDER": "Delete Primary Identity Provider", "ADD_SECONDARY_IDENTITY_PROVIDER": "Add Secondary Identity Provider", "EDIT_SECONDARY_IDENTITY_PROVIDER": "Edit Secondary Identity Provider", "DELETE_SECONDARY_IDENTITY_PROVIDER": "Delete Secondary Identity Provider", "SAML_CONFIGURATION": "SAML CONFIGURATION", "OIDC_CONFIGURATION": "OIDC CONFIGURATION", "IDENTITY_VENDOR": "Identity Vendor", "DOMAIN": "Domain", "YES": "YES", "NO": "No", "ADD_LOCATION": "Add Location", "EDIT_LOCATION": "Edit Location", "DELETE_LOCATION": "Delete Location", "IMPORT_LOCATION": "Import Location", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "Are you sure you want to delete this location? The changes cannot be undone.", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "Are you sure you want to bulk delete these locations? The changes cannot be undone.", "IP_ADDRESS": "IP Address", "COUNTRY": "Country", "LOCATION_INFORMATION": "Location Information", "NAME_REQUIRED_MESSAGE": "Name is required", "LOCATIONS_REQUIRED_MESSAGE": "Locations is required", "COUNTRY_REQUIRED_MESSAGE": "Country is required", "IP_ADDRESS_REQUIRED_MESSAGE": "IP Address is required", "LOCATION_COUNT": "Location Count", "LOCATIONS": "Locations", "UNSELECTED_LOCATIONS": "Unselected Locations", "SELECTED_LOCATIONS": "Selected Locations", "ADD_LOCATION_GROUP": "Add Location Group", "EDIT_LOCATION_GROUP": "Edit Location Group", "DELETE_LOCATION_GROUP": "Delete Location Group", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Are you sure you want to delete this location group? The changes cannot be undone.", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Are you sure you want to bulk delete these location groups? The changes cannot be undone.", "IMPORT_LOCATION_GROUP": "Import Location Group", "PROTOCOL": "Protocol", "AUTHENTICATION_REQUEST": "Authentication Request", "SAML_REQUEST_SIGNING": "SAML Request Signing", "LOGIN_HINT": "<PERSON><PERSON>", "SIGNING_ALGORITHM": "Signing Algorithm", "REQUEST_SIGNING_CERTIFICATE": "Request Signing Certificate", "SP_SAML_CERTIFICATE": "SP SAML Certificate", "DOWNLOAD_CERTIFICATE": "Download Certificate", "ENCRYPTED_SAML_RESPONSE": "Encrypted SAML Response", "ENCRYPTED_SAML_ASSERTION": "Encrypted SAML Assertion", "CERTIFICATE": "Certificate", "SAML_ENCRYPTION_CERTIFICATE": "SAML Encryption Certificate", "IDP_METADATA": "IdP <PERSON>", "IDP_METADATA_URL": "IdP Metadata URL", "METADATA_URL": "Metadata URL", "IDP_CERTIFICATE": "IdP Certificate", "IDP_ENTITY_URI": "IdP Entity URI", "PRIMARY_IDP_NOT_AVAILABLE": "Primary Identity Provider is not added.", "SECONDARY_IDP_NOT_AVAILABLE": "Secondary Identity Provider Not Available", "SP_METADATA": "SP Metadata", "DOWNLOAD_SP_METADATA": "Download SP Metadata", "IDP_SINGLE_SIGNON_URL": "IdP Single Sign-On URL", "REDIRECT_URI": "Redirect URI", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "Token Endpoint Authentication Method", "ISSUER": "Issuer", "ZDK_INSTANCE": "ZDK Instance", "APPLICATION": "Application", "ADD_CLAIM": "<PERSON><PERSON>", "VALUE": "Value", "CLAIM_REQUIREMENTS": "Claim Requirements", "SIGNATURE_VALIDATION": "Signature Validation", "EXPIRY": "Expiry", "AUTHORIZATION_ENDPOINT": "Authorization Endpoint", "TOKEN_ENDPOINT": "Token Endpoint", "JWKS_ENDPOINT": "JWKS Endpoint", "USER_INFORMATION_ENDPOINT": "User Information Endpoint", "CLIENT_SECRET_BASIC": "Client Secret Basic", "CLIENT_SECRET_POST": "Client Secret Post", "CLIENT_ID": "Client ID", "CLIENT_SECRET": "Client Secret", "REQUESTED_SCOPES": "Requested <PERSON><PERSON><PERSON>", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "<PERSON><PERSON><PERSON> Requested Authentication Context (Optional)", "SP_ENTITY_ID": "SP Entity ID", "SP_URL": "SP URL", "SCIM_PROVISIONING_STATUS": "SCIM Provisioning Status", "PROVISIONING_SETTINGS": "Provisioning Settings", "JIT_PROVISIONING": "Just-in-time (JIT) Provisioning", "ENABLE_FOR_JIT_PROVISIONING": "Enable for JIT provisioning", "JIT_ATTRIBUTE_MAPPING": "JIT Attribute Mapping", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "JUST-IN-TIME Attribute Mapping", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "Just-in-time User Group Attribute", "JUST_IN_TIME_ATTRIBUTE": "Just-in-time Attribute", "SAML_ATTRIBUTE_MAPPING": "SAML Attribute Mapping", "USER_GROUP_SAML_ATTRIBUTE": "User Group SAML Attribute", "SAML_ATTRIBUTE": "SAML Attribute", "USER_ATTRIBUTE": "User Attribute", "SCIM_PROVISIONING": "SCIM Provisioning", "SCIM_ATTRIBUTE_MAPPING": "SCIM Attribute Mapping", "SCIM_ATTRIBUTE": "SCIM Attribute", "SCIM_ENDPOINT_URL": "SCIM Endpoint URL", "SESSION_ATTRIBUTE_MAPPING": "Session Attribute Mapping", "IDP_ATTRIBUTE": "IdP Attribute", "AUTHENTICATION_METHOD": "Authentication Method", "TOKEN": "<PERSON><PERSON>", "GENERATE_TOKEN": "Generate Token", "INPUT_METHOD": "Input Method", "FETCH_WITH_URL": "Metadata URL", "UPLOAD_METADATA": "Upload Metadata", "MANUAL_ENTRY": "Enter Manually", "UPLOAD_CERTIFICATE": "Upload Certificate", "BEARER_TOKEN": "<PERSON><PERSON>", "IDP_ADVANCED_SETTINGS": "IdP Advanced Settings", "SESSION_TIMEOUT": "Session Timeout", "SESSION_TIMEOUT_DURATION_IN_MIN": "Session Timeout Duration (in Minutes)", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "Administrator Inactivity Timeout Duration (in Minutes)", "SERVICE_ENTITLEMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "Service Entitlement Session Timeout (in Minutes)", "NONE": "None", "TIME_RANGE": "Time Range", "ACTION": "Action", "CATEGORY": "Category", "SUB_CATEGORY": "Sub-Category", "INTERFACE": "Interface", "RESULT": "Result", "SCOPE": "<PERSON><PERSON>", "SCOPES_N_ROLES": "Scopes and Roles", "ADD_ITEMS": "Add Items", "TIME_STAMP": "Time stamp", "RESOURCE": "Resource", "ADMIN_ID": "Admin ID", "CLIENT_IP": "Client IP", "AM": "AM", "TIME": "Time", "TIME_PM": "PM", "START_TIME": "Start Time", "END_TIME": "END TIME", "CURRENT_DAY": "Today", "CURRENT_WEEK": "Current Week", "CURRENT_MONTH": "Current Month", "PREVIOUS_DAY": "Previous Day", "PREVIOUS_WEEK": "Previous Week", "PREVIOUS_MONTH": "Previous Month", "HELP_BROWSER": "Help", "SELECT_DATE": "Select Date", "CLOSE": "Close", "ACCEPT": "Accept", "SYNC_DOMAINS": "Sync Domains", "IMPORT_RESULTS": "Import Results", "COMPLETE": "Complete", "PROCESSED_RECORD": "Processed Records", "TOTAL_RECORDS_ADDED": "Total Records Added", "TOTAL_RECORDS_DELETED": "Total Records Deleted", "TOTAL_RECORDS_IN_IMPORT": "Total Records In Import", "TOTAL_RECORDS_UPDATED": "Total Records Updated", "FAILED_RECORDS": "Failed Records", "DUPLICATE_ITEM": "The given name is already in use", "RESOURCE_NOT_FOUND": "Resource not found", "CSV_FORMAT_INVALID": "Invalid CSV Format", "UNEXPECTED_ERROR": "Unexpected Error", "DUPLICATE_RECORD": "Duplicate Record", "CONFIGURATION_CHANGES": "Configuration Changes", "PRE_CHANGES_CONFIGURATION": "Pre-Changes Configuration", "POST_CHANGES_CONFIGURATION": "Post-Changes Configuration", "VIEW_CHANGES": "View Changes", "OPEN_HELP_BROWSER": "Open Help Browser", "ALL": "All", "SIGN_IN": "Sign In", "SIGN_OUT": "Sign Out", "CREATE": "Create", "GET": "Get", "SEARCH": "Search", "BULK": "Bulk", "DOWNLOAD": "Download", "UPLOAD": "Upload", "USER_MANAGEMENT": "User Management", "USER_MANAGEMENT_FAILURE": "User Management Failure", "LOGIN": "<PERSON><PERSON>", "AUTHENTICATION_SETTINGS": "Authentication Settings", "TENANTS": "Tenants", "USER": "User", "CUSTOM_USER_ATTRIBUTE": "Custom User Attribute", "PASSWORD_CHANGE": "Password Change", "PASSWORD_POLICY_CHANGE": "Password Policy Change", "IDENTITY_PROVIDERS": "Identity Providers", "ADVANCE_SETTINGS": "Advance Settings", "ADMIN_SIGN_ON": "Admin Sign-On", "SIGN_ON_POLICY": "Sign-On Policy", "SERVICE_ASSIGNMENT": "Service Assignment", "SERVICES": "Services", "SCIM_API": "SCIM API", "FAILURE": "Failure", "PARTIALLY_FAILED": "Partially Failed", "REMOVE_PAGE": "Remo<PERSON>", "REMOVE_ALL": "Remove All", "CONFIRMATION_REMOVE_PAGE": "Remo<PERSON>", "CONFIRMATION_REMOVE_ALL": "Remove All", "CONFIRM": "Confirm", "CUSTOMISE_COLUMNS": "CUSTOMISE COLUMNS", "ALL_CHANGES_SAVED": "All changes have been saved", "ITEM_HAS_BEEN_DELETED": "The Item has been deleted", "ITEMS_HAVE_BEEN_DELETED": "Items have been deleted", "START_TIME_BEFORE_END_TIME": "Start Time should be before End Time", "USER_GROUPS_ASSIGNMENT_LIMIT": "Can't assign more that {{value}} groups in one go", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "Can't assign more that {{value}} roles to users/groups in one go", "VERIFY_EMAIL_ADDRESS": "Verify Em<PERSON> Address", "ENTER_EMAIL_OTP": "Enter Email OTP", "SET_UP": "Set Up", "EMAIL_OTP": "Email OTP", "NO_MATCHING_ITEMS_FOUND": "No Matching items found", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "Bear<PERSON> generated successfully", "MSG_EMAIL_OTP_SENT": "Email OTP sent to {{email}}", "MSG_INVALID_OTP": "Incorrect OTP Entered", "DIDNT_RECEIVE": "Didn't receive?", "SELECTED_CRITERIA_INVALID": "Selected criteria is invalid", "ALLOW_FIDO_AS_PRIMARY": "Allow FIDO2 as Primary Authenticator", "ALLOW_EMAIL_OTP_AS_PRIMARY": "Allow Em<PERSON> as Primary Authenticator", "AUTHENTICATORS": "Authenticators", "MFA_AUTHENTICATOR": "MFA Authenticator", "LAST_USED": "Last Used", "DELETE_AUTHENTICATOR": "Delete MFA Authenticator", "RESTART_TENANT_CREATION": "Restart Tenant Creation", "CONTINUE": "Continue", "PARTIAL_TENANT_MSG": "Your Zscaler tenant setup is partially complete.", "CLICK_CONTINUE_MSG": "Click on \"Continue\" to complete the setup.", "DISABLE_MULTIFACTOR": "Disable Multi-Factor Authentication", "DISABLE_FIDO": "Disable FIDO2", "DISABLE_FIDO_MSG": "You might lock yourself out of the account if you are only using a security key or biometric for authentication. The only way to access your account is through password-based authentication if you disable M<PERSON>. In case you don’t remember your password or haven’t configured it yet, request a password reset.", "DISABLE_MFA_MSG": "Disabling Multi-Factor Authentication is a global change. This makes your users less secure. Are you sure you want to proceed?", "ADMINISTRATIVE": "Administrative", "SERVICE_ENTITLEMENTS": "Service Entitlements", "ADMINISTRATIVE_ENTITLEMENTS": "Administrative Entitlements", "ORGANIZATION_ID": "Org ID", "GROUPS": "Groups", "ASSIGN_USER": "Assign User", "SELECT_USERS_AND_ROLE": "Select Users & Roles", "SUMMARY": "Summary", "ASSIGN": "Assign", "SELECT_GROUPS_AND_ROLE": "Select Groups & Roles", "SERVICE_NAME": "Service Name", "LOGIN_NAME": "Login Name", "ADD_ROLE": "Add Role", "EDIT_ROLE": "Edit Role", "USERS_AND_GROUPS_TEXT": "Users & Groups", "USERS_CREDENTIALS": "Users Credentials", "AUTHENTICATION_SESSION": "Authentication Session", "AUTHENTICATION_SESSION_FOR_SERVICE_ENTITLEMENT": "Authentication Session for Service Entitlement", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "Force Authentication for Private Access Reauthentication", "ZSLOGIN_AS_IDENTITY_PROVIDER": "<PERSON><PERSON><PERSON>in as Identity Provider", "AZURE_AD_AS_TENANT": "Azure AD tenant A (OIDC)", "OKTA_TENANT": "Okta tenant B (SAML)", "PING_IDENTITY": "Ping Identity tenant C (SAML)", "DOMAINS": "Domains", "SERVICE_ENTITLEMENTS_TEXT": "Service Entitlements", "AUTHENTICATION_EVENT_LOG": "Authentication Event Log", "DELETE_ROLE": "Delete Role", "ENTITLEMENTS": "Entitlements", "ENTITLEMENTS_LABEL": "Entitlements", "USER_ID": "User ID", "REMOVE_ASSIGNMENTS": "Remove Assignments", "SELECT_USERS": "Select Users", "ASSIGN_GROUP": "Assign Group", "SELECT_GROUPS": "Select Groups", "SAME_ROLE_FOR_SELECTED_USERS": "Set same role for all selected users", "SAME_ROLE_FOR_SELECTED_GROUPS": "Set same role for all selected groups", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "Set same role and microtenant for all selected users", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "Set same role and microtenant for all selected groups", "ATTRIBUTE_ALREADY_MAPPED": "Attribute {{value}} already mapped", "MFA_TOGGLE_TOOLTIP": "Enabling Multi-Factor Authentication allows users to select a secondary authentication factor along with passwords.", "FIDO_TOGGLE_TOOLTIP": "Enabling FIDO2 as primary allows the users to skip passwords and use a biometric or security key as an authentication method.", "LINKED_TENANTS": "Linked Services", "SELECT_ROLE": "Select Role", "SELECT_SCOPE": "Select Microtenant", "ALL_USERS": "All Users", "ASSIGNED_USERS": "Assigned Users", "UNSELECTED_ROLES": "Unselected Roles", "SELECTED_ROLES": "Selected Roles", "MANAGE": "Manage", "EDIT_NAME": "Edit Name", "VIEW_ROLES": "View Roles", "DEVICE_GROUP_RESTRICTIONS": "Device Group Restrictions", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "Manage Device Group Restrictions", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "Enable Device Group Restrictions", "DEVICE_GROUPS": "Device Groups", "ORGANIZATION_NAME": "Org Name", "DEVICE_GROUP_ASSIGNMENT": "Device Group Assignment", "SERVICE_RUNTIME_ASSIGNMENT": "Service Runtime Assignment", "ROLE_ASSIGNMENT": "Role Assignment", "DELETE_ASSIGNMENT": "Delete Assignment", "NO_RECORD_EXISTS": "No record Exists", "SERVICE": "Service", "REMOTE_ASSISTANCE": "Remote Assistance", "CXO_INSIGHT": "Executive Insights", "VIEW": "View", "ACCESS_TYPE": "Access Type", "FULL_ACCESS": "Full Access", "ENABLE_FULL_ACCESS": "Enable Full Access", "ACCESS_VALID_UNTIL": "Access Valid Until", "VIEW_ONLY_ACCESS": "View Only Access", "ENABLE_VIEW_ONLY_ACCESS": "Enable View Only Access", "DEVICE_TOKEN": "<PERSON><PERSON>", "TOKEN_VALIDATORS": "Token Validators", "TOKEN_VALIDATOR": "Token Validator", "ADD_TOKEN_VALIDATOR": "Add Token Validator", "EDIT_TOKEN_VALIDATOR": "<PERSON>", "DELETE_TOKEN_VALIDATOR": "Delete Token Validator", "AUDIENCE": "Audience", "SUBJECT_CLAIM": "Subject Claim", "CLIENT_JWKS": "Client JWKs", "CERTIFICATES_PUBLIC_KEYS": "Certificates and Public Keys", "CLIENT_JWKS_URL": "Client JWKs URL", "VALIDATION_TYPE": "Validation Type", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Client Connector Device Token", "GUEST_DOMAINS": "Guest Domains", "ARBITRARY_GUEST_DOMAINS": "Arbitrary Guest Domains", "GUEST_USER": "Guest User", "GUEST_DOMAIN": "Guest Domain", "ADD": "Add", "DEPARTMENT": "Department", "ENABLE_TO_ALLOW_ALL_DOMAINS": "Enable to allow all the domains", "CUSTOM_ATTRIBUTE": "Custom Attribute", "BRANDING": "Branding", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "Customize Email Address Sent By", "CONFIGURATION_SETTINGS": "Configuration Settings", "CUSTOMIZE_EMAIL_SUBJECT": "Customize Email Subject", "EMAIL_FROM_ADDRESS": "Email From Address", "EMAIL_SUBJECT": "Email Subject", "EDIT_LOGO": "Edit <PERSON>", "LOGO": "Logo", "UPLOAD_LOGO": "Upload Logo", "INVALID_IMAGE_TYPE": "Invalid Image Type", "BASE_64_CONVERSION_FAILED": "Unable to convert image to base 64 encoded version", "DEPARTMENTS": "Departments", "ADD_DEPARTMENT": "Add Department", "EDIT_DEPARTMENT": "Edit Department", "DELETE_DEPARTMENT": "Delete Department", "IMPORT_DEPARTMENT": "Import Department", "DEPARTMENT_NAME": "Department Name", "VIEW_USER": "View User", "VIEW_GROUP": "View User Group", "DASHBOARD": "Dashboard", "CREATED": "Created", "UPDATED": "Updated", "DELETED": "Deleted", "EXTERNAL_IDP": "External IdP", "HOSTED": "Hosted", "TOTAL": "Total", "CSV": "Csv", "MANUAL": "Manual", "EMAIL_LOGIN": "Email OTP", "FIDO_LOGIN": "FIDO Authentication", "PWD_LOGIN": "Password", "TOTP_LOGIN": "TOTP Login", "SMS_LOGIN": "SMS Login", "SAML_LOGIN": "SAML", "OIDC_LOGIN": "OIDC", "JIT": "jit", "SCIM": "scim", "ADMIN_ASSIGNMENT": "Admin Assignment", "RUNTIME_ASSIGNMENT": "Runtime Assignment", "ZCC_ASSIGNMENT": "ZCC Assignment", "ONE_DAY": "1 Day", "TWO_DAYS": "2 Days", "SEVEN_DAYS": "7 Days", "FOURTEEN_DAYS": "14 Days", "THIRTY_DAYS": "30 Days", "SIXTY_DAYS": "60 Days", "NINTY_DAYS": "90 Days", "NO_DATA_AVAILABLE": "No Data Available", "ZSLOGIN_FOR_ADMINISTRATORS": "ZSLogin for Administrators", "ZIDENTITY_FOR_ADMINISTRATORS": "ZIdentity for Administrators", "REVERT_TO_PROVISIONED": "Revert to Provisioned", "REVERT": "<PERSON><PERSON>", "ZSLOGIN_MIGRATION_STATUS": "ZSLogin Migration Status", "ZIDENTITY_MIGRATION_STATUS": "ZIdentity Migration Status", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "Verification of External Identity Provider Configuration Unsuccessful", "UPDATE_PSEUDO_DOMAIN": "Update Pseudo Domain", "CURRENT_PSEUDO_DOMAIN_NAME": "Current Pseudo Domain Name", "NEW_PSEUDO_DOMAIN_NAME": "New Pseudo Domain Name", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "Update Pseudo Domain Success !!", "MULTIFACTOR_BYPASS": "Multi-factor Bypass", "SKIP_SECOND_FACTOR_AUTHENTICATION": "Skip Second Factor Authentication", "SKIP_UNTIL": "<PERSON><PERSON>", "INTEGRATION": "Integration", "API_CLIENTS": "API Clients", "API_RESOURCES": "API Resources", "ADD_API_CLIENT": "Add API Client", "ADD_API_RESOURCE": "Add API Resource", "EDIT_API_RESOURCE": "Edit API Resource", "API_RESOURCE": "API Resource", "ACCESS_TOKEN_VALIDITY": "Access Token Validity", "MINUTES": "Minutes", "HOURS": "Hours", "PRIVATE_KEY_JWT": "Private Key JWT", "CLIENT_JWK_URL": "Client JWKs URL", "CERTIFICATE_PUBLIC_KEY": "Certificates/Public Keys", "CLIENT_JWK": "Client JWK", "CLIENT_SECRETS": "Client Secrets", "CLIENT_AUTHENTICATION": "Client Authentication", "CLIENT_INFORMATION": "Client Information", "ZSCALER_API": "ZSCALER APIs", "RESOURCE_NAME": "Resource Name", "NEVER": "Never", "CERTIFICATES_AND_PUBLIC_KEYS": "Certificates and Public Keys", "DELETE_API_CLIENT": "Delete API Client", "DELETE_API_CLIENT_MESSAGE": "Are you sure you want to delete this API Client? The changes cannot be undone.", "ACCESS_TOKEN_REQUIRED_MESSAGE": "Access Token Lifetime is required.", "EXPIRES": "Expires", "SECRET": "Secret", "EDIT_API_CLIENT": "Edit API Client", "VIEW_API_CLIENT": "View API Client", "CREATED_ON": "Create On", "EXPIRES_ON": "Expires On", "API_CLIENT": "API Client", "CLIENT_SECRET_WARNING": "Copy Client Secret. It won't be displayed later.", "API_CLIENTS_AND_RESOURCES": "API Clients & Resources", "LOGIN_ID_ATTRIBUTE": "Login ID Attribute", "LOGOUT": "Logout", "ENVIRONMENT": "Environment", "SYSTEM": "System", "ZIDENTITY_ROLES": "ZIdentity Roles", "CLIENT": "Client", "RESOURCES": "Resources", "BASIC": "Basic", "ADVANCED": "Advanced", "PROVISIONING": "Provisioning", "ZIDENTITY_DASHBOARD": "ZIdentity Dashboard", "AUTHENTICATION_LEVELS": "Authentication Levels", "LEVEL_NAME": "Level Name", "VALIDITY": "Validity", "DURATION": "Duration", "MESSAGE_TO_USER": "Message to the user (optional)", "PARENT": "Parent", "SUB_LEVEL_NAME": "Sub-Level Name", "DAYS": "Days", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "Levels to Authentication context mapping", "LEVELS": "Levels", "AUTHENTICATION_CONTEXT": "Authentication Context", "DELETE_AL": "Delete Authentication Level", "DELETE_AL_CONFIRM_MSG": "This action is irreversible and will completely delete the authentication level. Are you sure you want to proceed?", "MOVE": "Move", "TOKENS": "Tokens", "ZIDENTITY_ISSUED_TOKENS": "ZIdentity Issued Tokens", "JTI": "JTI", "JWT": "JWT", "ISSUED_AT": "Issued At", "EXPIRES_AT": "Expires At", "EXPIRED": "Expired", "REVOKED": "Revoked", "VIEW_JWT": "View JWT", "REVOKE": "Revoke", "VIEW_TOKENS": "View Tokens", "REVOKE_TOKEN": "Revoke Token", "REVOKE_TOKEN_MESSAGE": "Are you sure you want to revoke this Token? The changes cannot be undone.", "LAST_FIFTEEN_MINUTES": "Last 15 Minutes", "LAST_THIRTY_MINUTES": "Last 30 Minutes", "LAST_ONE_HOUR": "Last 1 Hour", "LAST_TWENTY_FOUR_HOURS": "Last 24 Hours", "LAST_FORTY_EIGHT_HOURS": "Last 48 Hours", "NEXT_FIFTEEN_MINUTES": "Next 15 Minutes", "NEXT_THIRTY_MINUTES": "Next 30 Minutes", "NEXT_ONE_HOUR": "Next 1 Hour", "NEXT_TWENTY_FOUR_HOURS": "Next 24 Hours", "TODAY": "Today", "REMOVE_PASSWORD": "Remove Password", "AUDITOR_OVERRIDE": "Auditor Override", "OVERRIDE": "Override", "TENANT_ID": "Tenant ID", "ZAA_PROFILES": "Adaptive Access Profiles", "ZAA_CONTEXT_NUGGET_OVERRIDES": "Adaptive Access Overrides", "ZAA_INTEGRATIONS": "Adaptive Access Integrations", "ZAA_SIGNAL_HISTORY": "Adaptive Access Signal History", "API_CLIENT_ACCESS_POLICY": "Api Client Access Policy", "POLICY_RULE": "Policy Rule", "VIEW_API_CLIENT_POLICY": "View API Client's Policy", "ADD_POLICY_RULE": "Add Policy Rule", "EDIT_POLICY_RULE": "Edit Policy Rule", "DELETE_POLICY_RULE": "Delete Policy Rule", "ASSIGNED_API_CLIENTS": "Assigned API Clients", "CRITERIA_IF": "Criteria (If)", "RULE_ACTION_THEN": "Rule Action (Then)", "BLOCK": "Block", "THEN": "Then", "DEFINE_CRITERIA": "Define Criteria", "ASSIGN_API_CLIENTS": "Assign API Clients", "BASIC_INFORMATION": "Basic Information", "IF": "If", "OATH2_CLIENT": "OAuth2 Client", "ACCESS_POLICY": "Access Policy", "ZSDK_CONFIG": "ZSDK Configuration", "USER_AUTHENTICATOR": "User Authenticator", "VIEW_REMOTE_ASSISTANCE_DETAILS": "View Remote Assistance Details", "SSO_REMOTE_ASSISTANCE_DETAILS": "SSO Remote Assistance Details", "ZIA_AUTO_SERVICE_UPDATE": "ZIA Auto Service Update", "TEST_TOKEN": "Test Token", "HEADER": "Header", "PAYLOAD": "Payload", "SIGNATURE": "Signature", "CLAIM": "<PERSON><PERSON><PERSON>", "ISS_CLIAM_REQUIRED": "ISS Claim is required", "MATCHING_TOKEN_VALIDATOR": "Matching Token Validator", "ISS_CLIAM_IS_ALREADY_CONFIGURED": "ISS Claim is already configured", "DUPLICATE_CLAIM_CANNOT_BE_CONFIGURED": "Duplicate claim cannot be configured", "VALUE_CANNOT_BE_EMPTY": "Value cannot be empty", "CLAIM_CANNOT_BE_EMPTY": "Claim cannot be empty", "SAML_PROTOCOL_LABEL": "SAML (Limited Features)", "OIDC_PROTOCOL_LABEL": "OIDC (Recommended)", "UNSELECTED_SCOPES": "Unselected Scopes", "SELECTED_SCOPES": "Selected Scopes", "MICROTENANT": "Microtenant", "COMPLETE_MIGRATION": "Complete Migration", "SETTINGS": "Settings", "CONFIG_TAKES_EFFECT_FROM_NEXT_LOGIN": "The updated settings will take effect for all admins after their next login."}