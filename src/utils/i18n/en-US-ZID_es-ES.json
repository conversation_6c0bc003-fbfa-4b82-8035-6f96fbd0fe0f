{"LOCALE": "en-US", "EMAIL_ADDRESS": "Dirección de correo electrónico", "EMAIL_PLACEHOLDER": "Introduzca su dirección de correo electrónico...", "LOGIN_ID_PLACEHOLDER": "Introduzca su ID de inicio de sesión...", "LOGIN_ID_LABEL": "Identificador de inicio de sesión", "LOGIN_PASSWORD_PLACEHOLDER": "Introduzca su contraseña...", "PASSWORD_LABEL": "contraseña", "NEW_PASSWORD_LABEL": "Nueva contraseña", "OLD_PASSWORD_LABEL": "Contraseña antigua", "CONFIRM_NEW_PASSWORD_LABEL": "Confirmar nueva contraseña", "PASSWORD_PLACEHOLDER": "Introduzca la contraseña", "FORGOT_PASSWORD": "¿Ha olvidado la contraseña?", "REMEMBER_ME": "Recordarme", "RESET_PASSWORD": "Restablecer contraseña", "CHANGE_PASSWORD": "Cambiar contraseña", "SUCCESS": "Correcto", "RESEND": "Volver a enviar", "SIGN_IN_LABEL": "In<PERSON><PERSON>", "INITIAL_DOMAIN_NAME": "Nombre de dominio inicial", "FIRST_NAME": "Nombre", "LAST_NAME": "<PERSON><PERSON><PERSON><PERSON>(s)", "PHONE_NUMBER": "Número de teléfono", "REQUIRED_VALIDATION_MESSAGE": "Requerido", "INVALID_CREDENTIALS_MESSAGE": "ID o contraseña de inicio de sesión no válidos", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "Un auditor no puede hacer login desde aqui", "LANGUAGE": "<PERSON><PERSON><PERSON><PERSON> (EE. UU.)", "COOKIES_DISABLED": "Deben permitirse las cookies para utilizar esta aplicación. Active la compatibilidad con cookies de su navegador para este sitio.", "COOKIES_NOT_ALLOWED": "No se permiten cookies", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "Este navegador no es compatible y puede interrumpir la funcionalidad de este sitio. Le sugerimos que actualice su navegador a la última versión. Para ignorar este mensaje, haga clic en Aceptar.", "BROWSER_NOT_SUPPORTED": "No se admite la versión del navegador", "OK": "Aceptar", "NEXT": "Siguient<PERSON>", "BACK": "Volver", "SAVE": "Guardar", "SAVE_AND_NEXT": "Guardar y siguiente", "COPYRIGHT": "Copyright", "COPYRIGHT_STATEMENT": "Todos los derechos reservados.", "POWERED_BY": "Con tecnología de", "ADMIN_PORTAL_SIGN_IN": "Inicio de sesión en el portal de administración", "ALL_ORGANIZATIONS": "Todas las organizaciones", "SEARCH_PLACEHOLDER": "Buscar...", "GENERAL": "General", "ZPA": "Zscaler Private Access", "IAM": "Gestión de identidades y accesos", "ZIA": "Zscaler Internet Access", "ZDX": "Zscaler Digital Experience", "EC": "Cloud & Branch Connector", "CCP": "Portal de conector de cliente", "ZBI": "Aislamiento del navegador", "BI": "Business Insights", "ZRA": "Risk360", "LAUNCH": "Iniciar", "VERIFY": "Verify", "CANCEL": "<PERSON><PERSON><PERSON>", "DONE": "<PERSON><PERSON>", "RESET": "Restablecer", "CLEAR": "Bo<PERSON>r", "CLEAR_ALL": "<PERSON><PERSON><PERSON> todo", "MY_PROFILE": "Mi perfil", "ZS_LOGIN": "ZSL<PERSON>in", "Z_IDENTITY": "ZIdentity", "ACCOUNT_MANAGEMENT": "Gestión de cuentas", "CLOUD_CONFIGURATION": "Configuración de nube", "PRODUCT": "Producto", "CLOUD_NAME": "Nombre de la nube", "CLOUD_ID": "ID de nube", "ORG_NAME": "Nombre de la organización", "LINKED_SERVICES": "<PERSON><PERSON><PERSON>", "LINK": "Enlace", "UNLINK": "Desvin<PERSON>", "LINKED": "Vinculado", "UNLINKED": "No vinculado", "LINK_TENANT": "Vincular inquilino", "UNLINK_TENANT": "Desvincular inquilino", "ATTRIBUTE": "Atributo", "ATTRIBUTES": "Atributos", "USER_ATTRIBUTES": "Atributos de usuario", "SESSION_ATTRIBUTE": "Atributo de sesión", "ADVANCED_SETTINGS": "<PERSON><PERSON><PERSON><PERSON>", "DIRECTORY": "Directorio", "USERS": "Usuarios", "USER_GROUPS": "Grupos de usuarios", "EXTERNAL_IDENTITIES": "Identidades externas", "APPLICATIONS": "Aplicaciones", "ZSCALER_SERVICES": "<PERSON><PERSON><PERSON>", "SECURITY": "Seguridad", "IP_LOCATIONS": "Ubicaciones de IP", "IP_LOCATIONS_AND_GROUPS": "Ubicaciones y grupos de IP", "IP_LOCATION_GROUPS": "Grupos de ubicaciones de IP", "ADMINISTRATION": "Administración", "ADMINISTRATION_CONTROLS": "Controles de administración", "AUTHENTICATION": "Autenticación", "AUTHENTICATION_METHODS": "Métodos de autenticación", "ENABLE_MULTIFACTOR_SETTINGS": "Activar configuración multifactor", "ENABLE_MULTIFACTOR_AUTH_USER": "Activar autenticación multifactor (MFA) para inscripción en servicios", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "Activar autenticación multifactor (MFA) para administradores", "MFA_ENROLLMENT_GRACE_PERIOD": "Período de gracia de inscripción en MFA (en días)", "AUDIT_LOGS": "Registro de auditoría", "ADD_USER": "<PERSON><PERSON><PERSON>", "IMPORT_CSV": "Importar CSV", "ACTIONS": "Acciones", "ACTIVATE": "Activar", "DE_ACTIVATE": "Desactivar", "BULK_ACTIVATE": "Activar por lotes", "BULK_DEACTIVATE": "Desactivar por lotes", "BULK_DELETE": "Eliminar por lotes", "DELETE": "Eliminar", "NAME": "Nombre", "GROUP": "Grupo", "SELECT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LOGIN_ID": "Identificador de inicio de sesión", "STATUS": "Estado", "GENERAL_INFORMATION": "Información general", "MY_ACCOUNT": "Mi cuenta", "FULL_NAME": "Nombre completo", "VERIFIED": "Verificado", "LANGUAGE_LABEL": "Idioma", "TIMEZONE": "Zona horaria", "ACTIVE": "Activo", "INACTIVE": "Inactivo", "USER_INFORMATION": "Información de usuario", "PRIMARY_EMAIL": "Correo electrónico principal", "SAME_AS_LOGIN_ID": "Igual que el ID de inicio de sesión", "SECONDARY_EMAIL": "Correo electrónico secundario", "ADDITIONAL_ATTRIBUTES": "Atributos adicionales", "SECURITY_SETTINGS": "Configuración de seguridad", "PASSWORD_OPTION": "Opción de contraseña", "PROMPT_PASSWORD_FIRST_LOGIN": "Solicitar el cambio de contraseña después del inicio de sesión inicial", "CONFIRM_PASSWORD": "Confirmar con<PERSON>", "ASSIGNMENT": "Asignación", "ASSIGN_GROUPS": "Asignar grupos", "SET_BY_ADMIN": "Establecido por el administrador", "SET_BY_USER": "Establecido por el usuario", "AUTO_GENERATED": "Generado automáticamente", "DELETE_USER": "Eliminar usuario", "EDIT_USER": "<PERSON>ar usuario", "UPDATE": "Actualizar", "OFF": "Desactivado", "ON": "El", "ADD_ATTRIBUTE": "<PERSON><PERSON><PERSON>", "IMPORT_ATTRIBUTE": "Importar atributos", "CREATE_NEW_ATTRIBUTES": "Crear nuevos atributos", "TABLE_NUMBER": "N.º", "DISPLAY_NAME": "Nombre para mostrar", "ATTRIBUTE_NAME": "Nombre del atributo", "DATA_TYPE": "Tipo de <PERSON>", "TABLE_REQUIRED": "Requerido", "ORIGIN": "Origen", "SYSTEM_DEFINED": "definido por el sistema", "USER_DEFINED": "Definido por el usuario", "ENABLE": "Activar", "ENABLED": "Activado", "DISABLE": "Desacticar", "DISABLED": "Desactivado", "INFORMATION": "Información", "ENTER_TEXT": "Introduzca texto...", "ATTRIBUTE_REQUIRED": "Atributo requerido", "DESCRIPTION_OPTIONAL": "Descripción (opcional)", "DESCRIPTION": "Descripción ", "EDIT_ATTRIBUTE": "<PERSON>ar at<PERSON>", "DELETE_ATTRIBUTE": "Eliminar atributo", "ADD_SESSION_ATTRIBUTE": "Añadir atri<PERSON>o de sesión", "EDIT_SESSION_ATTRIBUTE": "Editar atributo de sesión", "DELETE_SESSION_ATTRIBUTE": "Eliminar atributo de sesión", "ADD_GROUP": "Añadir grupo", "ADD_USER_GROUP": "Añadir grupo de usuarios", "GROUP_NAME": "Nombre del grupo", "USER_NAME": "Nombre de usuario", "EDIT_GROUP": "Editar grupo", "DELETE_GROUP": "Eliminar grupo", "USER_GROUP_NAME": "Nombre de grupo de usuarios", "ASSIGN_USERS": "<PERSON><PERSON><PERSON> usuarios", "SOURCE": "Fuente", "STRING": "Cadena", "INTEGER": "<PERSON><PERSON>", "BOOLEAN": "<PERSON><PERSON><PERSON>", "DATE": "<PERSON><PERSON>", "DECIMAL": "Decimal", "CHANGE_PASSWORD_SETTINGS": "Cambiar la configuración de contraseña", "ZS_SERVICES": "<PERSON><PERSON><PERSON>", "CLOUD_AND_ORG_ID": "ID de nube y organización", "APPLICATION_DETAIL": "Detalle de la aplicación", "CLONE_MEMBERSHIP_FOR_APPLICATION": "Clonar acreditación como miembro de esta aplicación", "ROLE": "Rol", "ROLES": "Roles", "MANAGE_ROLES": "Administrar roles", "SYNC": "Sync", "USERS_ASSIGNMENT": "Asignación de usuarios", "GROUPS_ASSIGNMENT": "Asignación de grupo de usuarios", "ASSIGN_ROLE": "Asignar rol", "ASSIGN_SCOPE": "<PERSON><PERSON><PERSON>", "ASSIGN_USER_GROUPS": "Asignación de grupos", "ADD_MORE": "<PERSON><PERSON><PERSON>", "ASSIGN_USERS_AND_GROUPS": "Asignar usuarios y grupos de usuarios", "REVIEW": "Rev<PERSON><PERSON>", "USERS_AND_GROUPS": "Usuario y grupos de usuarios", "TYPE": "Acción de Política", "EDIT_USERS_ROLE": "Editar rol", "EUSA_AGREEMENT": "Acuerdo de suscripción de usuario ", "DELETE_USERS_ROLE": "Eliminar usuario", "EDIT_GROUPS_ROLE": "Editar rol", "DELETE_GROUPS_ROLE": "Eliminar grupo", "BULK_DELETE_USERS_GROUPS_ROLE": "Eliminar usuarios y grupos por lotes", "CONFIGURATION_TYPE": "Tipo de configuración", "RECOMMENDED": "Recomendado", "DEFAULT": "Defecto", "CUSTOM": "Personalizado", "PASSWORD_COMPLEXITY": "Complejidad de contraseña", "PASSWORD_LENGTH": "Longitud de la contraseña", "MIN_LWR_CASE": "N.º mínimo de letras minúsculas", "MIN_UPPR_CASE": "N.º mínimo de letras mayús<PERSON>s", "MIN_NUMERIC": "N.º mínimo de caracteres numéricos", "MIN_SPL_CHAR": "N.º mínimo de caracteres especiales", "PASSWORD_DOES_NOT_INCLUDE": "La contraseña no debe incluir el nombre de la empresa, el nombre de usuario, el nombre ni el apellido", "REJECT_REUSE": "Rechazar la reutilización de las últimas 5 contraseñas", "DEACTIVATE_USER": "Desactivar usuario después de 10 intentos fallidos", "ALLOW_ADMIN_SET_PASSWORD": "Permitir que el administrador cree o cambie la contraseña del usuario", "FORCE_PASSWORD_CHANGE": "Forzar el cambio de contraseña después del inicio de sesión inicial", "PASSWORD_EXPIRY": "Período de caducidad de la contraseña (en días)", "PASSWORD_CRITERIA": "Criterios de contraseña", "POLICY": "Política ", "POLICIES": "Políticas", "AUTHENTICATION_POLICY": "Política de autenticación", "PASSWORD_POLICY": "Política de contraseñas", "LOAD_MORE": "<PERSON>gar más", "EDIT_EMAIL": "Editar dirección de correo electrónico", "UPDATE_EMAIL": "Actualizar dirección de correo electrónico", "NEW_EMAIL": "Introduzca una nueva dirección de correo electrónico", "VALIDATE_EMAIL": "Validar correo electrónico", "VALIDATION_CODE": "Introduzca el código de validación", "RESEND_CODE": "Volver a enviar código", "CURRENT_PASSWORD": "Contraseña actual", "NEW_PASSWORD": "Nueva contraseña", "CONFIRM_NEW_PASSWORD": "Confirmar nueva contraseña", "MIN_LENGTH_REQUIRED": "{{value}} caracteres como mínimo", "MIN_LOWER_CASE_REQUIRED": "Al menos {{value}} carácter en minúsculas (a-z)", "MIN_UPPER_CASE_REQUIRED": "Al menos {{value}} carácter en mayúsculas (A-Z)", "MIN_NUMERIC_REQUIRED": "Al menos {{value}} carácter numérico (0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "Al menos {{value}} carácter especial", "SIGN_ON_POLICIES": "Política de inicio de sesión de administrador", "ADD_RULE": "<PERSON><PERSON><PERSON>", "RULE_ORDER": "Orden de regla", "RULE_STATUS": "Estado de regla", "RULE_NAME": "Nombre de la regla", "RULE_ACTION": "Acción de regla", "CRITERIA": "Criterios", "ADD_SIGN_ON_POLICY": "Añadir política de inicio de sesión de administrador", "EDIT_SIGN_ON_POLICY": "Editar política de inicio de sesión de administrador", "DELETE_SIGN_ON_POLICY": "Eliminar política de inicio de sesión de administrador", "LOCATION": "Ubicación", "LOCATION_GROUP": "Grupo de ubicaciones", "ALLOW": "<PERSON><PERSON><PERSON>", "DENY": "<PERSON><PERSON><PERSON>", "OPERATIONS": "Operaciones", "REMOVE": "Eliminar", "NO_DATA_FOUND": "No se han encontrados datos", "NO_ITEMS_FOUND": "No se ha encontrado ningún elemento", "BROWSE_FILE": "Examinar archivo", "IMPORT": "Importar", "NO_FILE_CHOSEN": "No se ha elegido ningún archivo", "OVERRIDE_EXISTING_ENTRIES": "Anular entradas existentes", "CSV_FILE": "Archivo CSV", "SAMPLE_CSV_DOWNLOAD": "<PERSON><PERSON><PERSON> muestra", "IMPORT_USERS": "Importar usuarios", "IMPORT_GROUPS": "Importar grupos", "PRIMARY_IDENTITY_PROVIDER": "Proveedor de identidad principal", "SECONDARY_IDENTITY_PROVIDER": "Proveedores de identidades secundarios", "ADD_PRIMARY_IDP": "<PERSON><PERSON><PERSON> principal", "ADD_SECONDARY_IDP": "<PERSON>ñadir IdP secundario", "ADD_PRIMARY_IDENTITY_PROVIDER": "<PERSON><PERSON><PERSON> de identidad principal", "EDIT_PRIMARY_IDENTITY_PROVIDER": "<PERSON>ar proveedor de identidad principal", "DELETE_PRIMARY_IDENTITY_PROVIDER": "Eliminar proveedor de identidad principal", "ADD_SECONDARY_IDENTITY_PROVIDER": "<PERSON><PERSON><PERSON> de identidad secundario", "EDIT_SECONDARY_IDENTITY_PROVIDER": "Editar proveedor de identidad secundario", "DELETE_SECONDARY_IDENTITY_PROVIDER": "Eliminar proveedor de identidad secundario", "SAML_CONFIGURATION": "Configuración de SAML", "OIDC_CONFIGURATION": "CONFIGURACIÓN DE OIDC", "IDENTITY_VENDOR": "Proveedor de identidad", "DOMAIN": "<PERSON>inio", "YES": "Sí", "NO": "No", "ADD_LOCATION": "Añadir ubicación", "EDIT_LOCATION": "Editar ubicación", "DELETE_LOCATION": "Eliminar ubicación", "IMPORT_LOCATION": "Importar ubicación", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "¿Está seguro de que desea eliminar esta ubicación? Los cambios no se pueden deshacer.", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "¿Está seguro de que desea eliminar por lotes estas ubicaciones? Los cambios no se pueden deshacer.", "IP_ADDRESS": "Dirección IP", "COUNTRY": "<PERSON><PERSON>", "LOCATION_INFORMATION": "Información de ubicación", "NAME_REQUIRED_MESSAGE": "El nombre es obligatorio", "LOCATIONS_REQUIRED_MESSAGE": "Ubicaciones es obligatorio", "COUNTRY_REQUIRED_MESSAGE": "El país es obligatorio", "IP_ADDRESS_REQUIRED_MESSAGE": "Se requiere una dirección IP", "LOCATION_COUNT": "Recuento de ubicaciones", "LOCATIONS": "Ubicaciones", "UNSELECTED_LOCATIONS": "Ubicaciones no seleccionadas", "SELECTED_LOCATIONS": "Ubicaciones seleccionadas", "ADD_LOCATION_GROUP": "Añadir grupo de ubicaciones", "EDIT_LOCATION_GROUP": "Editar grupo de ubicaciones", "DELETE_LOCATION_GROUP": "Eliminar grupo de ubicaciones", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "¿Está seguro de que desea eliminar este grupo de ubicaciones? Los cambios no se pueden deshacer.", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "¿Está seguro de que desea eliminar por lotes estos grupos de ubicaciones? Los cambios no se pueden deshacer.", "IMPORT_LOCATION_GROUP": "Importar grupo de ubicaciones", "PROTOCOL": "Protocolo", "AUTHENTICATION_REQUEST": "Solicitud de autenticación", "ENABLE_SAML_REQUEST_SIGNING": "Activar firma de solicitud SAML", "SAML_REQUEST_SIGNING": "Firma petición SAML", "LOGIN_HINT": "Pista de inicio de sesión", "SIGNING_ALGORITHM": "Algoritmo de firma", "REQUEST_SIGNING_CERTIFICATE": "Solicitar certificado de firma", "SP_SAML_CERTIFICATE": "Certificado SAML de SP", "DOWNLOAD_CERTIFICATE": "Descargar certificado", "ENCRYPTED_SAML_RESPONSE": "Respuesta SAML cifrada", "ENCRYPTED_SAML_ASSERTION": "Aserción SAML cifrada", "ENABLE_ENCRYPTED_SAML_ASSERTION": "Activar aserción SAML cifrada", "CERTIFICATE": "Certificado", "SAML_ENCRYPTION_CERTIFICATE": "Certificado de cifrado SAML", "IDP_METADATA": "Metadatos de IdP", "IDP_METADATA_URL": "URL de metadatos de IdP", "METADATA_URL": "URL de metadatos", "IDP_CERTIFICATE": "Certificado de IdP", "IDP_ENTITY_URI": "URI de entidad de IdP", "PRIMARY_IDP_NOT_AVAILABLE": "No se ha añadido proveedor de identidad principal.", "SECONDARY_IDP_NOT_AVAILABLE": "Proveedor de identidad secundario no disponible", "SP_METADATA": "Metadatos de SP", "DOWNLOAD_SP_METADATA": "Descargar metadatos de SP", "IDP_SINGLE_SIGNON_URL": "URL de inicio de sesión único de IdP", "REDIRECT_URI": "URI de redireccionamiento", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "Método de autenticación de punto de conexión de token", "ISSUER": "<PERSON><PERSON><PERSON>", "ZDK_INSTANCE": "Instancia de ZDK", "APPLICATION": "Aplicación", "ADD_CLAIM": "Añadir a<PERSON>", "VALUE": "Valor", "CLAIM_REQUIREMENTS": "Requisitos de afirmación", "SIGNATURE_VALIDATION": "Validación de firma", "EXPIRY": "Caducidad", "AUTHORIZATION_ENDPOINT": "Punto de conexión de autorización", "TOKEN_ENDPOINT": "Punto de conexión de token", "JWKS_ENDPOINT": "Punto de conexión JWKS", "USER_INFORMATION_ENDPOINT": "Punto de conexión de información de usuario", "CLIENT_SECRET_BASIC": "Secreto de cliente (Basic)", "CLIENT_SECRET_POST": "<PERSON><PERSON> de cliente (Post)", "CLIENT_ID": "ID de cliente", "CLIENT_SECRET": "Secreto de cliente", "REQUESTED_SCOPES": "Ámbitos solicitados", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "Contexto de autenticación solicitada predeterminada (opcional)", "SP_ENTITY_ID": "ID de entidad SP", "SP_URL": "URL de SP", "SCIM_PROVISIONING_STATUS": "Estado de aprovisionamiento de SCIM", "PROVISIONING_SETTINGS": "Configuración de aprovisionamiento", "JIT_PROVISIONING": "Aprovisionamiento Just-In-Time (JIT)", "ENABLE_FOR_JIT_PROVISIONING": "Activar para aprovisionamiento JIT", "JIT_ATTRIBUTE_MAPPING": "Asignación de atributos JIT", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "Asignación de atributos JUST-IN-TIME", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "Atributo de grupo de usuarios Just-In-Time", "JUST_IN_TIME_ATTRIBUTE": "Atributo Just-In-Time", "SAML_ATTRIBUTE_MAPPING": "Asignación de atributos SAML", "USER_GROUP_SAML_ATTRIBUTE": "Atributo SAML de grupo de usuarios", "SAML_ATTRIBUTE": "Atributo de SAML", "USER_ATTRIBUTE": "Atributo del usuario", "SCIM_PROVISIONING": "Aprovisionamiento de SCIM", "SCIM_ATTRIBUTE_MAPPING": "Asignación de atributos de SCIM", "SCIM_ATTRIBUTE": "Atributo de SCIM", "SCIM_ENDPOINT_URL": "URL de punto de conexión de SCIM", "SESSION_ATTRIBUTE_MAPPING": "Asignación de atributos de sesión", "IDP_ATTRIBUTE": "Atributo de IdP", "AUTHENTICATION_METHOD": "Método de autenticación", "TOKEN": "Token de portador", "GENERATE_TOKEN": "Generar token", "INPUT_METHOD": "Método de entrada", "FETCH_WITH_URL": "URL de metadatos", "UPLOAD_METADATA": "Cargar metadatos", "MANUAL_ENTRY": "Introducir manualmente", "UPLOAD_CERTIFICATE": "Cargar certificado", "BEARER_TOKEN": "Token de portador", "IDP_ADVANCED_SETTINGS": "Configuración avanzada de IdP", "SESSION_TIMEOUT": "Tiempo de espera de la sesión", "SESSION_TIMEOUT_DURATION_IN_MIN": "Duración del tiempo de espera de la sesión (en minutos)", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "Duración del tiempo de espera de inactividad del administrador (en minutos)", "SERVICE_ENROLLMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "Tiempo de espera de la sesión de inscripción en el servicio (en minutos)", "NONE": "<PERSON><PERSON><PERSON>", "TIME_RANGE": "Rango de <PERSON>", "ACTION": "Acción", "CATEGORY": "Categoría", "SUB_CATEGORY": "Subcategoría", "INTERFACE": "Interfaz", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "SCOPE": "Alcance", "SCOPES_N_ROLES": "Ámbitos y roles", "ADD_ITEMS": "<PERSON><PERSON><PERSON>", "TIME_STAMP": "Marca de tiempo", "RESOURCE": "Recurso", "ADMIN_ID": "ID de administrador", "CLIENT_IP": "IP de cliente", "AM": "AM", "TIME_PM": "pm", "START_TIME": "<PERSON>ra <PERSON>", "END_TIME": "Tiempo de fin", "CURRENT_DAY": "Hoy", "CURRENT_WEEK": "Semana actual", "CURRENT_MONTH": "Mes actual", "PREVIOUS_DAY": "Día anterior", "PREVIOUS_WEEK": "Semana anterior", "PREVIOUS_MONTH": "Mes anterior", "HELP_BROWSER": "<PERSON><PERSON><PERSON>", "SELECT_DATE": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha", "CLOSE": "<PERSON><PERSON><PERSON>", "ACCEPT": "Aceptar", "SYNC_DOMAINS": "Sincronizar dominios", "IMPORT_RESULTS": "Importar resultados", "COMPLETE": "Completo", "PROCESSED_RECORD": "Registros procesados", "TOTAL_RECORDS_ADDED": "Total de registros añadidos", "TOTAL_RECORDS_DELETED": "Total de registros eliminados", "TOTAL_RECORDS_IN_IMPORT": "Total de registros en importación", "TOTAL_RECORDS_UPDATED": "Total de registros actualizados", "FAILED_RECORDS": "Registros fallidos", "DUPLICATE_ITEM": "El nombre ya está en uso", "RESOURCE_NOT_FOUND": "Recurso no encontrado", "CSV_FORMAT_INVALID": "Formato CSV no válido", "UNEXPECTED_ERROR": "<PERSON><PERSON>r inesperado", "DUPLICATE_RECORD": "<PERSON>tro duplicado", "CONFIGURATION_CHANGES": "Cambios de configuración", "PRE_CHANGES_CONFIGURATION": "Configuración previa a los cambios", "POST_CHANGES_CONFIGURATION": "Configuración posterior a los cambios", "VIEW_CHANGES": "Ver cambios", "OPEN_HELP_BROWSER": "Abrir el navegador de ayuda", "ALL": "Todos", "SIGN_IN": "In<PERSON><PERSON>", "SIGN_OUT": "<PERSON><PERSON><PERSON>", "CREATE": "<PERSON><PERSON><PERSON>", "GET": "Obtener", "SEARCH": "Búsqueda", "BULK": "Por lotes", "DOWNLOAD": "<PERSON><PERSON><PERSON> ", "UPLOAD": "<PERSON><PERSON>", "USER_MANAGEMENT": "Gestión de usuario", "LOGIN": "Inicio de sesión", "AUTHENTICATION_SETTINGS": "Ajustes de Autenticación", "TENANTS": "Inquilinos", "USER": "Usuario", "CUSTOM_USER_ATTRIBUTE": "Atributo de usuario personalizado", "PASSWORD_CHANGE": "Cambio de contraseña", "PASSWORD_POLICY_CHANGE": "Cambio en la política de contraseñas", "IDENTITY_PROVIDERS": "Proveedores de identidad", "ADVANCE_SETTINGS": "Configuración avanzada", "ADMIN_SIGN_ON": "Inicio de sesión de administrador", "SIGN_ON_POLICY": "Política de inicio de sesión", "SERVICE_ASSIGNMENT": "Asignación de servicios", "SERVICES": "<PERSON><PERSON><PERSON>", "SCIM_API": "API de SCIM", "FAILURE": "Fallo", "PARTIALLY_FAILED": "Ha fallado parcialmente", "REMOVE_PAGE": "Eliminar página", "REMOVE_ALL": "<PERSON><PERSON><PERSON> todo", "CONFIRMATION_REMOVE_PAGE": "Eliminar página", "CONFIRMATION_REMOVE_ALL": "<PERSON><PERSON><PERSON> todo", "CONFIRM": "Confirmar", "CUSTOMISE_COLUMNS": "PERSONALIZAR COLUMNAS", "ALL_CHANGES_SAVED": "Se han guardado todos los cambios", "ITEM_HAS_BEEN_DELETED": "El elemento ha sido eliminado", "ITEMS_HAVE_BEEN_DELETED": "Se han eliminado elementos", "START_TIME_BEFORE_END_TIME": "La hora de inicio debe ser anterior a la hora de finalización", "USER_GROUPS_ASSIGNMENT_LIMIT": "No se pueden asignar más de {{value}} grupos de una sola vez", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "No se pueden asignar más de {{value}} roles a usuarios/grupos de una sola vez", "VERIFY_EMAIL_ADDRESS": "Verificar dirección de correo electrónico", "ENTER_EMAIL_OTP": "Introduzca el correo electrónico OTP", "SET_UP": "Configurar", "EMAIL_OTP": "OTP de correo electrónico", "NO_MATCHING_ITEMS_FOUND": "No se han encontrado elementos coincidentes", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "Token de portador generado correctamente", "MSG_EMAIL_OTP_SENT": "OTP de correo electrónico enviada a {{email}}", "MSG_INVALID_OTP": "OTP incorrecta introducida", "DIDNT_RECEIVE": "¿No lo ha recibido?", "SELECTED_CRITERIA_INVALID": "Los criterios seleccionados no son válidos", "ALLOW_FIDO_AS_PRIMARY": "Permitir FIDO2 como autenticador principal", "ALLOW_EMAIL_OTP_AS_PRIMARY": "Permitir <PERSON> de correo electrónico como autenticador principal", "AUTHENTICATORS": "Autenticadores", "MFA_AUTHENTICATOR": "Autenticador MFA", "LAST_USED": "Último uso", "DELETE_AUTHENTICATOR": "Eliminar autenticador MFA", "RESTART_TENANT_CREATION": "Reiniciar creación de inquilino", "CONTINUE": "<PERSON><PERSON><PERSON><PERSON>", "PARTIAL_TENANT_MSG": "La configuración del inquilino de Zscaler se ha completado parcialmente.", "CLICK_CONTINUE_MSG": "Haga clic en \"Continuar\" para completar la configuración.", "DISABLE_MULTIFACTOR": "Desactivar autenticación multifactor", "DISABLE_FIDO": "Desactivar FIDO2", "DISABLE_FIDO_MSG": "Puede bloquearse la cuenta si solo utiliza una clave de seguridad o datos biométricos para la autenticación. La única forma de acceder a su cuenta es a través de la autenticación basada en contraseña si desactiva MFA. En caso de que no recuerde su contraseña o aún no la haya configurado, solicite un restablecimiento de contraseña.", "DISABLE_MFA_MSG": "La desactivación de la autenticación multifactor es un cambio global. Esto hace que los usuarios estén menos protegidos. ¿Está seguro de que desea continuar?", "ADMINISTRATIVE": "Administrativo", "SERVICE_ENTITLEMENTS": "Derechos de servicio", "ADMINISTRATIVE_ENTITLEMENTS": "Derechos administrativos", "ORGANIZATION_ID": "ID de la organización", "GROUPS": "Grupos", "ASSIGN_USER": "<PERSON><PERSON><PERSON> usua<PERSON>", "SELECT_USERS_AND_ROLE": "Seleccionar usuarios y roles", "SUMMARY": "Resumen", "ASSIGN": "<PERSON><PERSON><PERSON>", "SELECT_GROUPS_AND_ROLE": "Seleccionar grupos y roles", "SERVICE_NAME": "Nombre del servicio", "LOGIN_NAME": "Nombre de inicio de sesión", "ADD_ROLE": "<PERSON><PERSON><PERSON> rol", "EDIT_ROLE": "Editar rol", "USERS_AND_GROUPS_TEXT": "Usuarios y grupos", "USERS_CREDENTIALS": "Credenciales de usuarios", "AUTHENTICATION_SESSION": "Sesión de autenticación", "AUTHENTICATION_SESSION_FOR_SERVICE_ENROLLMENT": "Sesión de autenticación para inscripción en servicios", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "Forzar autenticación para reautenticación de acceso privado", "ZSLOGIN_AS_IDENTITY_PROVIDER": "ZSLogin como proveedor de identidad", "AZURE_AD_AS_TENANT": "Inquilino A de Azure AD (OIDC)", "OKTA_TENANT": "Inquilino B de Okta (SAML)", "PING_IDENTITY": "Inquilino C de Ping Identity (SAML)", "DOMAINS": "<PERSON><PERSON><PERSON>", "SERVICE_ENTITLEMENTS_TEXT": "Derechos de servicio", "AUTHENTICATION_EVENT_LOG": "Registro de eventos de autenticación", "DELETE_ROLE": "Eliminar rol", "ENTITLEMENTS": "Derechos", "ENTITLEMENTS_LABEL": "Derechos", "USER_ID": "ID de usuario", "REMOVE_ASSIGNMENTS": "Eliminar asignaciones", "SELECT_USERS": "Seleccionar usuarios", "ASSIGN_GROUP": "Asignar grupo", "SELECT_GROUPS": "Seleccionar grupos", "SAME_ROLE_FOR_SELECTED_USERS": "Establecer el mismo rol para todos los usuarios seleccionados", "SAME_ROLE_FOR_SELECTED_GROUPS": "Establecer el mismo rol para todos los grupos seleccionados", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "Establecer el mismo rol y ámbito para todos los usuarios seleccionados", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "Establecer el mismo rol y ámbito para todos los grupos seleccionados", "ATTRIBUTE_ALREADY_MAPPED": "El atributo {{value}} ya se ha asignado", "MFA_TOGGLE_TOOLTIP": "La activación de la autenticación multifactor permite a los usuarios seleccionar un factor de autenticación secundario junto con las contraseñas.", "FIDO_TOGGLE_TOOLTIP": "Activar FIDO2 como principal permite a los usuarios omitir contraseñas y usar una clave biométrica o de seguridad como método de autenticación.", "LINKED_TENANTS": "<PERSON><PERSON><PERSON>", "SELECT_ROLE": "Seleccionar rol", "SELECT_SCOPE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ALL_USERS": "Todos los usuarios", "ASSIGNED_USERS": "Usuarios asignados", "UNSELECTED_ROLES": "Roles no seleccionados", "SELECTED_ROLES": "Roles sele<PERSON>", "MANAGE": "Administrar", "EDIT_NAME": "Editar nombre", "VIEW_ROLES": "Ver roles", "DEVICE_GROUP_RESTRICTIONS": "Restricciones de grupo de dispositivos", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "Administrar restricciones de grupos de dispositivos", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "Activar restricciones de grupos de dispositivos", "DEVICE_GROUPS": "Grupos de dispositivos", "ORGANIZATION_NAME": "Nombre de org.", "DEVICE_GROUP_ASSIGNMENT": "Asignación de grupo de dispositivos", "SERVICE_RUNTIME_ASSIGNMENT": "Asignación de tiempo de ejecución de servicio", "ROLE_ASSIGNMENT": "Asignación de rol", "DELETE_ASSIGNMENT": "Eliminar asignación", "NO_RECORD_EXISTS": "No existe ningún registro", "SERVICE": "<PERSON><PERSON><PERSON>", "REMOTE_ASSISTANCE": "<PERSON><PERSON><PERSON><PERSON> remota", "CXO_INSIGHT": "CXO Insight", "VIEW": "Vista", "ACCESS_TYPE": "Tipo de acceso", "FULL_ACCESS": "Acceso completo", "ENABLE_FULL_ACCESS": "Activar acceso completo", "ACCESS_VALID_UNTIL": "Acceso válido hasta", "VIEW_ONLY_ACCESS": "Acceso de solo lectura", "ENABLE_VIEW_ONLY_ACCESS": "Activar acceso de solo lectura", "DEVICE_TOKEN": "Token de dispositivo", "TOKEN_VALIDATORS": "Validadores de tokens", "TOKEN_VALIDATOR": "Validador de tokens", "ADD_TOKEN_VALIDATOR": "<PERSON><PERSON><PERSON> validador de tokens", "EDIT_TOKEN_VALIDATOR": "Editar validador de token", "DELETE_TOKEN_VALIDATOR": "Eliminar validador de tokens", "AUDIENCE": "Des<PERSON><PERSON><PERSON>", "SUBJECT_CLAIM": "Afirmación de sujeto", "CLIENT_JWKS": "JWK(s) de cliente", "CERTIFICATES_PUBLIC_KEYS": "Certificados y claves públicas", "CLIENT_JWKS_URL": "URL de JWK de cliente", "VALIDATION_TYPE": "Tipo de validación", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Token de dispositivo de Client Connector", "GUEST_DOMAINS": "<PERSON><PERSON><PERSON> in<PERSON>", "ARBITRARY_GUEST_DOMAINS": "Dominios invitados arbitrarios", "GUEST_USER": "<PERSON><PERSON><PERSON> invitado", "GUEST_DOMAIN": "<PERSON><PERSON><PERSON> in<PERSON>o", "ADD": "<PERSON><PERSON><PERSON>", "DEPARTMENT": "Departamento", "ENABLE_TO_ALLOW_ALL_DOMAINS": "Activar para permitir todos los dominios", "CUSTOM_ATTRIBUTE": "Atributo personalizado", "BRANDING": "<PERSON><PERSON>", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "Personalizar dirección de correo electrónico del remitente", "CONFIGURATION_SETTINGS": "Ajustes de configuración", "CUSTOMIZE_EMAIL_SUBJECT": "Personalizar asunto de correo electrónico", "EMAIL_FROM_ADDRESS": "Dirección de remitente de correo electrónico", "EMAIL_SUBJECT": "Asunto del correo electrónico", "EDIT_LOGO": "Editar logotipo", "LOGO": "Logotipo", "UPLOAD_LOGO": "Cargar logotipo", "INVALID_IMAGE_TYPE": "Tipo de imagen no válido", "BASE_64_CONVERSION_FAILED": "No se puede convertir la imagen a la versión codificada en base 64", "DEPARTMENTS": "Departamentos", "ADD_DEPARTMENT": "<PERSON><PERSON><PERSON> departamento", "EDIT_DEPARTMENT": "Editar departamento", "DELETE_DEPARTMENT": "Eliminar departamento", "IMPORT_DEPARTMENT": "Importar departamento", "DEPARTMENT_NAME": "Nombre del departamento", "VIEW_USER": "Ver usuario", "VIEW_GROUP": "Ver grupo de usuarios", "DASHBOARD": "Cuadro de Mandos", "CREATED": "<PERSON><PERSON><PERSON>", "UPDATED": "Actualizada", "DELETED": "Eliminado", "EXTERNAL_IDP": "IdP externo", "HOSTED": "<PERSON><PERSON><PERSON>", "TOTAL": "Total", "CSV": "CSV", "MANUAL": "Manual", "EMAIL_LOGIN": "OTP de correo electrónico", "FIDO_LOGIN": "Autenticación FIDO", "PWD_LOGIN": "contraseña", "TOTP_LOGIN": "Inicio de sesión TOTP", "SMS_LOGIN": "Inicio de sesión SMS", "SAML_LOGIN": "SAML", "OIDC_LOGIN": "OIDC", "JIT": "jit", "SCIM": "SCIM", "ADMIN_ASSIGNMENT": "Asignación de administrador", "RUNTIME_ASSIGNMENT": "Asignación de tiempo de ejecución", "ZCC_ASSIGNMENT": "Asignación de ZCC", "ONE_DAY": "1 día", "TWO_DAYS": "2 días", "SEVEN_DAYS": "7 días", "FOURTEEN_DAYS": "14 días", "THIRTY_DAYS": "30 dias", "SIXTY_DAYS": "60 días", "NINTY_DAYS": "90 dias", "NO_DATA_AVAILABLE": "No hay datos disponibles", "ZSLOGIN_FOR_ADMINISTRATORS": "ZSLogin para administradores", "ZIDENTITY_FOR_ADMINISTRATORS": "ZIdentity para administradores", "REVERT_TO_PROVISIONED": "Revertir a aprovisionado", "REVERT": "Revertir", "ZSLOGIN_MIGRATION_STATUS": "Estado de migración de ZSLogin", "ZIDENTITY_MIGRATION_STATUS": "Estado de migración de ZIdentity", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "Verificación incorrecta de la configuración del proveedor de identidad externo", "UPDATE_PSEUDO_DOMAIN": "<PERSON>ual<PERSON><PERSON>", "CURRENT_PSEUDO_DOMAIN_NAME": "Nombre del seudodominio actual", "NEW_PSEUDO_DOMAIN_NAME": "Nombre del nuevo seudodominio", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "¡Actualización de seudodominio realizada correctamente!", "MULTIFACTOR_BYPASS": "Omisión de verificación multifactor", "SKIP_SECOND_FACTOR_AUTHENTICATION": "Omitir autenticación de segundo factor", "SKIP_UNTIL": "<PERSON><PERSON><PERSON>", "INTEGRATION": "Integración", "API_CLIENTS": "Clientes de API", "API_RESOURCES": "Recursos de API", "ADD_API_CLIENT": "Añadir cliente de API", "ADD_API_RESOURCE": "Añadir recurso de API", "EDIT_API_RESOURCE": "Editar recurso de API", "API_RESOURCE": "Recurso de API", "ACCESS_TOKEN_VALIDITY": "Validez del token de acceso", "MINUTES": "<PERSON><PERSON><PERSON>", "HOURS": "<PERSON><PERSON>", "PRIVATE_KEY_JWT": "Private Key JWT", "CLIENT_JWK_URL": "URL de JWK de cliente", "CERTIFICATE_PUBLIC_KEY": "Certificados/Claves públicas", "CLIENT_JWK": "JWK de cliente", "CLIENT_SECRETS": "Secretos de cliente", "CLIENT_AUTHENTICATION": "Autenticación de cliente", "CLIENT_INFORMATION": "Información de cliente", "ZSCALER_API": "API de ZSCALER", "RESOURCE_NAME": "Nombre del recurso", "NEVER": "Nunca", "CERTIFICATES_AND_PUBLIC_KEYS": "Certificados y claves públicas", "DELETE_API_CLIENT": "Eliminar cliente de API", "DELETE_API_CLIENT_MESSAGE": "¿Está seguro de que desea eliminar este cliente de API? Los cambios no se pueden deshacer.", "ACCESS_TOKEN_REQUIRED_MESSAGE": "Se requiere la duración del token de acceso.", "EXPIRES": "Caduca el", "SECRET": "Secreto", "EDIT_API_CLIENT": "Editar cliente de API", "VIEW_API_CLIENT": "Ver cliente de API", "CREATED_ON": "<PERSON><PERSON><PERSON> en", "EXPIRES_ON": "Caduca el", "API_CLIENT": "Cliente API", "CLIENT_SECRET_WARNING": "Copiar secreto de cliente. No se mostrará posteriormente.", "API_CLIENTS_AND_RESOURCES": "Clientes y recursos de API", "LOGIN_ID_ATTRIBUTE": "Atributo de ID de inicio de sesión", "LOGOUT": "<PERSON><PERSON><PERSON>", "ENVIRONMENT": "Entorno", "SYSTEM": "Sistema", "ZIDENTITY_ROLES": "Roles de ZIdentity", "CLIENT": "Cliente", "RESOURCES": "Recursos", "BASIC": "Básico", "ADVANCED": "Advanced", "PROVISIONING": "Aprovisionamiento", "ZIDENTITY_DASHBOARD": "Panel de control de ZIdentity", "AUTHENTICATION_LEVELS": "Niveles de autenticación", "LEVEL_NAME": "Nombre del nivel", "VALIDITY": "Validez", "DURATION": "Duración", "MESSAGE_TO_USER": "Mensaje al usuario (opcional)", "PARENT": "Principal", "SUB_LEVEL_NAME": "Nombre de nivel secundario", "DAYS": "Día", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "Asignación de contexto de niveles a autenticación", "LEVELS": "<PERSON><PERSON><PERSON>", "AUTHENTICATION_CONTEXT": "Contexto de autenticación", "DELETE_AL": "Eliminar nivel de autenticación", "DELETE_AL_CONFIRM_MSG": "Esta acción es irreversible y eliminará por completo el nivel de autenticación. ¿Está seguro de que desea continuar?", "MOVE": "MOVE"}