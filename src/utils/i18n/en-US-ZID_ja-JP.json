{"LOCALE": "ja-<PERSON>", "EMAIL_ADDRESS": "メール アドレス", "EMAIL_PLACEHOLDER": "メール アドレスを入力...", "LOGIN_ID_PLACEHOLDER": "ログインIDを入力...", "LOGIN_ID_LABEL": "ログインID", "LOGIN_PASSWORD_PLACEHOLDER": "パスワードを入力...", "PASSWORD_LABEL": "パスワード", "NEW_PASSWORD_LABEL": "新しいパスワード", "OLD_PASSWORD_LABEL": "古いパスワード", "CONFIRM_NEW_PASSWORD_LABEL": "新しいパスワードの確認", "PASSWORD_PLACEHOLDER": "パスワードを入力", "FORGOT_PASSWORD": "パスワードを忘れた場合", "REMEMBER_ME": "ログイン情報を記憶する", "RESET_PASSWORD": "パスワードをリセット", "CHANGE_PASSWORD": "パスワードを変更", "SUCCESS": "成功", "RESEND": "再送信", "SIGN_IN_LABEL": "サイン イン", "INITIAL_DOMAIN_NAME": "初期ドメイン名", "FIRST_NAME": "名", "LAST_NAME": "姓", "PHONE_NUMBER": "電話番号", "REQUIRED_VALIDATION_MESSAGE": "必須", "INVALID_CREDENTIALS_MESSAGE": "無効なログインIDまたはパスワード", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "監査役はここからログインできません", "LANGUAGE": "英語(米国)", "COOKIES_DISABLED": "このアプリケーションを使用するには、Cookieを許可する必要があります。このサイトでは、ブラウザーのCookieサポートを有効にしてください。", "COOKIES_NOT_ALLOWED": "Cookieが許可されていません", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "このブラウザーはサポートされていないため、このサイトの機能が正しく動作しない可能性があります。ブラウザーを最新バージョンに更新することをお勧めします。このメッセージを無視するには、[OK]をクリックしてください。", "BROWSER_NOT_SUPPORTED": "ブラウザーのバージョンがサポートされていません", "OK": "OK", "NEXT": "次へ", "BACK": "戻る", "SAVE": "保存", "SAVE_AND_NEXT": "保存して次へ", "COPYRIGHT": "Copyright", "COPYRIGHT_STATEMENT": "All rights reserved.", "POWERED_BY": "提供元", "ADMIN_PORTAL_SIGN_IN": "管理ポータルへのサインイン", "ALL_ORGANIZATIONS": "すべての組織", "SEARCH_PLACEHOLDER": "検索...", "GENERAL": "一般", "ZPA": "Zscaler Private Access", "IAM": "アイデンティティーとアクセス管理", "ZIA": "Zscaler Internet Access", "ZDX": "Zscaler Digital Experience", "EC": "Cloud & Branch Connector", "CCP": "Client Connectorポータル", "ZBI": "ブラウザー分離", "BI": "ビジネス インサイト", "ZRA": "Risk360", "LAUNCH": "開始", "VERIFY": "検証", "CANCEL": "キャンセル", "DONE": "終了", "RESET": "リセット", "CLEAR": "クリア", "CLEAR_ALL": "すべてクリア", "MY_PROFILE": "自分のプロファイル", "ZS_LOGIN": "ZSL<PERSON>in", "Z_IDENTITY": "ZIdentity", "ACCOUNT_MANAGEMENT": "アカウント管理", "CLOUD_CONFIGURATION": "クラウド構成", "PRODUCT": "製品", "CLOUD_NAME": "クラウド名", "CLOUD_ID": "クラウドID", "ORG_NAME": "組織名", "LINKED_SERVICES": "リンクされたサービス", "LINK": "リンク", "UNLINK": "リンク解除", "LINKED": "リンク済み", "UNLINKED": "リンク解除済み", "LINK_TENANT": "テナントをリンク", "UNLINK_TENANT": "テナントのリンクを解除", "ATTRIBUTE": "属性", "ATTRIBUTES": "属性", "USER_ATTRIBUTES": "ユーザー属性", "SESSION_ATTRIBUTE": "セッション属性", "ADVANCED_SETTINGS": "詳細設定", "DIRECTORY": "ディレクトリー", "USERS": "ユーザー", "USER_GROUPS": "ユーザー グループ", "EXTERNAL_IDENTITIES": "外部アイデンティティー", "APPLICATIONS": "アプリケーション", "ZSCALER_SERVICES": "Zscalerサービス", "SECURITY": "セキュリティ", "IP_LOCATIONS": "IPロケーション", "IP_LOCATIONS_AND_GROUPS": "IPロケーションとグループ", "IP_LOCATION_GROUPS": "IPロケーション グループ", "ADMINISTRATION": "管理", "ADMINISTRATION_CONTROLS": "管理コントロール", "AUTHENTICATION": "認証", "AUTHENTICATION_METHODS": "認証方式", "ENABLE_MULTIFACTOR_SETTINGS": "多要素設定の有効化", "ENABLE_MULTIFACTOR_AUTH_USER": "サービス登録の多要素認証(MFA)を有効化", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "管理者の多要素認証(MFA)を有効化", "MFA_ENROLLMENT_GRACE_PERIOD": "MFA登録の猶予期間(日数)", "AUDIT_LOGS": "監査ログ", "ADD_USER": "ユーザーを追加", "IMPORT_CSV": "CSVをインポート", "ACTIONS": "アクション", "ACTIVATE": "アクティブ化", "DE_ACTIVATE": "非アクティブ化", "BULK_ACTIVATE": "一括アクティブ化", "BULK_DEACTIVATE": "一括非アクティブ化", "BULK_DELETE": "一括削除", "DELETE": "削除", "NAME": "名前", "GROUP": "グループ", "SELECT": "選択", "LOGIN_ID": "ログインID", "STATUS": "ステータス", "GENERAL_INFORMATION": "一般情報", "MY_ACCOUNT": "マイ アカウント", "FULL_NAME": "フルネーム", "VERIFIED": "検証済み", "LANGUAGE_LABEL": "言語", "TIMEZONE": "タイムゾーン", "ACTIVE": "アクティブ", "INACTIVE": "非アクティブ", "USER_INFORMATION": "ユーザー情報", "PRIMARY_EMAIL": "プライマリー メール アドレス", "SAME_AS_LOGIN_ID": "ログインIDと同じ", "SECONDARY_EMAIL": "セカンダリー メール アドレス", "ADDITIONAL_ATTRIBUTES": "追加属性", "SECURITY_SETTINGS": "セキュリティ設定", "PASSWORD_OPTION": "パスワード オプション", "PROMPT_PASSWORD_FIRST_LOGIN": "初回ログイン後にパスワード変更を促す", "CONFIRM_PASSWORD": "パスワードの確認", "ASSIGNMENT": "割り当て", "ASSIGN_GROUPS": "グループの割り当て", "SET_BY_ADMIN": "管理者による設定", "SET_BY_USER": "ユーザーによる設定", "AUTO_GENERATED": "自動生成", "DELETE_USER": "ユーザーを削除", "EDIT_USER": "ユーザーを編集", "UPDATE": "アップデート", "OFF": "オフ", "ON": "オン", "ADD_ATTRIBUTE": "属性を追加", "IMPORT_ATTRIBUTE": "属性をインポート", "CREATE_NEW_ATTRIBUTES": "新しい属性を作成", "TABLE_NUMBER": "No.", "DISPLAY_NAME": "表示名", "ATTRIBUTE_NAME": "属性名", "DATA_TYPE": "データ タイプ", "TABLE_REQUIRED": "必須", "ORIGIN": "定義元", "SYSTEM_DEFINED": "システム定義", "USER_DEFINED": "ユーザー定義", "ENABLE": "有効化", "ENABLED": "有効", "DISABLE": "無効化", "DISABLED": "無効", "INFORMATION": "情報", "ENTER_TEXT": "テキストを入力...", "ATTRIBUTE_REQUIRED": "必須属性", "DESCRIPTION_OPTIONAL": "説明(省略可)", "DESCRIPTION": "説明", "EDIT_ATTRIBUTE": "属性を編集", "DELETE_ATTRIBUTE": "属性を削除", "ADD_SESSION_ATTRIBUTE": "セッション属性を追加", "EDIT_SESSION_ATTRIBUTE": "セッション属性を編集", "DELETE_SESSION_ATTRIBUTE": "セッション属性を削除", "ADD_GROUP": "グループを追加", "ADD_USER_GROUP": "ユーザー グループを追加", "GROUP_NAME": "グループ名", "USER_NAME": "ユーザー名", "EDIT_GROUP": "グループを編集", "DELETE_GROUP": "グループを削除", "USER_GROUP_NAME": "ユーザー グループ名", "ASSIGN_USERS": "ユーザーの割り当て", "SOURCE": "送信元", "STRING": "文字列", "INTEGER": "整数", "BOOLEAN": "ブール値", "DATE": "日付", "DECIMAL": "小数", "CHANGE_PASSWORD_SETTINGS": "パスワード設定の変更", "ZS_SERVICES": "Zscalerサービス", "CLOUD_AND_ORG_ID": "クラウドと組織ID", "APPLICATION_DETAIL": "アプリケーションの詳細", "CLONE_MEMBERSHIP_FOR_APPLICATION": "このアプリケーションからメンバーシップをクローン", "ROLE": "ロール", "ROLES": "ロール", "MANAGE_ROLES": "ロールの管理", "SYNC": "同期", "USERS_ASSIGNMENT": "ユーザーの割り当て", "GROUPS_ASSIGNMENT": "ユーザー グループの割り当て", "ASSIGN_ROLE": "ロールの割り当て", "ASSIGN_SCOPE": "範囲の割り当て", "ASSIGN_USER_GROUPS": "グループの割り当て", "ADD_MORE": "さらに追加", "ASSIGN_USERS_AND_GROUPS": "ユーザーとユーザー グループの割り当て", "REVIEW": "確認", "USERS_AND_GROUPS": "ユーザーとユーザー グループ", "TYPE": "タイプ", "EDIT_USERS_ROLE": "ロールを編集", "EUSA_AGREEMENT": "エンド ユーザー サブスクリプション契約", "DELETE_USERS_ROLE": "ユーザーを削除", "EDIT_GROUPS_ROLE": "ロールを編集", "DELETE_GROUPS_ROLE": "グループを削除", "BULK_DELETE_USERS_GROUPS_ROLE": "ユーザーとグループを一括削除", "CONFIGURATION_TYPE": "構成タイプ", "RECOMMENDED": "推奨", "DEFAULT": "デフォルト", "CUSTOM": "カスタム", "PASSWORD_COMPLEXITY": "パスワードの複雑さ", "PASSWORD_LENGTH": "パスワードの長さ", "MIN_LWR_CASE": "小文字の最小文字数", "MIN_UPPR_CASE": "大文字の最小文字数", "MIN_NUMERIC": "数字の最小文字数", "MIN_SPL_CHAR": "特殊文字の最小文字数", "PASSWORD_DOES_NOT_INCLUDE": "パスワードには、会社名、ユーザー名、姓、名を含めることはできません", "REJECT_REUSE": "過去5回のパスワードの再利用を禁止", "DEACTIVATE_USER": "10回のログイン失敗後にユーザーを非アクティブ化", "ALLOW_ADMIN_SET_PASSWORD": "管理者にユーザーのパスワードの作成または変更を許可", "FORCE_PASSWORD_CHANGE": "初回ログイン後にパスワードの変更を強制", "PASSWORD_EXPIRY": "パスワードの有効期限(日数)", "PASSWORD_CRITERIA": "パスワードの条件", "POLICY": "ポリシー", "POLICIES": "ポリシー", "AUTHENTICATION_POLICY": "認証ポリシー", "PASSWORD_POLICY": "パスワード ポリシー", "LOAD_MORE": "さらに読み込む", "EDIT_EMAIL": "メール アドレスの編集", "UPDATE_EMAIL": "メール アドレスの更新", "NEW_EMAIL": "新しいメール アドレスの入力", "VALIDATE_EMAIL": "メール アドレスの検証", "VALIDATION_CODE": "検証コードの入力", "RESEND_CODE": "コードの再送信", "CURRENT_PASSWORD": "現在のパスワード", "NEW_PASSWORD": "新しいパスワード", "CONFIRM_NEW_PASSWORD": "新しいパスワードの確認", "MIN_LENGTH_REQUIRED": "{{value}}文字以上", "MIN_LOWER_CASE_REQUIRED": "{{value}}文字以上の小文字(a-z)", "MIN_UPPER_CASE_REQUIRED": "{{value}}文字以上の大文字(A-Z)", "MIN_NUMERIC_REQUIRED": "{{value}}文字以上の数字(0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "{{value}}文字以上の特殊文字", "SIGN_ON_POLICIES": "管理者サインオン ポリシー", "ADD_RULE": "ルールを追加", "RULE_ORDER": "ルールの順序", "RULE_STATUS": "ルールのステータス", "RULE_NAME": "ルール名", "RULE_ACTION": "ルールの処理", "CRITERIA": "条件", "ADD_SIGN_ON_POLICY": "管理者サインオン ポリシーの追加", "EDIT_SIGN_ON_POLICY": "管理者サインオン ポリシーの編集", "DELETE_SIGN_ON_POLICY": "管理者サインオン ポリシーの削除", "LOCATION": "ロケーション", "LOCATION_GROUP": "ロケーション グループ", "ALLOW": "許可", "DENY": "拒否", "OPERATIONS": "オペレーション", "REMOVE": "削除", "NO_DATA_FOUND": "データが見つかりません", "NO_ITEMS_FOUND": "項目が見つかりません", "BROWSE_FILE": "ファイルを参照", "IMPORT": "インポート", "NO_FILE_CHOSEN": "ファイルが選択されていません", "OVERRIDE_EXISTING_ENTRIES": "既存のエントリーをオーバーライド", "CSV_FILE": "CSVファイル", "SAMPLE_CSV_DOWNLOAD": "サンプルをダウンロード", "IMPORT_USERS": "ユーザーをインポート", "IMPORT_GROUPS": "グループをインポート", "PRIMARY_IDENTITY_PROVIDER": "プライマリーIDプロバイダー", "SECONDARY_IDENTITY_PROVIDER": "セカンダリーIDプロバイダー", "ADD_PRIMARY_IDP": "プライマリーIdPを追加", "ADD_SECONDARY_IDP": "セカンダリーIdPを追加", "ADD_PRIMARY_IDENTITY_PROVIDER": "プライマリーIDプロバイダーの追加", "EDIT_PRIMARY_IDENTITY_PROVIDER": "プライマリーIDプロバイダーの編集", "DELETE_PRIMARY_IDENTITY_PROVIDER": "プライマリーIDプロバイダーの削除", "ADD_SECONDARY_IDENTITY_PROVIDER": "セカンダリーIDプロバイダーの追加", "EDIT_SECONDARY_IDENTITY_PROVIDER": "セカンダリーIDプロバイダーの編集", "DELETE_SECONDARY_IDENTITY_PROVIDER": "セカンダリーIDプロバイダーの削除", "SAML_CONFIGURATION": "SAML構成", "OIDC_CONFIGURATION": "OIDC構成", "IDENTITY_VENDOR": "IDベンダー", "DOMAIN": "ドメイン", "YES": "はい", "NO": "いいえ", "ADD_LOCATION": "ロケーションを追加", "EDIT_LOCATION": "ロケーションを編集", "DELETE_LOCATION": "ロケーションを削除", "IMPORT_LOCATION": "ロケーションをインポート", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "このアプリケーションを削除してもよろしいですか？この変更を元に戻すことはできません。", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "これらのロケーションを一括削除してもよろしいですか？この変更を元に戻すことはできません。", "IP_ADDRESS": "IPアドレス", "COUNTRY": "国", "LOCATION_INFORMATION": "ロケーション情報", "NAME_REQUIRED_MESSAGE": "名前は必須です", "LOCATIONS_REQUIRED_MESSAGE": "ロケーションは必須です", "COUNTRY_REQUIRED_MESSAGE": "国は必須です", "IP_ADDRESS_REQUIRED_MESSAGE": "IPアドレスは必須です", "LOCATION_COUNT": "ロケーション数", "LOCATIONS": "ロケーション", "UNSELECTED_LOCATIONS": "未選択のロケーション", "SELECTED_LOCATIONS": "選択済みのロケーション", "ADD_LOCATION_GROUP": "ロケーション グループを追加", "EDIT_LOCATION_GROUP": "ロケーション グループを編集", "DELETE_LOCATION_GROUP": "ロケーション グループを削除", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "このロケーション グループを削除してもよろしいですか？この変更を元に戻すことはできません。", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "これらのロケーション グループを一括削除してもよろしいですか？この変更を元に戻すことはできません。", "IMPORT_LOCATION_GROUP": "ロケーション グループをインポート", "PROTOCOL": "プロトコル", "AUTHENTICATION_REQUEST": "認証リクエスト", "ENABLE_SAML_REQUEST_SIGNING": "SAMLリクエスト署名の有効化", "SAML_REQUEST_SIGNING": "SAMLリクエスト署名", "LOGIN_HINT": "ログインのヒント", "SIGNING_ALGORITHM": "署名アルゴリズム", "REQUEST_SIGNING_CERTIFICATE": "リクエスト署名証明書", "SP_SAML_CERTIFICATE": "SP SAML証明書", "DOWNLOAD_CERTIFICATE": "証明書をダウンロード", "ENCRYPTED_SAML_RESPONSE": "暗号化されたSAMLレスポンス", "ENCRYPTED_SAML_ASSERTION": "暗号化されたSAMLアサーション", "ENABLE_ENCRYPTED_SAML_ASSERTION": "暗号化されたSAMLアサーションの有効化", "CERTIFICATE": "証明書", "SAML_ENCRYPTION_CERTIFICATE": "SAML暗号化証明書", "IDP_METADATA": "IdPメタデータ", "IDP_METADATA_URL": "IdPメタデータURL", "METADATA_URL": "メタデータURL", "IDP_CERTIFICATE": "IdP証明書", "IDP_ENTITY_URI": "IdPエンティティーURI", "PRIMARY_IDP_NOT_AVAILABLE": "プライマリーIDプロバイダーが追加されていません。", "SECONDARY_IDP_NOT_AVAILABLE": "セカンダリーIDプロバイダーは利用できません", "SP_METADATA": "SPメタデータ", "DOWNLOAD_SP_METADATA": "SPメタデータをダウンロード", "IDP_SINGLE_SIGNON_URL": "IdPシングル サインオンURL", "REDIRECT_URI": "リダイレクトURI", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "トークン エンドポイント認証方式", "ISSUER": "発行者", "ZDK_INSTANCE": "ZDKインスタンス", "APPLICATION": "アプリケーション", "ADD_CLAIM": "要求を追加", "VALUE": "値", "CLAIM_REQUIREMENTS": "要求要件", "SIGNATURE_VALIDATION": "署名検証", "EXPIRY": "有効期限", "AUTHORIZATION_ENDPOINT": "認可エンドポイント", "TOKEN_ENDPOINT": "トークン エンドポイント", "JWKS_ENDPOINT": "JWKSエンドポイント", "USER_INFORMATION_ENDPOINT": "ユーザー情報エンドポイント", "CLIENT_SECRET_BASIC": "クライアント シークレット ベーシック", "CLIENT_SECRET_POST": "クライアント シークレット ポスト", "CLIENT_ID": "クライアントID", "CLIENT_SECRET": "クライアント シークレット", "REQUESTED_SCOPES": "リクエストされた範囲", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "デフォルトのリクエストされた認証コンテキスト(省略可)", "SP_ENTITY_ID": "SPエンティティーID", "SP_URL": "SP URL", "SCIM_PROVISIONING_STATUS": "SCIMプロビジョニング ステータス", "PROVISIONING_SETTINGS": "プロビジョニング設定", "JIT_PROVISIONING": "ジャストインタイム(JIT)プロビジョニング", "ENABLE_FOR_JIT_PROVISIONING": "JITプロビジョニングの有効化", "JIT_ATTRIBUTE_MAPPING": "JIT属性マッピング", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "ジャストインタイム属性マッピング", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "ジャストインタイム ユーザー グループ属性", "JUST_IN_TIME_ATTRIBUTE": "ジャストインタイム属性", "SAML_ATTRIBUTE_MAPPING": "SAML属性マッピング", "USER_GROUP_SAML_ATTRIBUTE": "ユーザー グループSAML属性", "SAML_ATTRIBUTE": "SAML属性", "USER_ATTRIBUTE": "ユーザー属性", "SCIM_PROVISIONING": "SCIMプロビジョニング", "SCIM_ATTRIBUTE_MAPPING": "SCIM属性マッピング", "SCIM_ATTRIBUTE": "SCIM属性", "SCIM_ENDPOINT_URL": "SCIMエンドポイントURL", "SESSION_ATTRIBUTE_MAPPING": "セッション属性マッピング", "IDP_ATTRIBUTE": "IdP属性", "AUTHENTICATION_METHOD": "認証方式", "TOKEN": "ベアラー トークン", "GENERATE_TOKEN": "トークンを発行", "INPUT_METHOD": "入力方法", "FETCH_WITH_URL": "メタデータURL", "UPLOAD_METADATA": "メタデータをアップロード", "MANUAL_ENTRY": "手動で入力", "UPLOAD_CERTIFICATE": "証明書をアップロード", "BEARER_TOKEN": "ベアラー トークン", "IDP_ADVANCED_SETTINGS": "IdP詳細設定", "SESSION_TIMEOUT": "セッション タイムアウト", "SESSION_TIMEOUT_DURATION_IN_MIN": "セッション タイムアウト期間(分単位)", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "管理者の非アクティブ タイムアウト期間(分単位)", "SERVICE_ENROLLMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "サービス登録セッションのタイムアウト(分単位)", "NONE": "なし", "TIME_RANGE": "時間範囲", "ACTION": "アクション", "CATEGORY": "カテゴリー", "SUB_CATEGORY": "サブカテゴリー", "INTERFACE": "インターフェイス", "RESULT": "結果", "SCOPE": "範囲", "SCOPES_N_ROLES": "範囲とロール", "ADD_ITEMS": "項目を追加", "TIME_STAMP": "タイム スタンプ", "RESOURCE": "リソース", "ADMIN_ID": "管理者ID", "CLIENT_IP": "クライアントIP", "AM": "AM", "TIME_PM": "PM", "START_TIME": "開始時刻", "END_TIME": "終了時刻", "CURRENT_DAY": "今日", "CURRENT_WEEK": "現在の週", "CURRENT_MONTH": "現在の月", "PREVIOUS_DAY": "前日", "PREVIOUS_WEEK": "前の週", "PREVIOUS_MONTH": "前月", "HELP_BROWSER": "ヘルプ", "SELECT_DATE": "日付を選択", "CLOSE": "閉じる", "ACCEPT": "承諾", "SYNC_DOMAINS": "ドメインを同期", "IMPORT_RESULTS": "インポート結果", "COMPLETE": "完了", "PROCESSED_RECORD": "処理済みレコード", "TOTAL_RECORDS_ADDED": "追加されたレコードの合計", "TOTAL_RECORDS_DELETED": "削除されたレコードの合計", "TOTAL_RECORDS_IN_IMPORT": "インポートされたレコードの合計", "TOTAL_RECORDS_UPDATED": "更新されたレコードの合計", "FAILED_RECORDS": "失敗したレコード", "DUPLICATE_ITEM": "入力された名前はすでに使用されています", "RESOURCE_NOT_FOUND": "リソースが見つかりません", "CSV_FORMAT_INVALID": "無効なCSV形式", "UNEXPECTED_ERROR": "予期しないエラー", "DUPLICATE_RECORD": "重複レコード", "CONFIGURATION_CHANGES": "構成の変更", "PRE_CHANGES_CONFIGURATION": "変更前の構成", "POST_CHANGES_CONFIGURATION": "変更後の構成", "VIEW_CHANGES": "変更内容を表示", "OPEN_HELP_BROWSER": "ヘルプ ブラウザーを開く", "ALL": "すべて", "SIGN_IN": "サイン イン", "SIGN_OUT": "サイン アウト", "CREATE": "作成", "GET": "取得", "SEARCH": "検索", "BULK": "一括", "DOWNLOAD": "ダウンロード", "UPLOAD": "アップロード", "USER_MANAGEMENT": "ユーザー管理", "LOGIN": "ログイン", "AUTHENTICATION_SETTINGS": "認証構成", "TENANTS": "テナント", "USER": "ユーザー", "CUSTOM_USER_ATTRIBUTE": "カスタム ユーザー属性", "PASSWORD_CHANGE": "パスワードの変更", "PASSWORD_POLICY_CHANGE": "パスワード ポリシーの変更", "IDENTITY_PROVIDERS": "IDプロバイダー", "ADVANCE_SETTINGS": "詳細設定", "ADMIN_SIGN_ON": "管理者サインオン", "SIGN_ON_POLICY": "サインオン ポリシー", "SERVICE_ASSIGNMENT": "サービスの割り当て", "SERVICES": "サービス", "SCIM_API": "SCIM API", "FAILURE": "失敗", "PARTIALLY_FAILED": "一部失敗", "REMOVE_PAGE": "ページを削除", "REMOVE_ALL": "すべて削除", "CONFIRMATION_REMOVE_PAGE": "ページを削除", "CONFIRMATION_REMOVE_ALL": "すべて削除", "CONFIRM": "確認", "CUSTOMISE_COLUMNS": "列のカスタマイズ", "ALL_CHANGES_SAVED": "すべての変更が保存されました", "ITEM_HAS_BEEN_DELETED": "その項目は削除されました", "ITEMS_HAVE_BEEN_DELETED": "項目が削除されました", "START_TIME_BEFORE_END_TIME": "開始時刻は終了時刻より前にする必要があります", "USER_GROUPS_ASSIGNMENT_LIMIT": "一度に{{value}}個以上のグループを割り当てることはできません", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "一度に{{value}}個以上のロールをユーザーまたはグループに割り当てることはできません", "VERIFY_EMAIL_ADDRESS": "メール アドレスを確認", "ENTER_EMAIL_OTP": "メールOTPを入力", "SET_UP": "セットアップ", "EMAIL_OTP": "メールOTP", "NO_MATCHING_ITEMS_FOUND": "一致する項目が見つかりません", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "ベアラー トークンが正常に生成されました", "MSG_EMAIL_OTP_SENT": "メールOTPが{{email}}に送信されました", "MSG_INVALID_OTP": "入力されたOTPが正しくありません", "DIDNT_RECEIVE": "受信していない場合", "SELECTED_CRITERIA_INVALID": "選択した条件が無効です", "ALLOW_FIDO_AS_PRIMARY": "FIDO2をプライマリー オーセンティケーターとして許可", "ALLOW_EMAIL_OTP_AS_PRIMARY": "メールOTPをプライマリー オーセンティケーターとして許可", "AUTHENTICATORS": "オーセンティケーター", "MFA_AUTHENTICATOR": "MFAオーセンティケーター", "LAST_USED": "最終使用", "DELETE_AUTHENTICATOR": "MFAオーセンティケーターを削除", "RESTART_TENANT_CREATION": "テナント作成を再開", "CONTINUE": "続行", "PARTIAL_TENANT_MSG": "Zscalerテナントのセットアップが一部完了しました。", "CLICK_CONTINUE_MSG": "セットアップを完了するには、[続行]をクリックしてください。", "DISABLE_MULTIFACTOR": "多要素認証を無効化", "DISABLE_FIDO": "FIDO2を無効化", "DISABLE_FIDO_MSG": "認証にセキュリティ キーまたは生体認証のみを使用している場合は、アカウントからロックアウトされる可能性があります。MFAを無効にすると、パスワードによる認証がアカウントにアクセスする唯一の方法になります。パスワードを忘れた場合、またはまだ設定していない場合は、パスワードのリセットをリクエストしてください。", "DISABLE_MFA_MSG": "多要素認証の無効化は、全体に影響する変更です。これにより、ユーザーのセキュリティが低下します。続行してもよろしいですか？", "ADMINISTRATIVE": "管理", "SERVICE_ENTITLEMENTS": "サービス エンタイトルメント", "ADMINISTRATIVE_ENTITLEMENTS": "管理エンタイトルメント", "ORGANIZATION_ID": "組織ID", "GROUPS": "グループ", "ASSIGN_USER": "ユーザーの割り当て", "SELECT_USERS_AND_ROLE": "ユーザーとロールの選択", "SUMMARY": "概要", "ASSIGN": "割り当て", "SELECT_GROUPS_AND_ROLE": "グループとロールの選択", "SERVICE_NAME": "サービス名", "LOGIN_NAME": "ログイン名", "ADD_ROLE": "ロールを追加", "EDIT_ROLE": "ロールを編集", "USERS_AND_GROUPS_TEXT": "ユーザーとグループ", "USERS_CREDENTIALS": "ユーザー資格情報", "AUTHENTICATION_SESSION": "認証セッション", "AUTHENTICATION_SESSION_FOR_SERVICE_ENROLLMENT": "サービス登録の認証セッション", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "プライベート アクセスの再認証に対して認証を強制", "ZSLOGIN_AS_IDENTITY_PROVIDER": "ZSLoginをIDプロバイダーとして使用", "AZURE_AD_AS_TENANT": "Azure ADテナントA (OIDC)", "OKTA_TENANT": "OktaテナントB (SAML)", "PING_IDENTITY": "Ping IdentityテナントC (SAML)", "DOMAINS": "ドメイン", "SERVICE_ENTITLEMENTS_TEXT": "サービス エンタイトルメント", "AUTHENTICATION_EVENT_LOG": "認証イベント ログ", "DELETE_ROLE": "ロールを削除", "ENTITLEMENTS": "エンタイトルメント", "ENTITLEMENTS_LABEL": "エンタイトルメント", "USER_ID": "ユーザーID", "REMOVE_ASSIGNMENTS": "割り当てを削除", "SELECT_USERS": "ユーザーを選択", "ASSIGN_GROUP": "グループの割り当て", "SELECT_GROUPS": "グループを選択", "SAME_ROLE_FOR_SELECTED_USERS": "選択したすべてのユーザーに同じロールを設定", "SAME_ROLE_FOR_SELECTED_GROUPS": "選択したすべてのグループに同じロールを設定", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "選択したすべてのユーザーに同じロールと範囲を設定", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "選択したすべてのグループに同じロールと範囲を設定", "ATTRIBUTE_ALREADY_MAPPED": "属性{{value}}はすでにマッピングされています", "MFA_TOGGLE_TOOLTIP": "多要素認証を有効にすると、ユーザーはパスワードと合わせてセカンダリー認証要素を選択できます。", "FIDO_TOGGLE_TOOLTIP": "FIDO2をプライマリーとして有効にすると、ユーザーはパスワードを省略し、認証方式として生体認証やセキュリティ キーを使用できるようになります。", "LINKED_TENANTS": "リンクされたサービス", "SELECT_ROLE": "ロールを選択", "SELECT_SCOPE": "範囲を選択", "ALL_USERS": "すべてのユーザー", "ASSIGNED_USERS": "割り当てられたユーザー", "UNSELECTED_ROLES": "未選択のロール", "SELECTED_ROLES": "選択済みのロール", "MANAGE": "管理", "EDIT_NAME": "名前を編集", "VIEW_ROLES": "ロールを表示", "DEVICE_GROUP_RESTRICTIONS": "デバイス グループの制限", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "デバイス グループの制限を管理", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "デバイス グループの制限を有効化", "DEVICE_GROUPS": "デバイス グループ", "ORGANIZATION_NAME": "組織名", "DEVICE_GROUP_ASSIGNMENT": "デバイス グループの割り当て", "SERVICE_RUNTIME_ASSIGNMENT": "サービス ランタイムの割り当て", "ROLE_ASSIGNMENT": "ロールの割り当て", "DELETE_ASSIGNMENT": "割り当てを削除", "NO_RECORD_EXISTS": "レコードが存在しません", "SERVICE": "サービス", "REMOTE_ASSISTANCE": "リモート アシスタンス", "CXO_INSIGHT": "CXO向けインサイト", "VIEW": "ビュー", "ACCESS_TYPE": "アクセスの種類", "FULL_ACCESS": "フル アクセス", "ENABLE_FULL_ACCESS": "フル アクセスを有効化", "ACCESS_VALID_UNTIL": "アクセス有効期限", "VIEW_ONLY_ACCESS": "表示専用アクセス", "ENABLE_VIEW_ONLY_ACCESS": "表示専用アクセスを有効化", "DEVICE_TOKEN": "デバイス トークン", "TOKEN_VALIDATORS": "トークン バリデーター", "TOKEN_VALIDATOR": "トークン バリデーター", "ADD_TOKEN_VALIDATOR": "トークン バリデーターを追加", "EDIT_TOKEN_VALIDATOR": "トークン バリデーターを編集", "DELETE_TOKEN_VALIDATOR": "トークン バリデーターを削除", "AUDIENCE": "オーディエンス", "SUBJECT_CLAIM": "サブジェクト クレーム", "CLIENT_JWKS": "クライアントJWKs", "CERTIFICATES_PUBLIC_KEYS": "証明書と公開キー", "CLIENT_JWKS_URL": "クライアントJWKs URL", "VALIDATION_TYPE": "検証タイプ", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Client Connectorデバイス トークン", "GUEST_DOMAINS": "ゲスト ドメイン", "ARBITRARY_GUEST_DOMAINS": "任意のゲスト ドメイン", "GUEST_USER": "ゲスト ユーザー", "GUEST_DOMAIN": "ゲスト ドメイン", "ADD": "追加", "DEPARTMENT": "部署", "ENABLE_TO_ALLOW_ALL_DOMAINS": "有効にするとすべてのドメインが許可されます", "CUSTOM_ATTRIBUTE": "カスタム属性", "BRANDING": "ブランディング", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "送信元メール アドレスのカスタマイズ", "CONFIGURATION_SETTINGS": "構成設定", "CUSTOMIZE_EMAIL_SUBJECT": "メールの件名のカスタマイズ", "EMAIL_FROM_ADDRESS": "送信元メール アドレス", "EMAIL_SUBJECT": "メールの件名", "EDIT_LOGO": "ロゴを編集", "LOGO": "ロゴ", "UPLOAD_LOGO": "ロゴをアップロード", "INVALID_IMAGE_TYPE": "無効な画像タイプ", "BASE_64_CONVERSION_FAILED": "画像をBase 64エンコード バージョンに変換できません", "DEPARTMENTS": "部署", "ADD_DEPARTMENT": "部署を追加", "EDIT_DEPARTMENT": "部署を編集", "DELETE_DEPARTMENT": "部署を削除", "IMPORT_DEPARTMENT": "部署をインポート", "DEPARTMENT_NAME": "部署名", "VIEW_USER": "ユーザーを表示", "VIEW_GROUP": "ユーザー グループを表示", "DASHBOARD": "ダッシュボード", "CREATED": "作成済み", "UPDATED": "更新済み", "DELETED": "削除済み", "EXTERNAL_IDP": "外部IdP", "HOSTED": "ホスト済み", "TOTAL": "合計", "CSV": "CSV", "MANUAL": "手動", "EMAIL_LOGIN": "メールOTP", "FIDO_LOGIN": "FIDO認証", "PWD_LOGIN": "パスワード", "TOTP_LOGIN": "TOTPログイン", "SMS_LOGIN": "SMSログイン", "SAML_LOGIN": "SAML", "OIDC_LOGIN": "OIDC", "JIT": "JIT", "SCIM": "SCIM", "ADMIN_ASSIGNMENT": "管理者の割り当て", "RUNTIME_ASSIGNMENT": "ランタイムの割り当て", "ZCC_ASSIGNMENT": "ZCCの割り当て", "ONE_DAY": "1日", "TWO_DAYS": "2日", "SEVEN_DAYS": "7日", "FOURTEEN_DAYS": "14日", "THIRTY_DAYS": "30日", "SIXTY_DAYS": "60日", "NINTY_DAYS": "90日", "NO_DATA_AVAILABLE": "利用可能なデータがありません", "ZSLOGIN_FOR_ADMINISTRATORS": "管理者用ZSLogin", "ZIDENTITY_FOR_ADMINISTRATORS": "管理者用ZIdentity", "REVERT_TO_PROVISIONED": "プロビジョニング済みに戻す", "REVERT": "戻す", "ZSLOGIN_MIGRATION_STATUS": "ZSLogin移行ステータス", "ZIDENTITY_MIGRATION_STATUS": "ZIdentity移行ステータス", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "外部IDプロバイダーの構成の検証に失敗しました", "UPDATE_PSEUDO_DOMAIN": "疑似ドメインを更新", "CURRENT_PSEUDO_DOMAIN_NAME": "現在の疑似ドメイン名", "NEW_PSEUDO_DOMAIN_NAME": "新しい疑似ドメイン名", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "疑似ドメインの更新に成功しました", "MULTIFACTOR_BYPASS": "多要素認証のバイパス", "SKIP_SECOND_FACTOR_AUTHENTICATION": "第二要素の認証をスキップ", "SKIP_UNTIL": "スキップ期限", "INTEGRATION": "統合", "API_CLIENTS": "APIクライアント", "API_RESOURCES": "APIリソース", "ADD_API_CLIENT": "APIクライアントを追加", "ADD_API_RESOURCE": "APIリソースを追加", "EDIT_API_RESOURCE": "APIリソースを編集", "API_RESOURCE": "APIリソース", "ACCESS_TOKEN_VALIDITY": "アクセス トークンの有効期限", "MINUTES": "分", "HOURS": "時間", "PRIVATE_KEY_JWT": "秘密キーJWT", "CLIENT_JWK_URL": "クライアントJWKs URL", "CERTIFICATE_PUBLIC_KEY": "証明書/公開キー", "CLIENT_JWK": "クライアントJWK", "CLIENT_SECRETS": "クライアント シークレット", "CLIENT_AUTHENTICATION": "クライアント認証", "CLIENT_INFORMATION": "クライアント情報", "ZSCALER_API": "Zscaler API", "RESOURCE_NAME": "リソース名", "NEVER": "なし", "CERTIFICATES_AND_PUBLIC_KEYS": "証明書と公開キー", "DELETE_API_CLIENT": "APIクライアントを削除", "DELETE_API_CLIENT_MESSAGE": "このAPIクライアントを削除してもよろしいですか？この変更を元に戻すことはできません。", "ACCESS_TOKEN_REQUIRED_MESSAGE": "アクセス トークンの有効期間は必須です。", "EXPIRES": "有効期限", "SECRET": "シークレット", "EDIT_API_CLIENT": "APIクライアントを編集", "VIEW_API_CLIENT": "APIクライアントを表示", "CREATED_ON": "作成日", "EXPIRES_ON": "有効期限", "API_CLIENT": "APIクライアント", "CLIENT_SECRET_WARNING": "クライアント シークレットをコピーしてください。後で表示されることはありません。", "API_CLIENTS_AND_RESOURCES": "APIクライアントとリソース", "LOGIN_ID_ATTRIBUTE": "ログインID属性", "LOGOUT": "ログアウト", "ENVIRONMENT": "環境", "SYSTEM": "システム", "ZIDENTITY_ROLES": "ZIdentityロール", "CLIENT": "クライアント", "RESOURCES": "リソース", "BASIC": "基本", "ADVANCED": "詳細", "PROVISIONING": "プロビジョニング", "ZIDENTITY_DASHBOARD": "ZIdentityダッシュボード", "AUTHENTICATION_LEVELS": "認証レベル", "LEVEL_NAME": "レベル名", "VALIDITY": "有効性", "DURATION": "期間", "MESSAGE_TO_USER": "ユーザーへのメッセージ(省略可)", "PARENT": "親", "SUB_LEVEL_NAME": "サブレベル名", "DAYS": "日", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "レベルから認証コンテキストへのマッピング", "LEVELS": "レベル", "AUTHENTICATION_CONTEXT": "認証コンテキスト", "DELETE_AL": "認証レベルを削除", "DELETE_AL_CONFIRM_MSG": "このアクションを元に戻すことはできず、認証レベルが完全に削除されます。続行してもよろしいですか？", "MOVE": "移動"}