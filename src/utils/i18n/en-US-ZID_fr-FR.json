{"LOCALE": "en-US", "EMAIL_ADDRESS": "Adresse e-mail", "EMAIL_PLACEHOLDER": "Entrez votre adresse e-mail...", "LOGIN_ID_PLACEHOLDER": "Entrez votre identifiant de connexion...", "LOGIN_ID_LABEL": "ID de connexion", "LOGIN_PASSWORD_PLACEHOLDER": "Entrez votre mot de passe...", "PASSWORD_LABEL": "Mot de passe", "NEW_PASSWORD_LABEL": "Nouveau mot de passe", "OLD_PASSWORD_LABEL": "Ancien mot de passe", "CONFIRM_NEW_PASSWORD_LABEL": "Confirmez le nouveau mot de passe", "PASSWORD_PLACEHOLDER": "Entrez le mot de passe", "FORGOT_PASSWORD": "Mot de passe oublié ?o", "REMEMBER_ME": "Se souvenir de moi", "RESET_PASSWORD": "Réinitialiser le mot de passe", "CHANGE_PASSWORD": "Changer le mot de passe", "SUCCESS": "Réussite", "RESEND": "<PERSON><PERSON><PERSON>", "SIGN_IN_LABEL": "Connexion", "INITIAL_DOMAIN_NAME": "Nom de domaine initial", "FIRST_NAME": "Prénom", "LAST_NAME": "Nom", "PHONE_NUMBER": "Numéro de téléphone", "REQUIRED_VALIDATION_MESSAGE": "Obligatoire(e)", "INVALID_CREDENTIALS_MESSAGE": "identifiant de connexion ou mot de passe non valide", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "Un auditeur ne peut pas se connecter ici.", "LANGUAGE": "<PERSON><PERSON><PERSON> (États-Unis)", "COOKIES_DISABLED": "Les cookies doivent être autorisés pour utiliser cette application. Veuillez activer la prise en charge des cookies dans votre navigateur pour ce site.", "COOKIES_NOT_ALLOWED": "Cookies non autorisés", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "Ce navigateur n'est pas pris en charge et peut compromettre la fonctionnalité de ce site. Nous vous conseillons de mettre à jour votre navigateur vers la version la plus récente. Pour ignorer ce message, cliquez sur OK.", "BROWSER_NOT_SUPPORTED": "Version du navigateur non prise en charge", "OK": "OK", "NEXT": "Suivant", "BACK": "Retour", "SAVE": "Enregistrer", "SAVE_AND_NEXT": "Enregistrer et Suivant", "COPYRIGHT": "<PERSON><PERSON> d'auteur", "COPYRIGHT_STATEMENT": "Tous droits réservés.", "POWERED_BY": "Reposant sur", "ADMIN_PORTAL_SIGN_IN": "Connexion au portail d'administration", "ALL_ORGANIZATIONS": "Toutes les entreprises", "SEARCH_PLACEHOLDER": "Rechercher...", "GENERAL": "Général", "ZPA": "Accès privé <PERSON>", "IAM": "Gestion de l’identité et de l’accès", "ZIA": "Zscaler Internet Access", "ZDX": "Expérience numérique <PERSON>", "EC": "Cloud & Branch Connector", "CCP": "Portail de connexion des clients", "ZBI": "Isolation du navigateur", "BI": "Perspectives business", "ZRA": "Risk360", "LAUNCH": "Lancement", "VERIFY": "Vérifier", "CANCEL": "Annuler", "DONE": "<PERSON><PERSON><PERSON><PERSON>", "RESET": "Réinitialiser", "CLEAR": "<PERSON><PERSON><PERSON><PERSON>", "CLEAR_ALL": "Tout effacer", "MY_PROFILE": "Mon profil", "ZS_LOGIN": "ZSL<PERSON>in", "Z_IDENTITY": "ZIdentity", "ACCOUNT_MANAGEMENT": "Gestion de compte", "CLOUD_CONFIGURATION": "Configuration du cloud", "PRODUCT": "Produit", "CLOUD_NAME": "Nom du cloud", "CLOUD_ID": "Identifiant cloud", "ORG_NAME": "Nom de l'entreprise", "LINKED_SERVICES": "Services liés", "LINK": "<PERSON><PERSON>", "UNLINK": "Dissocier", "LINKED": "<PERSON><PERSON><PERSON><PERSON>(s)", "UNLINKED": "Dissoci<PERSON>(s)", "LINK_TENANT": "Associer le locataire", "UNLINK_TENANT": "Dissocier le locataire", "ATTRIBUTE": "Attribut", "ATTRIBUTES": "Attributs", "USER_ATTRIBUTES": "Attributs de l'utilisateur", "SESSION_ATTRIBUTE": "Attribut de session", "ADVANCED_SETTINGS": "Paramètres avancés", "DIRECTORY": "Répertoire", "USERS": "Utilisateurs", "USER_GROUPS": "Groupes de l'utilisateur", "EXTERNAL_IDENTITIES": "Identités externes", "APPLICATIONS": "Applications", "ZSCALER_SERVICES": "Zscaler Services", "SECURITY": "Sécurité", "IP_LOCATIONS": "Emplacements IP", "IP_LOCATIONS_AND_GROUPS": "Emplacements & groupes IP", "IP_LOCATION_GROUPS": "Groupes d'emplacements IP", "ADMINISTRATION": "Administration", "ADMINISTRATION_CONTROLS": "Contr<PERSON>les administratifs", "AUTHENTICATION": "Authentification", "AUTHENTICATION_METHODS": "Méthodes d'authentification", "ENABLE_MULTIFACTOR_SETTINGS": "Activer les paramètres à plusieurs facteurs", "ENABLE_MULTIFACTOR_AUTH_USER": "Activer l'authentification à plusieurs facteurs (MFA) pour l'inscription au service", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "Activer l'authentification à plusieurs facteurs (MFA) pour les administrateurs", "MFA_ENROLLMENT_GRACE_PERIOD": "<PERSON><PERSON><PERSON> pour l'inscription MFA (en jours)", "AUDIT_LOGS": "Journaux d'audit", "ADD_USER": "Ajouter un utilisateur", "IMPORT_CSV": "Importer CSV", "ACTIONS": "Actions", "ACTIVATE": "Activer", "DE_ACTIVATE": "Désactiver", "BULK_ACTIVATE": "Activation en masse", "BULK_DEACTIVATE": "Désactivation en masse", "BULK_DELETE": "Suppression en masse", "DELETE": "<PERSON><PERSON><PERSON><PERSON>", "NAME": "Nom", "GROUP": "Groupe", "SELECT": "Choi<PERSON>", "LOGIN_ID": "ID de connexion", "STATUS": "Statut", "GENERAL_INFORMATION": "Informations générales", "MY_ACCOUNT": "Mon compte", "FULL_NAME": "Nom complet", "VERIFIED": "Vérifié", "LANGUAGE_LABEL": "<PERSON><PERSON>", "TIMEZONE": "<PERSON><PERSON> ho<PERSON>", "ACTIVE": "Actif", "INACTIVE": "Inactif(e)", "USER_INFORMATION": "Informations sur l'utilisateur", "PRIMARY_EMAIL": "Adresse e-mail principale", "SAME_AS_LOGIN_ID": "Identique à l'ID de connexion", "SECONDARY_EMAIL": "Adresse e-mail secondaire", "ADDITIONAL_ATTRIBUTES": "Attributs supplémentaires", "SECURITY_SETTINGS": "Paramètres de sécurité", "PASSWORD_OPTION": "Option de mot de passe", "PROMPT_PASSWORD_FIRST_LOGIN": "Invitation à changer de mot de passe après la première connexion", "CONFIRM_PASSWORD": "Confirmer le mot de passe", "ASSIGNMENT": "Attribution", "ASSIGN_GROUPS": "Attribuer des groupes", "SET_BY_ADMIN": "Définie par l'administrateur", "SET_BY_USER": "Définie par l'utilisateur", "AUTO_GENERATED": "Génération automatique", "DELETE_USER": "Supprimer l'utilisateur", "EDIT_USER": "Modifier l’utilisateur", "UPDATE": "Mettre à jour", "OFF": "<PERSON><PERSON><PERSON><PERSON>", "ON": "Activé(e)", "ADD_ATTRIBUTE": "Ajouter un attribut", "IMPORT_ATTRIBUTE": "Importer des attributs", "CREATE_NEW_ATTRIBUTES": "<PERSON><PERSON>er de nouveaux attributs", "TABLE_NUMBER": "Nº", "DISPLAY_NAME": "A<PERSON>iche<PERSON> le nom", "ATTRIBUTE_NAME": "Nom de l'attribut", "DATA_TYPE": "Type de données", "TABLE_REQUIRED": "Obligatoire(e)", "ORIGIN": "Origine", "SYSTEM_DEFINED": "défini par le système", "USER_DEFINED": "Défini par l’utilisateur", "ENABLE": "Activer", "ENABLED": "Activé", "DISABLE": "Désactiver", "DISABLED": "Désactivé", "INFORMATION": "Informations", "ENTER_TEXT": "Entrez le texte...", "ATTRIBUTE_REQUIRED": "Attribut obligatoire", "DESCRIPTION_OPTIONAL": "Description (facultatif)", "DESCRIPTION": "Description", "EDIT_ATTRIBUTE": "Modifier l'attribut", "DELETE_ATTRIBUTE": "Supprimer l'attribut", "ADD_SESSION_ATTRIBUTE": "Ajouter un attribut de session", "EDIT_SESSION_ATTRIBUTE": "Modifier l'attribut de session", "DELETE_SESSION_ATTRIBUTE": "Supprimer l'attribut de session", "ADD_GROUP": "Ajouter un groupe", "ADD_USER_GROUP": "Ajouter un groupe d'utilisateurs", "GROUP_NAME": "Nom du groupe", "USER_NAME": "Nom d'utilisateur", "EDIT_GROUP": "Modifier le groupe", "DELETE_GROUP": "Supprimer le groupe", "USER_GROUP_NAME": "Nom du groupe d'utilisateurs", "ASSIGN_USERS": "Attribuer des utilisateurs", "SOURCE": "Source", "STRING": "<PERSON><PERSON><PERSON>", "INTEGER": "<PERSON><PERSON>", "BOOLEAN": "Booléen", "DATE": "Date", "DECIMAL": "Décimal", "CHANGE_PASSWORD_SETTINGS": "Modifier les paramètres du mot de passe", "ZS_SERVICES": "Zscaler Services", "CLOUD_AND_ORG_ID": "Identifiant du Cloud et de l'entreprise", "APPLICATION_DETAIL": "Détails de l'application", "CLONE_MEMBERSHIP_FOR_APPLICATION": "Cloner l'adhésion à partir de cette application", "ROLE": "R<PERSON><PERSON>", "ROLES": "<PERSON><PERSON><PERSON>", "MANAGE_ROLES": "<PERSON><PERSON><PERSON> rôles", "SYNC": "Sync", "USERS_ASSIGNMENT": "Attribution des utilisateurs", "GROUPS_ASSIGNMENT": "Attribution des groupes d'utilisateurs", "ASSIGN_ROLE": "Attribuer un rôle", "ASSIGN_SCOPE": "Attribuer un périmètre", "ASSIGN_USER_GROUPS": "Attribution des groupes", "ADD_MORE": "Ajouter d'autres éléments", "ASSIGN_USERS_AND_GROUPS": "Attribuer des utilisateurs et des groupes d'utilisateurs", "REVIEW": "Examiner", "USERS_AND_GROUPS": "Utilisateurs et groupes d'utilisateurs", "TYPE": "Action liée à la politique", "EDIT_USERS_ROLE": "Modifier le rôle", "EUSA_AGREEMENT": "Contrat d'abonnement utilisateur", "DELETE_USERS_ROLE": "Supprimer l'utilisateur", "EDIT_GROUPS_ROLE": "Modifier le rôle", "DELETE_GROUPS_ROLE": "Supprimer le groupe", "BULK_DELETE_USERS_GROUPS_ROLE": "Suppression en masse d'utilisateurs et de groupes", "CONFIGURATION_TYPE": "Type de configuration", "RECOMMENDED": "Recommandé(e)", "DEFAULT": "<PERSON><PERSON> <PERSON><PERSON>", "CUSTOM": "<PERSON><PERSON><PERSON><PERSON>", "PASSWORD_COMPLEXITY": "Complexité du mot de passe", "PASSWORD_LENGTH": "Longueur du mot de passe", "MIN_LWR_CASE": "Nombre minimal de lettres minuscules", "MIN_UPPR_CASE": "Nombre minimal de lettres majuscules", "MIN_NUMERIC": "Nombre minimal de caractères numériques", "MIN_SPL_CHAR": "Nombre minimal de caractères spéciaux", "PASSWORD_DOES_NOT_INCLUDE": "Le mot de passe ne peut pas inclure le nom de l'entreprise, le nom d'utilisateur, le prénom ou le nom de famille.", "REJECT_REUSE": "Refuser la réutilisation des 5 derniers mots de passe", "DEACTIVATE_USER": "Désactiver l'utilisateur après 10 tentatives infructueuses", "ALLOW_ADMIN_SET_PASSWORD": "Permettre à l'administrateur de créer ou modifier le mot de passe de l'utilisateur", "FORCE_PASSWORD_CHANGE": "Imposer le renouvellement du mot de passe après la première connexion", "PASSWORD_EXPIRY": "Période d'expiration du mot de passe (en jours)", "PASSWORD_CRITERIA": "Critères du mot de passe", "POLICY": "Politique", "POLICIES": "Stratégies", "AUTHENTICATION_POLICY": "Politique d'authentification", "PASSWORD_POLICY": "Politique en matière de mots de passe", "LOAD_MORE": "Charger plus", "EDIT_EMAIL": "Modifier l'adresse e-mail", "UPDATE_EMAIL": "Mettre à jour l'adresse e-mail", "NEW_EMAIL": "Entrer une nouvelle adresse e-mail", "VALIDATE_EMAIL": "Valider l'adresse e-mail", "VALIDATION_CODE": "Entrer le code de validation", "RESEND_CODE": "Renvoyer le code", "CURRENT_PASSWORD": "Mot de passe actuel", "NEW_PASSWORD": "Nouveau mot de passe", "CONFIRM_NEW_PASSWORD": "Confirmez le nouveau mot de passe", "MIN_LENGTH_REQUIRED": "{{value}} caractères minimum", "MIN_LOWER_CASE_REQUIRED": "Au moins {{value}} lettre minuscule (a-z)", "MIN_UPPER_CASE_REQUIRED": "Au moins {{value}} lettre majuscule (A-Z)", "MIN_NUMERIC_REQUIRED": "Au moins {{value}} caractère numérique (0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "Au moins {{value}} caractère spécial", "SIGN_ON_POLICIES": "Politique d'ouverture de session par l'administrateur", "ADD_RULE": "Ajouter une règle", "RULE_ORDER": "<PERSON><PERSON>", "RULE_STATUS": "Statut de règle", "RULE_NAME": "Nom de la règle", "RULE_ACTION": "Action de règle", "CRITERIA": "Critères", "ADD_SIGN_ON_POLICY": "Ajouter une politique d'ouverture de session par l'administrateur", "EDIT_SIGN_ON_POLICY": "Modifier la politique d'ouverture de session par l'administrateur", "DELETE_SIGN_ON_POLICY": "Supprimer la politique d'ouverture de session par l'administrateur", "LOCATION": "Emplacement", "LOCATION_GROUP": "Groupe d'emplacements", "ALLOW": "Autoriser", "DENY": "Refuser", "OPERATIONS": "Opérations", "REMOVE": "<PERSON><PERSON><PERSON><PERSON>", "NO_DATA_FOUND": "Impossible de trouver des données", "NO_ITEMS_FOUND": "Aucun élément trouvé", "BROWSE_FILE": "<PERSON><PERSON><PERSON> le fichier", "IMPORT": "Importer", "NO_FILE_CHOSEN": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "OVERRIDE_EXISTING_ENTRIES": "Re<PERSON>lacer les entrées existantes", "CSV_FILE": "Fichier CSV", "SAMPLE_CSV_DOWNLOAD": "Télécharger l'échantillon", "IMPORT_USERS": "Importer des utilisateurs", "IMPORT_GROUPS": "Importer des groupes", "PRIMARY_IDENTITY_PROVIDER": "Fournisseur d'identité principal", "SECONDARY_IDENTITY_PROVIDER": "Fournisseurs d'identité secondaires", "ADD_PRIMARY_IDP": "Ajouter un IdP primaire", "ADD_SECONDARY_IDP": "Ajouter un IdP secondaire", "ADD_PRIMARY_IDENTITY_PROVIDER": "Ajouter un fournisseur d'identité principal", "EDIT_PRIMARY_IDENTITY_PROVIDER": "Modifier le fournisseur d'identité principal", "DELETE_PRIMARY_IDENTITY_PROVIDER": "Supp<PERSON>er le fournisseur d'identité principal", "ADD_SECONDARY_IDENTITY_PROVIDER": "Ajouter un fournisseur d'identité secondaire", "EDIT_SECONDARY_IDENTITY_PROVIDER": "Modifier le fournisseur d'identité secondaire", "DELETE_SECONDARY_IDENTITY_PROVIDER": "Supp<PERSON>er le fournisseur d'identité secondaire", "SAML_CONFIGURATION": "Configuration SAML ", "OIDC_CONFIGURATION": "CONFIGURATION OIDC", "IDENTITY_VENDOR": "Fournisseur d'identité", "DOMAIN": "Domaine", "YES": "O<PERSON>", "NO": "Non", "ADD_LOCATION": "Ajouter un emplacement", "EDIT_LOCATION": "Modifier l'emplacement", "DELETE_LOCATION": "Supprimer l'emplacement", "IMPORT_LOCATION": "Importer un emplacement", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "Voulez-vous vraiment supprimer cet emplacement ? Les modifications ne peuvent pas être annulées.", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "Voulez-vous vraiment supprimer ces emplacements en masse ? Les modifications ne peuvent pas être annulées.", "IP_ADDRESS": "Adresse IP", "COUNTRY": "Pays", "LOCATION_INFORMATION": "Informations sur l'emplacement", "NAME_REQUIRED_MESSAGE": "Le nom est obligatoire", "LOCATIONS_REQUIRED_MESSAGE": "L'emplacement est obligatoire", "COUNTRY_REQUIRED_MESSAGE": "Le pays est obligatoire", "IP_ADDRESS_REQUIRED_MESSAGE": "L'adresse IP est obligatoire", "LOCATION_COUNT": "Nombre d'emplacements", "LOCATIONS": "Emplacements", "UNSELECTED_LOCATIONS": "Emplacements non sélectionnés", "SELECTED_LOCATIONS": "Emplacements sélectionnés", "ADD_LOCATION_GROUP": "Ajouter un groupe d'emplacements", "EDIT_LOCATION_GROUP": "Modifier le groupe d'emplacements", "DELETE_LOCATION_GROUP": "Supprimer le groupe d'emplacements", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Voulez-vous vraiment supprimer ce groupe d'emplacements ? Les modifications ne peuvent pas être annulées.", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "Voulez-vous vraiment supprimer ces groupes d'emplacements en masse ? Les modifications ne peuvent pas être annulées.", "IMPORT_LOCATION_GROUP": "Importer un groupe d'emplacements", "PROTOCOL": "Protocole", "AUTHENTICATION_REQUEST": "Demande d'authentification", "ENABLE_SAML_REQUEST_SIGNING": "Activer la signature des demandes SAML", "SAML_REQUEST_SIGNING": "Signature de demande SAML", "LOGIN_HINT": "Indice de connexion", "SIGNING_ALGORITHM": "Algorithme de signature", "REQUEST_SIGNING_CERTIFICATE": "Demander un certificat de signature", "SP_SAML_CERTIFICATE": "Certificat SAML du fournisseur d'accès", "DOWNLOAD_CERTIFICATE": "Télécharger le certificat", "ENCRYPTED_SAML_RESPONSE": "Réponse SAML chiffrée", "ENCRYPTED_SAML_ASSERTION": "Assertion SAML chiffrée", "ENABLE_ENCRYPTED_SAML_ASSERTION": "Activer l'assertion SAML chiffrée", "CERTIFICATE": "Certificat", "SAML_ENCRYPTION_CERTIFICATE": "Certificat de chiffrement SAML", "IDP_METADATA": "Métadonnées de l'IdP", "IDP_METADATA_URL": "URL des métadonnées de l'IdP", "METADATA_URL": "URL des métadonnées", "IDP_CERTIFICATE": "Certificat d'IDP", "IDP_ENTITY_URI": "URI de l'entité IdP", "PRIMARY_IDP_NOT_AVAILABLE": "Le fournisseur d'identité principal n'a pas été ajouté.", "SECONDARY_IDP_NOT_AVAILABLE": "Le fournisseur d'identité secondaire n'est pas disponible", "SP_METADATA": "Métadonnées SP", "DOWNLOAD_SP_METADATA": "Télécharger les métadonnées SP", "IDP_SINGLE_SIGNON_URL": "URL d’authentification unique IdP", "REDIRECT_URI": "URI de redirection", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "Méthode d'authentification de l'endpoint du jeton", "ISSUER": "<PERSON><PERSON><PERSON>", "ZDK_INSTANCE": "Instance ZDK", "APPLICATION": "Application", "ADD_CLAIM": "Ajouter une réclamation", "VALUE": "<PERSON><PERSON>", "CLAIM_REQUIREMENTS": "Exigences en matière de réclamation", "SIGNATURE_VALIDATION": "Validation de signature", "EXPIRY": "Expiration", "AUTHORIZATION_ENDPOINT": "Endpoint d'autorisation", "TOKEN_ENDPOINT": "Endpoint de jeton", "JWKS_ENDPOINT": "Endpoint JWKS", "USER_INFORMATION_ENDPOINT": "Endpoint d'informations utilisateur", "CLIENT_SECRET_BASIC": "Secret du client - Basique", "CLIENT_SECRET_POST": "Secret du client - Poste", "CLIENT_ID": "Identifiant du client", "CLIENT_SECRET": "Secret du client", "REQUESTED_SCOPES": "<PERSON><PERSON><PERSON> demand<PERSON>", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "Contexte d'authentification demandé par défa<PERSON> (facultatif)", "SP_ENTITY_ID": "ID de l'entité SP", "SP_URL": "URL SP", "SCIM_PROVISIONING_STATUS": "État du provisionnement SCIM", "PROVISIONING_SETTINGS": "Paramètres de provisionnement", "JIT_PROVISIONING": "Provisionnement juste à temps (JIT)", "ENABLE_FOR_JIT_PROVISIONING": "Activer pour le provisionnement JIT", "JIT_ATTRIBUTE_MAPPING": "Mappage d'attributs JIT", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "Mappage d'attributs JUSTE À TEMPS", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "Attribut de groupe d'utilisateurs Juste à temps", "JUST_IN_TIME_ATTRIBUTE": "Attribut Juste à temps", "SAML_ATTRIBUTE_MAPPING": "Mappage d'attributs SAML", "USER_GROUP_SAML_ATTRIBUTE": "Attribut SAML du groupe d'utilisateurs", "SAML_ATTRIBUTE": "Attribut SAML", "USER_ATTRIBUTE": "Attribut de l'utilisateur", "SCIM_PROVISIONING": "Provisionnement SCIM", "SCIM_ATTRIBUTE_MAPPING": "Mappage d'attributs SCIM", "SCIM_ATTRIBUTE": "Attribut SCIM", "SCIM_ENDPOINT_URL": "URL d'endpoint SCIM", "SESSION_ATTRIBUTE_MAPPING": "Mappage des attributs de session", "IDP_ATTRIBUTE": "Attribut IdP", "AUTHENTICATION_METHOD": "Méthode d'authentification", "TOKEN": "Jeton du porteur", "GENERATE_TOKEN": "Générer un jeton", "INPUT_METHOD": "Méthode d'entrée", "FETCH_WITH_URL": "URL des métadonnées", "UPLOAD_METADATA": "Charger les métadonnées", "MANUAL_ENTRY": "<PERSON><PERSON>", "UPLOAD_CERTIFICATE": "Charger le certificat", "BEARER_TOKEN": "Jeton du porteur", "IDP_ADVANCED_SETTINGS": "Paramètres avancées de l'IdP", "SESSION_TIMEOUT": "<PERSON><PERSON><PERSON> d'expiration de la session", "SESSION_TIMEOUT_DURATION_IN_MIN": "<PERSON><PERSON><PERSON> du délai d'expiration de la session (en minutes)", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "Du<PERSON><PERSON> du délai d'expiration pour inactivité de l'administrateur (en minutes)", "SERVICE_ENROLLMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "D<PERSON>lai d'expiration de la session d'inscription au service (en minutes)", "NONE": "Aucun", "TIME_RANGE": "Plage de temps", "ACTION": "Action", "CATEGORY": "<PERSON><PERSON><PERSON><PERSON>", "SUB_CATEGORY": "Sous-cat<PERSON><PERSON><PERSON>", "INTERFACE": "Interface", "RESULT": "Résultat", "SCOPE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SCOPES_N_ROLES": "Étendues et rôles", "ADD_ITEMS": "Ajouter des éléments", "TIME_STAMP": "Horodatage", "RESOURCE": "Ressource", "ADMIN_ID": "Identifiant d'administrateur", "CLIENT_IP": "IP du client", "AM": "AM", "TIME_PM": "pm", "START_TIME": "<PERSON><PERSON> d<PERSON>", "END_TIME": "Heure de fin", "CURRENT_DAY": "<PERSON>jou<PERSON><PERSON>hui", "CURRENT_WEEK": "<PERSON><PERSON><PERSON> en cours", "CURRENT_MONTH": "Mois en cours", "PREVIOUS_DAY": "<PERSON><PERSON><PERSON>", "PREVIOUS_WEEK": "<PERSON><PERSON><PERSON>", "PREVIOUS_MONTH": "<PERSON><PERSON>", "HELP_BROWSER": "Aide", "SELECT_DATE": "Sélectionner la date", "CLOSE": "<PERSON><PERSON><PERSON>", "ACCEPT": "Accepter", "SYNC_DOMAINS": "Synchroniser les domaines", "IMPORT_RESULTS": "Importer des résultats", "COMPLETE": "<PERSON><PERSON><PERSON>", "PROCESSED_RECORD": "Enregistrements traités", "TOTAL_RECORDS_ADDED": "Nombre total d'enregistrements ajoutés", "TOTAL_RECORDS_DELETED": "Nombre total d'enregistrements supprimés", "TOTAL_RECORDS_IN_IMPORT": "Nombre total d'enregistrements dans l'importation", "TOTAL_RECORDS_UPDATED": "Nombre total d'enregistrements mis à jour", "FAILED_RECORDS": "Enregistrements en échec", "DUPLICATE_ITEM": "Le nom indiqué est déjà utilisé.", "RESOURCE_NOT_FOUND": "<PERSON><PERSON><PERSON> ressource trouvée", "CSV_FORMAT_INVALID": "Format CSV non valide", "UNEXPECTED_ERROR": "<PERSON><PERSON><PERSON> inattendue", "DUPLICATE_RECORD": "Enregistrement en double", "CONFIGURATION_CHANGES": "Modification dans la configuration", "PRE_CHANGES_CONFIGURATION": "Configuration avant les modifications", "POST_CHANGES_CONFIGURATION": "Configuration après les modifications", "VIEW_CHANGES": "Aff<PERSON><PERSON> les modifications", "OPEN_HELP_BROWSER": "<PERSON><PERSON><PERSON><PERSON><PERSON> le navigateur d'aide", "ALL": "Tous", "SIGN_IN": "Connexion", "SIGN_OUT": "Se déconnecter", "CREATE": "<PERSON><PERSON><PERSON>", "GET": "GET", "SEARCH": "<PERSON><PERSON><PERSON>", "BULK": "En masse", "DOWNLOAD": "Télécharger", "UPLOAD": "Charger", "USER_MANAGEMENT": "Gestion utilisateur", "LOGIN": "Connexion", "AUTHENTICATION_SETTINGS": "Paramètres d'authentification", "TENANTS": "Locataires", "USER": "Utilisa<PERSON>ur", "CUSTOM_USER_ATTRIBUTE": "Attribut utilisateur person<PERSON>", "PASSWORD_CHANGE": "Modification de mot de passe", "PASSWORD_POLICY_CHANGE": "Politique de modification des mots de passe", "IDENTITY_PROVIDERS": "Fournisseurs d'identité", "ADVANCE_SETTINGS": "Paramètres avancés", "ADMIN_SIGN_ON": "Ouverture de session de l'administrateur", "SIGN_ON_POLICY": "Politique d'ouverture de session", "SERVICE_ASSIGNMENT": "Affectation de service", "SERVICES": "Services", "SCIM_API": "API SCIM", "FAILURE": "Échec", "PARTIALLY_FAILED": "Échec partiel", "REMOVE_PAGE": "Supprimer la page", "REMOVE_ALL": "<PERSON>ut supprimer", "CONFIRMATION_REMOVE_PAGE": "Supprimer la page", "CONFIRMATION_REMOVE_ALL": "<PERSON>ut supprimer", "CONFIRM": "Confirmer", "CUSTOMISE_COLUMNS": "PERSONNALISER LES COLONNES", "ALL_CHANGES_SAVED": "Toutes les modifications ont été enregistrées", "ITEM_HAS_BEEN_DELETED": "L'élément a été supprimé", "ITEMS_HAVE_BEEN_DELETED": "Les éléments ont été supprimés", "START_TIME_BEFORE_END_TIME": "L'heure de début doit être antérieure à l'heure de fin", "USER_GROUPS_ASSIGNMENT_LIMIT": "Impossible d'attribuer plus de {{value}} groupes en une fois", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "Impossible d'attribuer plus de {{value}} rôles à des utilisateurs/groupes en une fois", "VERIFY_EMAIL_ADDRESS": "Vérifier l'adresse e-mail", "ENTER_EMAIL_OTP": "Entrer l'OTP d'e-mail", "SET_UP": "Configurer", "EMAIL_OTP": "OTP d'e-mail", "NO_MATCHING_ITEMS_FOUND": "Impossible de trouver un élément correspondant", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "Le jeton du porteur a été généré", "MSG_EMAIL_OTP_SENT": "OTP d'e-mail envoy<PERSON> <PERSON> {{email}}", "MSG_INVALID_OTP": "L'OTP entré est incorrect", "DIDNT_RECEIVE": "Vous ne l'avez pas reçu ?", "SELECTED_CRITERIA_INVALID": "Le critère sélectionné n'est pas valide", "ALLOW_FIDO_AS_PRIMARY": "Autoriser FIDO2 comme authentificateur principal", "ALLOW_EMAIL_OTP_AS_PRIMARY": "Autoriser l'OTP d'e-mail comme authentificateur principal", "AUTHENTICATORS": "Authentificateurs", "MFA_AUTHENTICATOR": "Authentificateur MFA", "LAST_USED": "Dernière utilisation", "DELETE_AUTHENTICATOR": "Supprimer l'authentificateur MFA", "RESTART_TENANT_CREATION": "Redémarrer la création de locataires", "CONTINUE": "<PERSON><PERSON><PERSON>", "PARTIAL_TENANT_MSG": "La configuration de votre locataire Zscaler est partiellement terminée.", "CLICK_CONTINUE_MSG": "Cliquez sur \"Terminer\" pour terminer la configuration.", "DISABLE_MULTIFACTOR": "Désactiver l'authentification multifacteurs", "DISABLE_FIDO": "Désactiver FIDO2", "DISABLE_FIDO_MSG": "Vous risquez de vous exclure du compte si vous n'utilisez qu'une clé de sécurité ou une clé biométrique pour l'authentification. Si vous désactivez MFA, la seule façon d'accéder à votre compte est l'authentification par mot de passe. Si vous ne vous souvenez pas de votre mot de passe ou si vous ne l'avez pas encore configuré, demandez une réinitialisation de votre mot de passe.", "DISABLE_MFA_MSG": "La désactivation de l'authentification à plusieurs facteurs est une modification globale. Elle dégrade la sécurité de vos utilisateurs. Voulez-vous vraiment continuer ?", "ADMINISTRATIVE": "Administratif", "SERVICE_ENTITLEMENTS": "Droits au service", "ADMINISTRATIVE_ENTITLEMENTS": "Droits administratifs", "ORGANIZATION_ID": "Identifiant d’organisation", "GROUPS": "Groupes", "ASSIGN_USER": "Attribuer un utilisateur", "SELECT_USERS_AND_ROLE": "Sélectionner les utilisateurs et les rôles", "SUMMARY": "Synthèse", "ASSIGN": "Attribuer", "SELECT_GROUPS_AND_ROLE": "Sélectionner les groupes et les rôles", "SERVICE_NAME": "Nom du service", "LOGIN_NAME": "Nom de connexion", "ADD_ROLE": "Ajouter un rôle", "EDIT_ROLE": "Modifier le rôle", "USERS_AND_GROUPS_TEXT": "Utilisateurs & Groupes", "USERS_CREDENTIALS": "Informations d'identification des utilisateurs", "AUTHENTICATION_SESSION": "Session d'authentification", "AUTHENTICATION_SESSION_FOR_SERVICE_ENROLLMENT": "Session d'authentification pour l'inscription au service", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "Forcer l'authentification pour la réauthentification Private Access", "ZSLOGIN_AS_IDENTITY_PROVIDER": "ZSLogin comme fournisseur d'identité", "AZURE_AD_AS_TENANT": "Locataire AZURE AD A (OIDC)", "OKTA_TENANT": "Locataire Okta B (SAML)", "PING_IDENTITY": "Locataire Ping Identity C (SAML)", "DOMAINS": "Domaines", "SERVICE_ENTITLEMENTS_TEXT": "Droits au service", "AUTHENTICATION_EVENT_LOG": "Journaux des événements d'authentification", "DELETE_ROLE": "<PERSON><PERSON><PERSON><PERSON> le rôle", "ENTITLEMENTS": "Droits", "ENTITLEMENTS_LABEL": "Droits", "USER_ID": "Identifiant utilisateur", "REMOVE_ASSIGNMENTS": "Supprimer les attributions", "SELECT_USERS": "Sélectionner des utilisateurs", "ASSIGN_GROUP": "Attribuer le groupe", "SELECT_GROUPS": "Sélectionner les groupes", "SAME_ROLE_FOR_SELECTED_USERS": "Définir le même rôle pour tous les utilisateurs sélectionnés", "SAME_ROLE_FOR_SELECTED_GROUPS": "Définir le même rôle pour tous les groupes sélectionnés", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "Définir le même rôle et la même portée pour tous les utilisateurs sélectionnés", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "Définir le même rôle et la même portée pour tous les groupes sélectionnés", "ATTRIBUTE_ALREADY_MAPPED": "Attribut {{value}} dé<PERSON><PERSON> mappé", "MFA_TOGGLE_TOOLTIP": "L’activation de l'authentification multifacteur permet aux utilisateurs de sélectionner un facteur d'authentification secondaire en plus de leur mot de passe.", "FIDO_TOGGLE_TOOLTIP": "L'activation de FIDO2 en tant que méthode principale permet à l'utilisateur d'éviter les mots de passe et d'utiliser une clé biométrique ou de sécurité comme méthode d'authentification.", "LINKED_TENANTS": "Services liés", "SELECT_ROLE": "Sélectionner un rôle", "SELECT_SCOPE": "Sélectionner la portée", "ALL_USERS": "Tous les utilisateurs", "ASSIGNED_USERS": "Utilisateurs attribués", "UNSELECTED_ROLES": "Rôles non sélectionnés", "SELECTED_ROLES": "<PERSON><PERSON><PERSON> sélectionn<PERSON>", "MANAGE": "<PERSON><PERSON><PERSON>", "EDIT_NAME": "Modifier le nom", "VIEW_ROLES": "Voir les rôles", "DEVICE_GROUP_RESTRICTIONS": "Restrictions relatives au groupe d'appareils", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "G<PERSON>rer les restrictions des groupes d'appareils", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "Activer les restrictions sur les groupes d'appareils", "DEVICE_GROUPS": "Groupes d'appareils", "ORGANIZATION_NAME": "Nom de l'organisation", "DEVICE_GROUP_ASSIGNMENT": "Attribution des groupes d'appareils", "SERVICE_RUNTIME_ASSIGNMENT": "Attribution de service à l'exécution", "ROLE_ASSIGNMENT": "Attribution des rôles", "DELETE_ASSIGNMENT": "Supprimer l'attribution", "NO_RECORD_EXISTS": "Aucun enregistrement n'existe", "SERVICE": "Service", "REMOTE_ASSISTANCE": "Assistance à distance", "CXO_INSIGHT": "Informations pour les décideurs", "VIEW": "<PERSON><PERSON><PERSON><PERSON>", "ACCESS_TYPE": "Type d’accès", "FULL_ACCESS": "<PERSON><PERSON>ès complet", "ENABLE_FULL_ACCESS": "<PERSON><PERSON> l'accès complet", "ACCESS_VALID_UNTIL": "Accès valable jusqu'au", "VIEW_ONLY_ACCESS": "Accès en lecture seule", "ENABLE_VIEW_ONLY_ACCESS": "Activer l'affichage en lecture seule", "DEVICE_TOKEN": "Jeton de l'appareil", "TOKEN_VALIDATORS": "Validateurs de jetons", "TOKEN_VALIDATOR": "Validateur de jeton", "ADD_TOKEN_VALIDATOR": "Ajouter un validateur de jeton", "EDIT_TOKEN_VALIDATOR": "Modifier le validateur de jeton", "DELETE_TOKEN_VALIDATOR": "Supprimer le validateur de jeton", "AUDIENCE": "Public", "SUBJECT_CLAIM": "Revendication d'objet", "CLIENT_JWKS": "JWK client", "CERTIFICATES_PUBLIC_KEYS": "Certificats et clés publiques", "CLIENT_JWKS_URL": "URL des JWK client", "VALIDATION_TYPE": "Type de validation", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Jeton d'appareil Client Connector", "GUEST_DOMAINS": "Domaines invités", "ARBITRARY_GUEST_DOMAINS": "Domaines invités arbitraires", "GUEST_USER": "Utilisateur invité", "GUEST_DOMAIN": "Domaine invité", "ADD": "Ajouter", "DEPARTMENT": "Département", "ENABLE_TO_ALLOW_ALL_DOMAINS": "Activer pour autoriser tous les domaines", "CUSTOM_ATTRIBUTE": "Attribut person<PERSON><PERSON><PERSON>", "BRANDING": "Marque", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "Personnaliser l'adresse e-mail envoyée par", "CONFIGURATION_SETTINGS": "Paramètres de configuration", "CUSTOMIZE_EMAIL_SUBJECT": "Personnaliser l'objet de l'e-mail", "EMAIL_FROM_ADDRESS": "Adresse d'expéditeur de l'e-mail", "EMAIL_SUBJECT": "Objet de l'e-mail", "EDIT_LOGO": "Modifier le logo", "LOGO": "Logo", "UPLOAD_LOGO": "Charger le logo", "INVALID_IMAGE_TYPE": "Type d'image non valide", "BASE_64_CONVERSION_FAILED": "Impossible de convertir l'image en version codée en base 64", "DEPARTMENTS": "Départements", "ADD_DEPARTMENT": "Ajouter un département", "EDIT_DEPARTMENT": "Modifier le département", "DELETE_DEPARTMENT": "Supp<PERSON>er le département", "IMPORT_DEPARTMENT": "Importer le département", "DEPARTMENT_NAME": "Nom du service", "VIEW_USER": "Voir l'utilisateur", "VIEW_GROUP": "Voir le groupe d'utilisateurs", "DASHBOARD": "Tableau de bord", "CREATED": "<PERSON><PERSON><PERSON>", "UPDATED": "Mis à jour", "DELETED": "Supprimé", "EXTERNAL_IDP": "IdP externe", "HOSTED": "<PERSON><PERSON><PERSON><PERSON>", "TOTAL": "Total", "CSV": "CSV", "MANUAL": "<PERSON>", "EMAIL_LOGIN": "OTP d'e-mail", "FIDO_LOGIN": "Authentification FIDO", "PWD_LOGIN": "Mot de passe", "TOTP_LOGIN": "Connexion TOTP", "SMS_LOGIN": "Connexion SMS", "SAML_LOGIN": "SAML", "OIDC_LOGIN": "OIDC", "JIT": "jit", "SCIM": "SCIM", "ADMIN_ASSIGNMENT": "Attribution d'administrateur", "RUNTIME_ASSIGNMENT": "Attribution à l'exécution", "ZCC_ASSIGNMENT": "Attribution ZCC", "ONE_DAY": "1 jour", "TWO_DAYS": "2 jours", "SEVEN_DAYS": "7 jours", "FOURTEEN_DAYS": "14 jours", "THIRTY_DAYS": "30 jours", "SIXTY_DAYS": "60 jours", "NINTY_DAYS": "90 jours", "NO_DATA_AVAILABLE": "<PERSON><PERSON><PERSON> donnée disponible", "ZSLOGIN_FOR_ADMINISTRATORS": "ZSLogin pour administrateurs", "ZIDENTITY_FOR_ADMINISTRATORS": "ZIdentity pour administrateurs", "REVERT_TO_PROVISIONED": "Revenir à l'état provisionné", "REVERT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ZSLOGIN_MIGRATION_STATUS": "État de la migration de ZSLogin", "ZIDENTITY_MIGRATION_STATUS": "État de la migration de ZIdentity", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "La vérification de la configuration du fournisseur d'identité externe a échoué", "UPDATE_PSEUDO_DOMAIN": "Mettre à jour le pseudo-domaine", "CURRENT_PSEUDO_DOMAIN_NAME": "Nom du pseudo-domaine actuel", "NEW_PSEUDO_DOMAIN_NAME": "Nouveau nom du pseudo-domaine", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "La mise à jourd du pseudo-domaine a réussi !", "MULTIFACTOR_BYPASS": "Contourner l'authentification multifactorielle", "SKIP_SECOND_FACTOR_AUTHENTICATION": "Ignorer l'authentification à deux facteurs", "SKIP_UNTIL": "Ignorer jusque", "INTEGRATION": "Intégration", "API_CLIENTS": "Clients d'API", "API_RESOURCES": "Ressources d'API", "ADD_API_CLIENT": "Ajouter un client d'API", "ADD_API_RESOURCE": "Ajouter une ressource d'API", "EDIT_API_RESOURCE": "Modifier la ressource d'API", "API_RESOURCE": "Ressource d'API", "ACCESS_TOKEN_VALIDITY": "Validité du jeton d'accès", "MINUTES": "Minutes", "HOURS": "<PERSON><PERSON>", "PRIVATE_KEY_JWT": "JWT de clé privée", "CLIENT_JWK_URL": "URL des JWK client", "CERTIFICATE_PUBLIC_KEY": "Certificats/clés publiques", "CLIENT_JWK": "JWK client", "CLIENT_SECRETS": "Secrets du client", "CLIENT_AUTHENTICATION": "Authentification du client", "CLIENT_INFORMATION": "Informations sur le client", "ZSCALER_API": "API ZSCALER", "RESOURCE_NAME": "Nom de la ressource", "NEVER": "<PERSON><PERSON>", "CERTIFICATES_AND_PUBLIC_KEYS": "Certificats et clés publiques", "DELETE_API_CLIENT": "Supprimer le client d'API", "DELETE_API_CLIENT_MESSAGE": "Voulez-vous vraiment supprimer ce client d'API ? Les modifications ne peuvent pas être annulées.", "ACCESS_TOKEN_REQUIRED_MESSAGE": "La durée de vie du jeton d'accès est requise.", "EXPIRES": "Expiration", "SECRET": "Secret", "EDIT_API_CLIENT": "Modifier le client d'API", "VIEW_API_CLIENT": "Voir le client d'API", "CREATED_ON": "<PERSON><PERSON><PERSON> le", "EXPIRES_ON": "Expire le", "API_CLIENT": "Client API", "CLIENT_SECRET_WARNING": "<PERSON><PERSON>r le secret du client. Il ne sera plus affiché par la suite.", "API_CLIENTS_AND_RESOURCES": "Clients & ressources d'API", "LOGIN_ID_ATTRIBUTE": "Attribut d'identification de connexion", "LOGOUT": "Déconnexion", "ENVIRONMENT": "Environnement", "SYSTEM": "Système", "ZIDENTITY_ROLES": "Rôles de ZIdentity", "CLIENT": "Client", "RESOURCES": "Ressources", "BASIC": "de base", "ADVANCED": "Advanced", "PROVISIONING": "Provisionnement", "ZIDENTITY_DASHBOARD": "Tableau de bord ZIdentity", "AUTHENTICATION_LEVELS": "Niveaux d'authentification", "LEVEL_NAME": "Nom du niveau", "VALIDITY": "Validité", "DURATION": "<PERSON><PERSON><PERSON>", "MESSAGE_TO_USER": "Message pour l'utilisateur (facultatif)", "PARENT": "Parent", "SUB_LEVEL_NAME": "Nom du sous-niveau", "DAYS": "Jours", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "Correspondance entre les niveaux et le contexte d'authentification", "LEVELS": "Niveaux", "AUTHENTICATION_CONTEXT": "Contexte d'authentification", "DELETE_AL": "Supprimer le niveau d'authentification", "DELETE_AL_CONFIRM_MSG": "Cette action est irréversible et supprime complètement le niveau d'authentification. Voulez-vous vraiment continuer ?", "MOVE": "MOVE"}