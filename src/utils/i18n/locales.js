import deDE from './en-US-ZID_de-DE.json';
import esES from './en-US-ZID_es-ES.json';
import frFR from './en-US-ZID_fr-FR.json';
import jaJP from './en-US-ZID_ja-JP.json';
import zhTW from './en-US-ZID_zh-TW.json';
import enUS from './en-us.json';

export const DEFAULT_LOCALE = 'en-US';

export const getSelectedLocale = () => {
  const locale = localStorage?.locale || DEFAULT_LOCALE;

  return locale;
};

export const LOCALES_MAPPING = {
  'English (US)': 'en-US',
  'Spanish (es-ES)': 'es-ES',
  'French (fr-FR)': 'fr-FR',
  'Chinese Taiwan Traditional (zh-TW)': 'zh-TW',
  'Japanese (ja-JP)': 'ja-JP',
  'German (de-DE)': 'de-DE',
};

export const LOCALES = {
  'en-US': enUS,
  'es-ES': esES,
  'fr-FR': frFR,
  'zh-TW': zhTW,
  'ja-JP': jaJP,
  'de-DE': deDE,
};
