{"LOCALE": "en-US", "EMAIL_ADDRESS": "E-Mail-Adresse", "EMAIL_PLACEHOLDER": "Geben Sie Ihre E-Mail-Adresse ein...", "LOGIN_ID_PLACEHOLDER": "<PERSON><PERSON><PERSON> Sie Ihre Login-ID ein...", "LOGIN_ID_LABEL": "Login ID", "LOGIN_PASSWORD_PLACEHOLDER": "Geben Sie Ihr Passwort ein...", "PASSWORD_LABEL": "Passwort", "NEW_PASSWORD_LABEL": "Neues Passwort", "OLD_PASSWORD_LABEL": "Altes Passwort", "CONFIRM_NEW_PASSWORD_LABEL": "Neues Passwort bestätigen", "PASSWORD_PLACEHOLDER": "Passwort eingeben...", "FORGOT_PASSWORD": "Passwort vergessen?", "REMEMBER_ME": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "RESET_PASSWORD": "Passwort zurücksetzen", "CHANGE_PASSWORD": "Passwort ändern", "SUCCESS": "Erfolg", "RESEND": "<PERSON><PERSON><PERSON> senden", "SIGN_IN_LABEL": "Anmelden", "INITIAL_DOMAIN_NAME": "Ursprünglicher Domänenname", "FIRST_NAME": "<PERSON><PERSON><PERSON>", "LAST_NAME": "Nachname", "PHONE_NUMBER": "Telefonnummer", "REQUIRED_VALIDATION_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "INVALID_CREDENTIALS_MESSAGE": "Ungültige Anmelde-ID oder ungültiges Passwort", "AUDITOR_DIRECT_LOGIN_NOT_ALLOWED": "Ein Auditor kann sich von hier nicht einloggen", "LANGUAGE": "English (US)", "COOKIES_DISABLED": "Cookies müssen zugelassen werden, um diese Anwendung zu verwenden. Bitte aktivieren Sie die Cookie-Unterstützung Ihres Browsers für diese Website.", "COOKIES_NOT_ALLOWED": "<PERSON><PERSON> nicht erlaubt", "WEB_APP_NOT_OPTIMIZED_FOR_CURRENT_BROWSER": "Dieser Browser wird nicht unterstützt und er kann die Funktionalität dieser Website beeinträchtigen. Wir empfeh<PERSON>en, <PERSON>hren Browser auf die neueste Version zu aktualisieren. Wenn Sie diese Meldung ignorieren möchten, klicken Sie auf OK.", "BROWSER_NOT_SUPPORTED": "Browserversion nicht unterstützt", "OK": "OK", "NEXT": "<PERSON><PERSON>", "BACK": "Zurück", "SAVE": "Speichern", "SAVE_AND_NEXT": "Speichern und fortfahren", "COPYRIGHT": "Copyright", "COPYRIGHT_STATEMENT": "Alle Rechte vorbehalten.", "POWERED_BY": "Powered by", "ADMIN_PORTAL_SIGN_IN": "Verwaltungsportal-Anmeldung", "ALL_ORGANIZATIONS": "Alle Organisationen", "SEARCH_PLACEHOLDER": "Suchen...", "GENERAL": "Allgemein", "ZPA": "Zscaler Private Access", "IAM": "Identitäts- und Zugriffsmanagement", "ZIA": "Zscaler Internet Access", "ZDX": "Zscaler Digital Experience", "EC": "Cloud & Branch Connector", "CCP": "Client Connector-Portal", "ZBI": "Browser-Isolierung", "BI": "Business Insights", "ZRA": "Risk360", "LAUNCH": "Starten", "VERIFY": "Prüfen", "CANCEL": "Abbrechen", "DONE": "<PERSON><PERSON><PERSON><PERSON>", "RESET": "Z<PERSON>ücksetzen", "CLEAR": "Löschen", "CLEAR_ALL": "Alle löschen", "MY_PROFILE": "<PERSON><PERSON>", "ZS_LOGIN": "ZSL<PERSON>in", "Z_IDENTITY": "ZIdentity", "ACCOUNT_MANAGEMENT": "Kontoverwaltung", "CLOUD_CONFIGURATION": "Cloud-Konfiguration", "PRODUCT": "Produkt", "CLOUD_NAME": "Cloud-Name", "CLOUD_ID": "Cloud-ID", "ORG_NAME": "Organisationsname", "LINKED_SERVICES": "Verknüpfte Services", "LINK": "Link", "UNLINK": "Verknüpfung aufheben", "LINKED": "Verknüpft", "UNLINKED": "Nicht verknüpft", "LINK_TENANT": "Instanz verknüpfen", "UNLINK_TENANT": "Verknüpfung der Instanz aufheben", "ATTRIBUTE": "Attribut", "ATTRIBUTES": "Attribute", "USER_ATTRIBUTES": "Benutzerattribut", "SESSION_ATTRIBUTE": "Sitzungsattribut", "ADVANCED_SETTINGS": "Erweiterte Einstellungen", "DIRECTORY": "Directory", "USERS": "<PERSON><PERSON><PERSON>", "USER_GROUPS": "Benutzergruppen", "EXTERNAL_IDENTITIES": "Externe Identitäten", "APPLICATIONS": "Applikationen", "ZSCALER_SERVICES": "Zscaler-Services", "SECURITY": "Sicherheit", "IP_LOCATIONS": "IP-Standorte", "IP_LOCATIONS_AND_GROUPS": "IP-Standorte und Gruppen", "IP_LOCATION_GROUPS": "Standortgruppen", "ADMINISTRATION": "Administration", "ADMINISTRATION_CONTROLS": "Verwaltungs-Steuerelemente", "AUTHENTICATION": "Authentifizierung", "AUTHENTICATION_METHODS": "Authentifizierungsmethoden", "ENABLE_MULTIFACTOR_SETTINGS": "Multifaktor-Einstellungen aktivieren", "ENABLE_MULTIFACTOR_AUTH_USER": "Multifaktorauthentifizierung (MFA) für die Serviceregistrierung aktivieren", "ENABLE_MULTIFACTOR_AUTH_ADMIN": "Multifaktorauthentifizierung (MFA) für Administratoren aktivieren", "MFA_ENROLLMENT_GRACE_PERIOD": "Kulanzfrist für MFA-Registrierung (in Tagen)", "AUDIT_LOGS": "Audit-Logs", "ADD_USER": "Benutzer hinzufügen", "IMPORT_CSV": "CSV importieren", "ACTIONS": "Aktionen", "ACTIVATE": "Aktivieren", "DE_ACTIVATE": "Deaktivieren", "BULK_ACTIVATE": "Als Stapel aktivieren", "BULK_DEACTIVATE": "Als Stapel deaktivieren", "BULK_DELETE": "Als Stapel löschen", "DELETE": "Löschen", "NAME": "Name", "GROUP": "Gruppe", "SELECT": "Auswählen", "LOGIN_ID": "Login ID", "STATUS": "Status", "GENERAL_INFORMATION": "Allgemeine Informationen", "MY_ACCOUNT": "<PERSON><PERSON>", "FULL_NAME": "Vollständiger Name", "VERIFIED": "Verifiziert", "LANGUAGE_LABEL": "<PERSON><PERSON><PERSON>", "TIMEZONE": "Zeitzone", "ACTIVE": "Aktiv", "INACTIVE": "Inaktiv", "USER_INFORMATION": "Userinformationen", "PRIMARY_EMAIL": "Primäre E-Mail-Adresse", "SAME_AS_LOGIN_ID": "<PERSON><PERSON><PERSON> wie Login-ID", "SECONDARY_EMAIL": "Sekundäre E-Mail-Adresse", "ADDITIONAL_ATTRIBUTES": "Zusätzliche Attribute", "SECURITY_SETTINGS": "Sicherheitseinstellungen", "PASSWORD_OPTION": "Passwortoption", "PROMPT_PASSWORD_FIRST_LOGIN": "Nach erster Anmeldung zu Passwortänderung auffordern", "CONFIRM_PASSWORD": "Passwort bestätigen", "ASSIGNMENT": "<PERSON><PERSON><PERSON><PERSON>g", "ASSIGN_GROUPS": "Gruppen zuweisen", "SET_BY_ADMIN": "Von <PERSON> fest<PERSON><PERSON>t", "SET_BY_USER": "<PERSON>r festgelegt", "AUTO_GENERATED": "Automatisch generiert", "DELETE_USER": "<PERSON>r l<PERSON>", "EDIT_USER": "<PERSON><PERSON><PERSON> bearbeiten", "UPDATE": "Update", "OFF": "Aus", "ON": "Ein", "ADD_ATTRIBUTE": "Attribut hinzufügen", "IMPORT_ATTRIBUTE": "Attribute importieren", "CREATE_NEW_ATTRIBUTES": "Neue Attribute erstellen", "TABLE_NUMBER": "Anz.", "DISPLAY_NAME": "Anzeigename", "ATTRIBUTE_NAME": "Attributname", "DATA_TYPE": "Datentyp", "TABLE_REQUIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ORIGIN": "Ursprung", "SYSTEM_DEFINED": "systemdefiniert", "USER_DEFINED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ENABLE": "Aktivieren", "ENABLED": "Aktiviert", "DISABLE": "Deaktivieren", "DISABLED": "Deaktiviert", "INFORMATION": "Informationen", "ENTER_TEXT": "Text eingeben...", "ATTRIBUTE_REQUIRED": "Attribut <PERSON><PERSON><PERSON>", "DESCRIPTION_OPTIONAL": "Beschreibung (optional)", "DESCRIPTION": "Beschreibung", "EDIT_ATTRIBUTE": "Attribut bearbeiten", "DELETE_ATTRIBUTE": "Attribut löschen", "ADD_SESSION_ATTRIBUTE": "Sitzungsattribut hinzufügen", "EDIT_SESSION_ATTRIBUTE": "Sitzungsattribut bearbeiten", "DELETE_SESSION_ATTRIBUTE": "Sitzungsattribut löschen", "ADD_GROUP": "Gruppe hinzufügen", "ADD_USER_GROUP": "Usergruppe hinzufügen", "GROUP_NAME": "Gruppenname", "USER_NAME": "<PERSON><PERSON><PERSON><PERSON>", "EDIT_GROUP": "Gruppe bearbeiten", "DELETE_GROUP": "Gruppe löschen", "USER_GROUP_NAME": "Benutzergruppenname", "ASSIGN_USERS": "<PERSON><PERSON> <PERSON><PERSON>", "SOURCE": "<PERSON><PERSON>", "STRING": "Zeichenfolge", "INTEGER": "<PERSON><PERSON><PERSON><PERSON>", "BOOLEAN": "<PERSON><PERSON><PERSON>", "DATE": "Datum", "DECIMAL": "Dezimal", "CHANGE_PASSWORD_SETTINGS": "Passworteinstellungen ändern", "ZS_SERVICES": "Zscaler-Services", "CLOUD_AND_ORG_ID": "Cloud- und Organisations-ID", "APPLICATION_DETAIL": "Anwendungsdetails", "CLONE_MEMBERSHIP_FOR_APPLICATION": "Mitgliedschaft aus dieser Anwendung klonen", "ROLE": "<PERSON><PERSON>", "ROLES": "<PERSON><PERSON>", "MANAGE_ROLES": "<PERSON><PERSON> verwalten", "SYNC": "Sync", "USERS_ASSIGNMENT": "Userzuweisung", "GROUPS_ASSIGNMENT": "Usergruppenzuweisung", "ASSIGN_ROLE": "<PERSON><PERSON>", "ASSIGN_SCOPE": "Geltungsbereich zu<PERSON>sen", "ASSIGN_USER_GROUPS": "Gruppenzuweisung", "ADD_MORE": "Me<PERSON> hinzufügen", "ASSIGN_USERS_AND_GROUPS": "User und Usergruppen zuweisen", "REVIEW": "Überprüfung", "USERS_AND_GROUPS": "User und Usergruppen", "TYPE": "<PERSON><PERSON>", "EDIT_USERS_ROLE": "<PERSON><PERSON> bearbeiten", "EUSA_AGREEMENT": "End-User <PERSON>", "DELETE_USERS_ROLE": "<PERSON>r l<PERSON>", "EDIT_GROUPS_ROLE": "<PERSON><PERSON> bearbeiten", "DELETE_GROUPS_ROLE": "Gruppe löschen", "BULK_DELETE_USERS_GROUPS_ROLE": "Benutzern und Gruppen stapelweise löschen", "CONFIGURATION_TYPE": "Konfigurationstyp", "RECOMMENDED": "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT": "Standard", "CUSTOM": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PASSWORD_COMPLEXITY": "Passwortkomplexität", "PASSWORD_LENGTH": "Passwortlänge", "MIN_LWR_CASE": "Mind<PERSON><PERSON><PERSON> von Kleinbu<PERSON>", "MIN_UPPR_CASE": "Mindestanzahl von Großbuchstaben", "MIN_NUMERIC": "Mindestanzahl von numerischen Zeichen", "MIN_SPL_CHAR": "Mind<PERSON><PERSON><PERSON> von Sonderzeichen", "PASSWORD_DOES_NOT_INCLUDE": "Das Passwort darf weder den Firmennamen, noch den Usernamen, den Vornamen oder den Nachnamen enthalten", "REJECT_REUSE": "Wiederverwendung der letzten 5 Passwörter verhindern", "DEACTIVATE_USER": "User nach 10 erfolglosen Versuchen deaktivieren", "ALLOW_ADMIN_SET_PASSWORD": "Administrator gesta<PERSON>, das Passwort des Benutzers zu erstellen oder zu ändern", "FORCE_PASSWORD_CHANGE": "Passwortänderung nach der ersten Anmeldung erzwingen", "PASSWORD_EXPIRY": "Passwort-Ablaufzeit (in Tagen)", "PASSWORD_CRITERIA": "Passwortkriterien", "POLICY": "Policy", "POLICIES": "<PERSON><PERSON><PERSON><PERSON>", "AUTHENTICATION_POLICY": "Authentifizierungsrichtlinie", "PASSWORD_POLICY": "Passwortrichtlinie", "LOAD_MORE": "<PERSON><PERSON> <PERSON>", "EDIT_EMAIL": "E-Mail-<PERSON><PERSON><PERSON> bear<PERSON>", "UPDATE_EMAIL": "E-Mail-Adresse aktualisieren", "NEW_EMAIL": "Neue E-Mail-Adresse eingeben", "VALIDATE_EMAIL": "E-Mail-Adresse valid<PERSON>", "VALIDATION_CODE": "Validierungscode eingeben", "RESEND_CODE": "Code erneut senden", "CURRENT_PASSWORD": "Aktuelles Passwort", "NEW_PASSWORD": "Neues Passwort", "CONFIRM_NEW_PASSWORD": "Neues Passwort bestätigen", "MIN_LENGTH_REQUIRED": "Mindestens {{value}} Z<PERSON><PERSON>", "MIN_LOWER_CASE_REQUIRED": "Mindestens {{value}} Kleinbuchstaben (a-z)", "MIN_UPPER_CASE_REQUIRED": "Mindestens {{value}} Großbuchstaben (A-Z)", "MIN_NUMERIC_REQUIRED": "Mindestens {{value}} <PERSON><PERSON><PERSON> (0-9)", "MIN_SPECIAL_CHAR_REQUIRED": "Mindestens {{value}} Sonderzeichen", "SIGN_ON_POLICIES": "Administrator-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD_RULE": "Regel hinzufügen", "RULE_ORDER": "Regelposition", "RULE_STATUS": "Regel Status", "RULE_NAME": "Regelname", "RULE_ACTION": "Regelaktion", "CRITERIA": "Kriterien", "ADD_SIGN_ON_POLICY": "Administrator-Anmelderichtl<PERSON><PERSON> hinzufü<PERSON>", "EDIT_SIGN_ON_POLICY": "Administrator-Anmelderichtlini<PERSON> bearbei<PERSON>", "DELETE_SIGN_ON_POLICY": "Administrator-Anmelderichtlinie löschen", "LOCATION": "<PERSON><PERSON>", "LOCATION_GROUP": "Standortgruppe", "ALLOW": "<PERSON><PERSON><PERSON>", "DENY": "Verweigern", "OPERATIONS": "<PERSON><PERSON><PERSON>", "REMOVE": "Entfernen", "NO_DATA_FOUND": "<PERSON><PERSON> Daten gefunden", "NO_ITEMS_FOUND": "<PERSON>ine Elemente gefunden", "BROWSE_FILE": "<PERSON><PERSON> <PERSON> suchen", "IMPORT": "Importieren", "NO_FILE_CHOSEN": "<PERSON><PERSON> ausgewählt", "OVERRIDE_EXISTING_ENTRIES": "Bestehende Einträge überschreiben", "CSV_FILE": "CSV-Datei", "SAMPLE_CSV_DOWNLOAD": "Be<PERSON>piel herunterladen", "IMPORT_USERS": "User importieren", "IMPORT_GROUPS": "Gruppen importieren", "PRIMARY_IDENTITY_PROVIDER": "Primärer Identitätsanbieter", "SECONDARY_IDENTITY_PROVIDER": "Sekundäre Identitätsanbieter", "ADD_PRIMARY_IDP": "Primären IdP hinzufügen", "ADD_SECONDARY_IDP": "Sekundären IdP hinzufügen", "ADD_PRIMARY_IDENTITY_PROVIDER": "Primären Identitätsanbieter hinzufügen", "EDIT_PRIMARY_IDENTITY_PROVIDER": "Primären Identitätsanbieter bearbeiten", "DELETE_PRIMARY_IDENTITY_PROVIDER": "Primären Identitätsanbieter löschen", "ADD_SECONDARY_IDENTITY_PROVIDER": "Sekundären Identitätsanbieter hinzufügen", "EDIT_SECONDARY_IDENTITY_PROVIDER": "Sekundären Identitätsanbieter bearbeiten", "DELETE_SECONDARY_IDENTITY_PROVIDER": "Sekundären Identitätsanbieter löschen", "SAML_CONFIGURATION": "SAML-Konfiguration", "OIDC_CONFIGURATION": "OIDC-KONFIGURATION", "IDENTITY_VENDOR": "Identitätsanbieter", "DOMAIN": "<PERSON><PERSON><PERSON>", "YES": "<PERSON>a", "NO": "Nr.", "ADD_LOCATION": "Standort hinzufügen", "EDIT_LOCATION": "<PERSON>ort bearbeiten", "DELETE_LOCATION": "Standort löschen", "IMPORT_LOCATION": "Standort importieren", "DELETE_LOCATION_CONFIRMATION_MESSAGE": "<PERSON><PERSON> dieser Standort wirklich gelöscht werden? Diese Änderung kann nicht mehr rückgängig gemacht werden.", "BULK_DELETE_LOCATION_CONFIRMATION_MESSAGE": "<PERSON><PERSON> diese Standorte wirklich als Stapel gelöscht werden? Diese Änderungen können nicht mehr rückgängig gemacht werden.", "IP_ADDRESS": "IP-Adresse", "COUNTRY": "Land", "LOCATION_INFORMATION": "Information über Standort", "NAME_REQUIRED_MESSAGE": "'Name' ist ein <PERSON>", "LOCATIONS_REQUIRED_MESSAGE": "'Standort' ist ein P<PERSON>feld", "COUNTRY_REQUIRED_MESSAGE": "'Land' ist ein P<PERSON>feld", "IP_ADDRESS_REQUIRED_MESSAGE": "'IP-Adresse' ist ein Pflichtfeld", "LOCATION_COUNT": "<PERSON><PERSON><PERSON><PERSON>", "LOCATIONS": "<PERSON><PERSON><PERSON>", "UNSELECTED_LOCATIONS": "Nicht ausgewählte Standorte", "SELECTED_LOCATIONS": "Ausgewählte Standorte", "ADD_LOCATION_GROUP": "Standortgruppe hinzufügen", "EDIT_LOCATION_GROUP": "Standortgruppe bearbeiten", "DELETE_LOCATION_GROUP": "Standortgruppe löschen", "DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "<PERSON>l diese Standortgruppe wirklich gelöscht werden? Diese Änderung kann nicht mehr rückgängig gemacht werden.", "BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE": "<PERSON><PERSON> diese Standortgruppen wirklich als Stapel gelöscht werden? Diese Änderungen können nicht mehr rückgängig gemacht werden.", "IMPORT_LOCATION_GROUP": "Standortgruppe importieren", "PROTOCOL": "Protokoll", "AUTHENTICATION_REQUEST": "Authentifizierungsanfrage", "ENABLE_SAML_REQUEST_SIGNING": "Signieren von SAML-Anfragen aktivieren", "SAML_REQUEST_SIGNING": "SAML – Signaturanforderung", "LOGIN_HINT": "Anmeldungshin<PERSON><PERSON>", "SIGNING_ALGORITHM": "Signaturalgorithmus", "REQUEST_SIGNING_CERTIFICATE": "Anfragesignaturzertifikat", "SP_SAML_CERTIFICATE": "SP SAML-Zertifikat", "DOWNLOAD_CERTIFICATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ENCRYPTED_SAML_RESPONSE": "Verschlüsselte SAML-Antwort", "ENCRYPTED_SAML_ASSERTION": "Verschlüsselte SAML-Assertion", "ENABLE_ENCRYPTED_SAML_ASSERTION": "Verschlüsselte SAML-Assertion aktivieren", "CERTIFICATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SAML_ENCRYPTION_CERTIFICATE": "SAML-Verschlüsselungszertifikat", "IDP_METADATA": "IdP-Metadaten", "IDP_METADATA_URL": "IdP-Metadaten-URL", "METADATA_URL": "Metadaten-URL", "IDP_CERTIFICATE": "IDP-Zertifikat", "IDP_ENTITY_URI": "IdP-Entitäts-URI", "PRIMARY_IDP_NOT_AVAILABLE": "Primärer Identitätsanbieter nicht hinzugefügt", "SECONDARY_IDP_NOT_AVAILABLE": "Sekundärer Identitätsanbieter nicht verfügbar", "SP_METADATA": "SP-Metadaten", "DOWNLOAD_SP_METADATA": "SP-<PERSON><PERSON><PERSON>", "IDP_SINGLE_SIGNON_URL": "IdP-SSO-URL", "REDIRECT_URI": "Umleitungs-URI", "TOKEN_ENDPOINT_AUTHENTICATION_METHOD": "Token-Endgerät-Authentifizierungsmethode", "ISSUER": "Aussteller", "ZDK_INSTANCE": "ZDK-Instanz", "APPLICATION": "<PERSON><PERSON><PERSON><PERSON>", "ADD_CLAIM": "Anspruch hinzufügen", "VALUE": "Wert", "CLAIM_REQUIREMENTS": "Anspruchsanforderungen", "SIGNATURE_VALIDATION": "Signaturvalidierung", "EXPIRY": "<PERSON><PERSON><PERSON>", "AUTHORIZATION_ENDPOINT": "Autorisierungs-Endgerät", "TOKEN_ENDPOINT": "Token-Endgerät", "JWKS_ENDPOINT": "JWKS-Endgerät", "USER_INFORMATION_ENDPOINT": "Userinformationen-Endgerät", "CLIENT_SECRET_BASIC": "Client-Geheimschlüssel Basic", "CLIENT_SECRET_POST": "Client-Geheimschlüssel posten", "CLIENT_ID": "Client-ID", "CLIENT_SECRET": "Client-Geheimschlüssel", "REQUESTED_SCOPES": "Angeforderte Geltungsbereiche", "DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT": "Angeforderter Standard-Authentifizierungskontext (optional)", "SP_ENTITY_ID": "SP-Entitäts-ID", "SP_URL": "SP-URL", "SCIM_PROVISIONING_STATUS": "SCIM-Bereitstellungsstatus", "PROVISIONING_SETTINGS": "Bereitstellungeinstellungen", "JIT_PROVISIONING": "Just-in-Time-Bereitstellung (JIT)", "ENABLE_FOR_JIT_PROVISIONING": "Für JIT-Bereitstellung aktivieren", "JIT_ATTRIBUTE_MAPPING": "JIT-Attributzuordnung", "JUST_IN_TIME_ATTRIBUTE_MAPPING": "Just-in-Time-Attributzuordnung", "JUST_IN_TIME_USER_GROUP_ATTRIBUTE": "Just-in-Time-Usergruppenattribut", "JUST_IN_TIME_ATTRIBUTE": "Just-in-Time-Attribut", "SAML_ATTRIBUTE_MAPPING": "SAML-Attributzuordnung", "USER_GROUP_SAML_ATTRIBUTE": "Usergruppen-SAML-Attribute", "SAML_ATTRIBUTE": "SAML-Attribut", "USER_ATTRIBUTE": "Benutzerattribut", "SCIM_PROVISIONING": "SCIM-Bereitstellung", "SCIM_ATTRIBUTE_MAPPING": "SCIM-Attributzuordnung", "SCIM_ATTRIBUTE": "SCIM-Attribut", "SCIM_ENDPOINT_URL": "SCIM-Endgerät-URL", "SESSION_ATTRIBUTE_MAPPING": "Sitzungsattributzuordnung", "IDP_ATTRIBUTE": "IdP-Attribut", "AUTHENTICATION_METHOD": "Authentifizierungsmethode", "TOKEN": "<PERSON>er-<PERSON><PERSON>", "GENERATE_TOKEN": "<PERSON><PERSON> generieren", "INPUT_METHOD": "Eingabemethode", "FETCH_WITH_URL": "Metadaten-URL", "UPLOAD_METADATA": "<PERSON><PERSON><PERSON> ho<PERSON>laden", "MANUAL_ENTRY": "<PERSON><PERSON>", "UPLOAD_CERTIFICATE": "Zertif<PERSON>t ho<PERSON>n", "BEARER_TOKEN": "<PERSON>er-<PERSON><PERSON>", "IDP_ADVANCED_SETTINGS": "Erweiterte IdP-Einstellungen", "SESSION_TIMEOUT": "Sitzungs-Zeitlimit", "SESSION_TIMEOUT_DURATION_IN_MIN": "Dauer des Sitzungs-Zeitlimits (in Minuten)", "ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN": "Dauer des Zeitlimits für Administratoreninaktivität (in Minuten)", "SERVICE_ENROLLMENT_SESSION_TIMEOUT_DURATION_IN_MIN": "Sitzungs-Zeitlimit für Serviceregistrierung (in Minuten)", "NONE": "<PERSON><PERSON>", "TIME_RANGE": "Zeitspanne", "ACTION": "Aktion", "CATEGORY": "<PERSON><PERSON><PERSON>", "SUB_CATEGORY": "Unterkategorie", "INTERFACE": "Schnittstelle", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "SCOPE": "<PERSON><PERSON><PERSON>", "SCOPES_N_ROLES": "Geltungsbereiche und Rollen", "ADD_ITEMS": "Elemente hinzufügen", "TIME_STAMP": "Zeitstempel", "RESOURCE": "Ressource", "ADMIN_ID": "Admin-ID", "CLIENT_IP": "Client-IP", "AM": "AM", "TIME_PM": "pm", "START_TIME": "Startzeit", "END_TIME": "Endzeit", "CURRENT_DAY": "<PERSON><PERSON>", "CURRENT_WEEK": "Aktuelle Woche", "CURRENT_MONTH": "Aktueller Monat", "PREVIOUS_DAY": "Voriger Tag", "PREVIOUS_WEEK": "<PERSON><PERSON><PERSON>", "PREVIOUS_MONTH": "<PERSON><PERSON><PERSON>", "HELP_BROWSER": "<PERSON><PERSON><PERSON>", "SELECT_DATE": "Da<PERSON> ausw<PERSON>en", "CLOSE": "Schließen", "ACCEPT": "Akzeptieren", "SYNC_DOMAINS": "<PERSON><PERSON><PERSON> synchronisieren", "IMPORT_RESULTS": "Ergebnisse importieren", "COMPLETE": "fertigstellen", "PROCESSED_RECORD": "Verarbeitete Datensätze", "TOTAL_RECORDS_ADDED": "Hinzugefügte Datensätze gesamt", "TOTAL_RECORDS_DELETED": "Gelöschte Datensätze gesamt", "TOTAL_RECORDS_IN_IMPORT": "Datensätze im Import gesamt", "TOTAL_RECORDS_UPDATED": "Aktualisierte Datensätze gesamt", "FAILED_RECORDS": "Fehlgeschlagene Datensätze", "DUPLICATE_ITEM": "Der angegebene Name wird bereits verwendet", "RESOURCE_NOT_FOUND": "Ressource nicht gefunden", "CSV_FORMAT_INVALID": "Ungültiges CSV-Format", "UNEXPECTED_ERROR": "<PERSON><PERSON><PERSON><PERSON>", "DUPLICATE_RECORD": "Do<PERSON><PERSON>z", "CONFIGURATION_CHANGES": "Konfigurationsänderungen", "PRE_CHANGES_CONFIGURATION": "Konfiguration vor Änderungen", "POST_CHANGES_CONFIGURATION": "Konfiguration nach Änderungen", "VIEW_CHANGES": "Änderungen anzeigen", "OPEN_HELP_BROWSER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ALL": "Alle", "SIGN_IN": "Anmelden", "SIGN_OUT": "Abmelden", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "GET": "<PERSON><PERSON><PERSON><PERSON>", "SEARCH": "<PERSON><PERSON>", "BULK": "<PERSON><PERSON><PERSON>", "DOWNLOAD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UPLOAD": "Upload", "USER_MANAGEMENT": "User-Management", "LOGIN": "<PERSON><PERSON><PERSON><PERSON>", "AUTHENTICATION_SETTINGS": "Einstellungen Authentifizierung", "TENANTS": "<PERSON><PERSON><PERSON>", "USER": "<PERSON><PERSON><PERSON>", "CUSTOM_USER_ATTRIBUTE": "Benutzerdefiniertes Userattribut", "PASSWORD_CHANGE": "Passwortänderung", "PASSWORD_POLICY_CHANGE": "Passwortrichtlinienänderung", "IDENTITY_PROVIDERS": "Identitätsanbieter", "ADVANCE_SETTINGS": "Erweiterte Einstellungen", "ADMIN_SIGN_ON": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SIGN_ON_POLICY": "Anmelderichtlinie", "SERVICE_ASSIGNMENT": "Servicezuweisung", "SERVICES": "<PERSON><PERSON><PERSON>", "SCIM_API": "SCIM-API", "FAILURE": "<PERSON><PERSON>", "PARTIALLY_FAILED": "Teilweise fehlgeschlagen", "REMOVE_PAGE": "Seite entfernen", "REMOVE_ALL": "Alle entfernen", "CONFIRMATION_REMOVE_PAGE": "Seite entfernen", "CONFIRMATION_REMOVE_ALL": "Alle entfernen", "CONFIRM": "Bestätigen", "CUSTOMISE_COLUMNS": "SPALTEN ANPASSEN", "ALL_CHANGES_SAVED": "Alle Änderungen wurden gespeichert.", "ITEM_HAS_BEEN_DELETED": "Das Element wurde gelöscht", "ITEMS_HAVE_BEEN_DELETED": "Elemente wurden gelöscht", "START_TIME_BEFORE_END_TIME": "Die Startzeit muss vor der Endzeit liegen", "USER_GROUPS_ASSIGNMENT_LIMIT": "Es können nicht mehr als {{value}} Gruppen auf einmal zugewiesen werden", "USER_GROUPS_ROLES_ASSIGNMENT_LIMIT": "Es können nicht mehr als {{value}} Gruppen auf einmal zu Usern/Gruppen zugewiesen werden", "VERIFY_EMAIL_ADDRESS": "E-Mail-Adresse verifizieren", "ENTER_EMAIL_OTP": "E-Mail-OTP eingeben", "SET_UP": "Einrichten", "EMAIL_OTP": "E-Mail-OTP", "NO_MATCHING_ITEMS_FOUND": "<PERSON>ine übereinstimmenden Elemente gefunden", "BEARER_TOKEN_SUCCESSFULLY_GENERATED": "Bearer-<PERSON>ken erfolgreich generiert", "MSG_EMAIL_OTP_SENT": "E-Mail-OTP an {{email}} gesendet", "MSG_INVALID_OTP": "Falscher OTP eingegeben", "DIDNT_RECEIVE": "Nicht erhalten?", "SELECTED_CRITERIA_INVALID": "Das ausgewählte Kriterium ist ungültig", "ALLOW_FIDO_AS_PRIMARY": "FIDO2 als primären Authentifikator zulassen", "ALLOW_EMAIL_OTP_AS_PRIMARY": "E-Mail-OTP als primären Authentifikator zulassen", "AUTHENTICATORS": "Authentifikatoren", "MFA_AUTHENTICATOR": "MFA-Authentifikator", "LAST_USED": "Zuletzt verwendet", "DELETE_AUTHENTICATOR": "MFA-Authentifikator löschen", "RESTART_TENANT_CREATION": "Instanzerstellung neu starten", "CONTINUE": "Fortsetzen", "PARTIAL_TENANT_MSG": "Das Einrichten Ihrer Zscaler-Instanz ist teilweise abgeschlossen.", "CLICK_CONTINUE_MSG": "<PERSON><PERSON><PERSON> Si<PERSON> auf 'Wei<PERSON>', um die Einrichtung abzuschließen.", "DISABLE_MULTIFACTOR": "Multifaktorauthentifizierung deaktivieren", "DISABLE_FIDO": "FIDO2 deaktivieren", "DISABLE_FIDO_MSG": "Wenn Sie nur einen Sicherheitsschlüssel oder biometrische Daten zur Authentifizierung verwenden, sperren <PERSON> sich möglicherweise selbst aus dem Konto aus. Die einzige Möglichkeit, auf Ihr Konto zuzugreifen, ist die passwortbasierte Authentifizierung, wenn Sie MFA deaktivieren. Falls Sie sich nicht an Ihr Passwort erinnern oder es noch nicht eingerichtet haben, fordern Sie eine Passwortrücksetzung an.", "DISABLE_MFA_MSG": "Das Deaktivieren der Multifaktorauthentifizierung ist eine globale Änderung. Dies macht Ihre User weniger sicher. Wollen Sie wirklich fortfahren?", "ADMINISTRATIVE": "Administrativ", "SERVICE_ENTITLEMENTS": "Serviceberechtigungen", "ADMINISTRATIVE_ENTITLEMENTS": "Verwaltungsberechtigungen", "ORGANIZATION_ID": "Org-ID", "GROUPS": "Gruppen", "ASSIGN_USER": "<PERSON><PERSON> <PERSON><PERSON>", "SELECT_USERS_AND_ROLE": "User und Rollen auswählen", "SUMMARY": "Zusammenfassung", "ASSIGN": "<PERSON><PERSON><PERSON><PERSON>", "SELECT_GROUPS_AND_ROLE": "Gruppen und Rollen auswählen", "SERVICE_NAME": "Dienstname", "LOGIN_NAME": "Anmeldename", "ADD_ROLE": "<PERSON><PERSON> hinzufügen", "EDIT_ROLE": "<PERSON><PERSON> bearbeiten", "USERS_AND_GROUPS_TEXT": "User und Gruppen", "USERS_CREDENTIALS": "User-Zugangsdaten", "AUTHENTICATION_SESSION": "Authentifizierungssitzung", "AUTHENTICATION_SESSION_FOR_SERVICE_ENROLLMENT": "Authentifizierungssitzung für Serviceregistrierung", "FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION": "Authentifizierung für erneute Private Access-Authentifizierung erzwingen", "ZSLOGIN_AS_IDENTITY_PROVIDER": "ZSLogin als Identitätsanbieter", "AZURE_AD_AS_TENANT": "Azure AD-Instanz A (OIDC)", "OKTA_TENANT": "Okta-Instanz B (SAML)", "PING_IDENTITY": "Ping-Identitätsinstanz C (SAML)", "DOMAINS": "Domains", "SERVICE_ENTITLEMENTS_TEXT": "Serviceberechtigungen", "AUTHENTICATION_EVENT_LOG": "Authentifizierungs-Ereignisprotokoll", "DELETE_ROLE": "Rolle löschen", "ENTITLEMENTS": "Berechtigungen", "ENTITLEMENTS_LABEL": "Berechtigungen", "USER_ID": "User-ID", "REMOVE_ASSIGNMENTS": "Zuweisungen entfernen", "SELECT_USERS": "Benutzer auswählen", "ASSIGN_GROUP": "Gruppe <PERSON>", "SELECT_GROUPS": "Gruppen auswählen", "SAME_ROLE_FOR_SELECTED_USERS": "Gleiche Rolle für alle ausgewählten User einrichten", "SAME_ROLE_FOR_SELECTED_GROUPS": "Gleiche Rolle für alle ausgewählten Gruppen einrichten", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_USERS": "Gleiche Rolle und gleichen Geltungsbereich für alle ausgewählten User einrichten", "SAME_SCOPE_AND_ROLE_FOR_SELECTED_GROUPS": "Gleiche Rolle und gleichen Geltungsbereich für alle ausgewählten Gruppen einrichten", "ATTRIBUTE_ALREADY_MAPPED": "Attribut {{value}} ist bereits zugeordnet", "MFA_TOGGLE_TOOLTIP": "Wenn Sie die Multifaktorauthentifizierung aktivieren, können User einen sekundären Authentifizierungsfaktor zusammen mit Passwörtern auswählen.", "FIDO_TOGGLE_TOOLTIP": "Wenn Sie FIDO2 als primäre Authetifizierungsmethode aktivieren, können User Passwörter überspringen und biometrische Daten oder einen Sicherheitsschlüssel als Authentifizierungsmethode verwenden.", "LINKED_TENANTS": "Verknüpfte Services", "SELECT_ROLE": "Rolle auswählen", "SELECT_SCOPE": "Geltungsbereich auswählen", "ALL_USERS": "<PERSON><PERSON>", "ASSIGNED_USERS": "Zugewiesene User", "UNSELECTED_ROLES": "Nicht ausgewählte Rollen", "SELECTED_ROLES": "Ausgewählte Rollen", "MANAGE": "<PERSON><PERSON><PERSON><PERSON>", "EDIT_NAME": "Namen bearbeiten", "VIEW_ROLES": "Rollen anzeigen", "DEVICE_GROUP_RESTRICTIONS": "Gerätegruppenbeschränkungen", "MANAGE_DEVICE_GROUP_RESTRICTIONS": "Gerätegruppenbeschränkungen verwalten", "ENABLE_DEVICE_GROUP_RESTRICTIONS": "Gerätegruppenbeschränkungen aktivieren", "DEVICE_GROUPS": "Gerätegruppen", "ORGANIZATION_NAME": "Organisationsname", "DEVICE_GROUP_ASSIGNMENT": "Gerätegruppenzuweisung", "SERVICE_RUNTIME_ASSIGNMENT": "Laufzeit-Servicezuweisung", "ROLE_ASSIGNMENT": "Rollenzuweisung", "DELETE_ASSIGNMENT": "Zuweisung löschen", "NO_RECORD_EXISTS": "<PERSON>s liegt kein Datensatz vor", "SERVICE": "<PERSON><PERSON>", "REMOTE_ASSISTANCE": "Remote-Unterstützung", "CXO_INSIGHT": "CXO-Einblick", "VIEW": "<PERSON><PERSON><PERSON>", "ACCESS_TYPE": "Zugriffstyp", "FULL_ACCESS": "Vollzugriff", "ENABLE_FULL_ACCESS": "Vollzugriff aktivieren", "ACCESS_VALID_UNTIL": "Zugriff gültig bis", "VIEW_ONLY_ACCESS": "Schreibgeschützter Zugriff", "ENABLE_VIEW_ONLY_ACCESS": "Schreibgeschützten Zugriff aktivieren", "DEVICE_TOKEN": "Geräte-Token", "TOKEN_VALIDATORS": "Token-Validatoren", "TOKEN_VALIDATOR": "Token-Validator", "ADD_TOKEN_VALIDATOR": "Token-Validator hi<PERSON>", "EDIT_TOKEN_VALIDATOR": "<PERSON>ken-<PERSON><PERSON><PERSON> bear<PERSON>", "DELETE_TOKEN_VALIDATOR": "Token-Validator l<PERSON>", "AUDIENCE": "Zielgruppe", "SUBJECT_CLAIM": "Subjektanspruch", "CLIENT_JWKS": "Client-JWKs", "CERTIFICATES_PUBLIC_KEYS": "Zertifikate und öffentliche Schlüssel", "CLIENT_JWKS_URL": "Client-JWKs-URL", "VALIDATION_TYPE": "Validierungstyp", "CLIENT_CONNECTOR_DEVICE_TOKEN": "Client Connector-Geräte-Token", "GUEST_DOMAINS": "Gastdomänen", "ARBITRARY_GUEST_DOMAINS": "Beliebige Gastdomänen", "GUEST_USER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GUEST_DOMAIN": "Gastdomäne", "ADD": "Hinzufügen", "DEPARTMENT": "Abteilung", "ENABLE_TO_ALLOW_ALL_DOMAINS": "Aktivieren, um alle Domänen zuzulassen", "CUSTOM_ATTRIBUTE": "Benutzerdefiniertes Attribut", "BRANDING": "Branding", "CUSTOMIZE_EMAIL_ADDRESS_SENT_BY": "Anpassen der E-Mail-Adresse gesendet von", "CONFIGURATION_SETTINGS": "Konfigurationseinstellungen", "CUSTOMIZE_EMAIL_SUBJECT": "E-Mail-<PERSON><PERSON><PERSON> anpassen", "EMAIL_FROM_ADDRESS": "E-Mail-Absenderadresse", "EMAIL_SUBJECT": "E-Mail-Betreff", "EDIT_LOGO": "<PERSON>go bearbeiten", "LOGO": "Logo", "UPLOAD_LOGO": "Logo hochladen", "INVALID_IMAGE_TYPE": "Ungültiger Bildtyp", "BASE_64_CONVERSION_FAILED": "Bild konnte nicht in die verschlüsselte Version von Base 64 konvertiert werden", "DEPARTMENTS": "Abteilungen", "ADD_DEPARTMENT": "Abteilung hinzufügen", "EDIT_DEPARTMENT": "Abteilung bearbeiten", "DELETE_DEPARTMENT": "Abteilung löschen", "IMPORT_DEPARTMENT": "Abteilung importieren", "DEPARTMENT_NAME": "Abteilungsname", "VIEW_USER": "User anzeigen", "VIEW_GROUP": "Usergruppe anzeigen", "DASHBOARD": "Dashboard", "CREATED": "<PERSON><PERSON><PERSON><PERSON>", "UPDATED": "<PERSON>ktual<PERSON><PERSON>", "DELETED": "Löschen", "EXTERNAL_IDP": "Externer IdP", "HOSTED": "Intern", "TOTAL": "Gesamt", "CSV": "CSV", "MANUAL": "Benuterhandbuch", "EMAIL_LOGIN": "E-Mail-OTP", "FIDO_LOGIN": "FIDO-Authentifizierung", "PWD_LOGIN": "Passwort", "TOTP_LOGIN": "TOTP-Anmeldung", "SMS_LOGIN": "SMS-Anmeldung", "SAML_LOGIN": "SAML", "OIDC_LOGIN": "OIDC", "JIT": "jit", "SCIM": "SCIM", "ADMIN_ASSIGNMENT": "Administratorzuweisung", "RUNTIME_ASSIGNMENT": "Laufzeitzuweisung", "ZCC_ASSIGNMENT": "ZCC-Zuweisung", "ONE_DAY": "1 Tag", "TWO_DAYS": "2 Tage", "SEVEN_DAYS": "7 Tage", "FOURTEEN_DAYS": "14 Tage", "THIRTY_DAYS": "30 Tage", "SIXTY_DAYS": "60 Tage", "NINTY_DAYS": "90 Tage", "NO_DATA_AVAILABLE": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "ZSLOGIN_FOR_ADMINISTRATORS": "ZSLogin für Administratoren", "ZIDENTITY_FOR_ADMINISTRATORS": "ZIdentity für Administratoren", "REVERT_TO_PROVISIONED": "Auf 'Bereitgestellt' zurücksetzen", "REVERT": "Z<PERSON>ücksetzen", "ZSLOGIN_MIGRATION_STATUS": "ZSLogin-Migrationsstatus", "ZIDENTITY_MIGRATION_STATUS": "ZIdentity-Migrationsstatus", "IDP_AUTHENTICATION_NOT_SUCCESSFUL": "Überprüfung der externen Identitätsanbieterkonfiguration fehlgeschlagen", "UPDATE_PSEUDO_DOMAIN": "Pseudo-Domäne aktualisieren", "CURRENT_PSEUDO_DOMAIN_NAME": "Aktueller Name der Pseudo-Domäne", "NEW_PSEUDO_DOMAIN_NAME": "Neuer Name der Pseudo-Domäne", "UPDATE_PSEUDO_DOMAIN_SUCCESS": "Pseudo-<PERSON><PERSON>ne erfolgreich aktualisiert!", "MULTIFACTOR_BYPASS": "Multifaktor umgehen", "SKIP_SECOND_FACTOR_AUTHENTICATION": "Zweistufige Authentifizierung überspringen", "SKIP_UNTIL": "Überspringen bis", "INTEGRATION": "Integration", "API_CLIENTS": "API-Clients", "API_RESOURCES": "API-Ressourcen", "ADD_API_CLIENT": "API-Client hinzufügen", "ADD_API_RESOURCE": "API-Ressource hinzufügen", "EDIT_API_RESOURCE": "API-Ressource bearbeiten", "API_RESOURCE": "API-Ressource", "ACCESS_TOKEN_VALIDITY": "Zugriffstoken-Gültigkeit", "MINUTES": "Minute", "HOURS": "<PERSON><PERSON><PERSON><PERSON>", "PRIVATE_KEY_JWT": "Privater Schlüssel JWT", "CLIENT_JWK_URL": "Client-JWKs-URL", "CERTIFICATE_PUBLIC_KEY": "Zertifikate/öffentliche Schlüssel", "CLIENT_JWK": "Client-JWK", "CLIENT_SECRETS": "Client-Geheimschlüssel", "CLIENT_AUTHENTICATION": "Client-Authentifizierung", "CLIENT_INFORMATION": "Client-Informationen", "ZSCALER_API": "ZSCALER-APIs", "RESOURCE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NEVER": "nie", "CERTIFICATES_AND_PUBLIC_KEYS": "Zertifikate und öffentliche Schlüssel", "DELETE_API_CLIENT": "API-Client löschen", "DELETE_API_CLIENT_MESSAGE": "<PERSON><PERSON> dieser API-Client wirklich gelöscht werden? Die Änderung kann nicht mehr rückgängig gemacht werden.", "ACCESS_TOKEN_REQUIRED_MESSAGE": "Lebensdauer für Zugriffs-Token ist verbindlich", "EXPIRES": "<PERSON><PERSON><PERSON>", "SECRET": "Geheimschlüssel", "EDIT_API_CLIENT": "API-Client bearbeiten", "VIEW_API_CLIENT": "API-Client anzeigen", "CREATED_ON": "Erstellen am", "EXPIRES_ON": "Läuft ab am", "API_CLIENT": "API-Client", "CLIENT_SECRET_WARNING": "Client-Geheimschlüssel kopieren Er wird später nicht wieder angezeigt.", "API_CLIENTS_AND_RESOURCES": "API-Clients und -Ressourcen", "LOGIN_ID_ATTRIBUTE": "Login-ID-Attribut", "LOGOUT": "Abmelden", "ENVIRONMENT": "Umgebung", "SYSTEM": "System", "ZIDENTITY_ROLES": "ZIdentity-Rollen", "CLIENT": "Client", "RESOURCES": "Ressourcen", "BASIC": "Grundlegend", "ADVANCED": "Advanced", "PROVISIONING": "Bereitstellung", "ZIDENTITY_DASHBOARD": "ZIdentity-Dashboard", "AUTHENTICATION_LEVELS": "Authentifizierungsebenen", "LEVEL_NAME": "Ebenenname", "VALIDITY": "Gültigkeit", "DURATION": "<PERSON><PERSON>", "MESSAGE_TO_USER": "<PERSON><PERSON><PERSON><PERSON> an den User (optional)", "PARENT": "Übergeordnet", "SUB_LEVEL_NAME": "<PERSON><PERSON>eb<PERSON><PERSON><PERSON>", "DAYS": "Tage", "LEVELS_TO_AUTHENTICATION_CONTEXT_MAPPING": "Ebenen für Authentifizierungskontextzuordnung", "LEVELS": "<PERSON><PERSON><PERSON>", "AUTHENTICATION_CONTEXT": "Authentifizierungskontext", "DELETE_AL": "Authentifizierungsebene löschen", "DELETE_AL_CONFIRM_MSG": "Diese Aktion kann nicht mehr rückgängig gemacht werden und löscht die Authentifizierungsebene vollständig. Wollen Sie wirklich fortfahren?", "MOVE": "MOVE"}