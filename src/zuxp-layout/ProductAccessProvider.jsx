import { createContext, useContext } from 'react';

import PropTypes from 'prop-types';

const ProductAccessContext = createContext(undefined);

export const ProductAccessProvider = ({ children, products }) => {
  return (
    <ProductAccessContext.Provider value={{ products }}>{children}</ProductAccessContext.Provider>
  );
};

export const useProductAccess = () => {
  const context = useContext(ProductAccessContext);

  if (!context) {
    throw new Error('useProductAccess must be used within a ProductAccessProvider');
  }

  return context;
};

ProductAccessProvider.propTypes = {
  children: PropTypes.any,
  products: PropTypes.any,
};
