.selector.checkbox .icon {
  font-size: 1.2rem;
}

.help-container {
  display: none;
}

.toast-container {
  top: 62px;
}

.column-config-container .show-list-icon {
  font-size: 1.2rem;
}

.configure-column-container {
  .checkbox.large {
    font-size: 1.2rem;
  }
  .button {
    font-size: 0.75rem;
  }
}

.configure-column-list-container {
  max-height: 360px;
  overflow: auto;
}
.drag-and-drop-item-container .content-container {
  font-size: 0.8rem;
  font-weight: normal;
}

.checkbox.large {
  font-size: 1.3rem;
}

#entitlements {
  #services-users-and-groups {
    .services-tabs {
      .services-tabs-table {
        max-height: calc(100vh - 314px);
      }
    }
    .assign-entities {
      height: calc(100vh - 88px);
      .assign-view {
        height: calc(100vh - 188px);
        .right-panel {
          .assign-admin-entitlements {
            max-height: calc(100vh - 438px);
            &.review-mode {
              max-height: calc(100vh - 322px);
            }
          }
          .assign-service-entitlements {
            max-height: calc(100vh - 388px);
            &.review-mode {
              max-height: calc(100vh - 322px);
            }
          }
        }
      }
    }
  }
}

.action-section {
  bottom: 0;
}

.tag {
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-container {
  padding: 0;
}

.modal-container {
  // adjust zuxp nav height 3.5rem as of now
  height: calc(100% - 3.5rem);
  margin-top: 3.5rem;
}

.page-container .table-container {
  max-height: calc(100vh - 280px);
}
