'use client';

import { I18nextProvider } from 'react-i18next';

import i18next from 'i18next';
import PropTypes from 'prop-types';

import StoreProvider from '../StoreProvider';
import { APP_ID, PORTAL_ROOT_ID } from '../config';
import AppLayout from './AppLayout';
import { ProductAccessProvider } from './ProductAccessProvider';

const AppProvider = ({ children, products }) => (
  <>
    <div id={APP_ID}>
      <ProductAccessProvider products={products}>
        <I18nextProvider i18n={i18next}>
          <StoreProvider>
            <AppLayout>{children}</AppLayout>
          </StoreProvider>
        </I18nextProvider>
      </ProductAccessProvider>
    </div>
    <div id={PORTAL_ROOT_ID} />
  </>
);

AppProvider.propTypes = {
  children: PropTypes.any,
  products: PropTypes.any,
};
export default AppProvider;
