import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faAngleRight, faAngleUp } from '@fortawesome/pro-light-svg-icons';
import { faExternalLink, faSync, faUsers } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  InlineText,
  Search,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { filter, includes, noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { getRoles, getScopes, syncRoles } from '../../ducks/admin-entitlements';
import {
  selectRoleTableConfig,
  selectServiceRoleDetail,
  selectServiceScopeDetail,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
} from '../../ducks/admin-entitlements/selectors';
import { getList } from '../../ducks/roles';
import { selectTableDetail as selectRolesListTableDetail } from '../../ducks/roles/selectors';

import ScopeRoleDetail from './ScopeRoleDetail';

const defaultProps = {
  detail: {},
  scopeSupport: false,
};

const SyncRolesAndScope = ({ detail, scopeSupport }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall({ hasLoader: true });
  const { id, manageRolesUrl, serviceName } = detail;

  const isZiamService = serviceName === 'ZIAM';
  const ziamRolesListTableDetail = useSelector(selectRolesListTableDetail);

  const roleTableConfig = useSelector(selectRoleTableConfig);

  const tserviceScopes = selectServiceScopeDetail(useSelector(selectServicesScopeDetail), id);
  const tserviceRoles = selectServiceRoleDetail(useSelector(selectServicesRoleDetail), id);
  const ziamServiceRoles = ziamRolesListTableDetail.data;

  const [searchTerm, setSearchTerm] = useState('');
  const [activeRoles, setActiveRoles] = useState(tserviceRoles);

  const refreshServiceDetail = () => {
    if (isZiamService) {
      apiCall(getList()).catch(noop);
    } else {
      apiCall(getRoles({ id })).catch(noop);
    }

    if (scopeSupport) {
      apiCall(getScopes({ id })).catch(noop);
    }
  };

  useEffect(() => {
    refreshServiceDetail();
  }, []);

  useEffect(() => {
    let serviceRoles = tserviceRoles;
    if (isZiamService) {
      serviceRoles = ziamServiceRoles;
    }

    if (searchTerm) {
      const filteredRoles = filter(scopeSupport ? tserviceScopes : serviceRoles, ({ name }) => {
        return includes((name + '').toLowerCase(), (searchTerm + '').toLowerCase());
      });

      setActiveRoles(filteredRoles);
    } else {
      setActiveRoles(scopeSupport ? tserviceScopes : serviceRoles);
    }
  }, [searchTerm, tserviceRoles, tserviceScopes, ziamServiceRoles]);

  const tableColumnConfig = useMemo(() => {
    roleTableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'idx') {
        const ItemCount = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              getIsExpanded,
              // eslint-disable-next-line react/prop-types
              toggleExpanded,
              // eslint-disable-next-line react/prop-types
              index,
            },
            // eslint-disable-next-line react/prop-types
            table,
          } = props;

          const isExpanded = getIsExpanded();

          const onToggleExpandClick = () => {
            // eslint-disable-next-line react/prop-types
            table?.toggleAllRowsExpanded(false);
            toggleExpanded(!isExpanded);
          };

          return (
            <>
              <div
                className={`is-flex has-ai-c ${scopeSupport ? 'pointer' : ''}`}
                onKeyDown={noop}
                onClick={onToggleExpandClick}
              >
                {scopeSupport && (
                  <FontAwesomeIcon
                    icon={isExpanded ? faAngleUp : faAngleRight}
                    style={{ marginLeft: '0.25em', marginRight: '0.5em' }}
                  />
                )}

                <span> {index + 1} </span>
              </div>
            </>
          );
        };

        columnDetail.cell = ItemCount;
      }

      if (columnDetail.id === 'roleName') {
        const InlineTextComponent = (props) => <InlineText {...props} />;

        columnDetail.cell = InlineTextComponent;
      }

      return columnDetail;
    });

    const newColumns = [...(roleTableConfig?.columns || [])];

    if (scopeSupport) {
      remove(newColumns, { id: 'roleName' });
    } else {
      remove(newColumns, { id: 'scopeAndRoleName' });
    }

    return newColumns;
  }, [roleTableConfig?.columns]);

  const ExpandedRowContainer = (props) => <ScopeRoleDetail {...props} />;

  const onSyncRolesAndScopeClick = () => {
    apiCall(syncRoles({ id }))
      .catch(noop)
      .finally(() => refreshServiceDetail());
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = ziamRolesListTableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
      name: searchTerm,
    };

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <>
      <Search
        onSearch={onSearchEnter}
        term={searchTerm}
        label={scopeSupport ? 'SCOPES_N_ROLES' : 'ROLES'}
      />

      <div className="roles-section" style={{ marginTop: '16px' }}>
        <TableContainer
          {...roleTableConfig}
          columns={tableColumnConfig}
          data={activeRoles}
          hidePagination={!isZiamService}
          pagination={isZiamService ? { ...ziamRolesListTableDetail, onLoadMoreClick } : {}}
          showFooter={false}
          renderExpandedRowContainer={ExpandedRowContainer}
        />
        <div className="is-flex has-jc-sb">
          {manageRolesUrl && (
            <a href={manageRolesUrl} target="_blank" rel="noreferrer" className="link">
              <Button type="tertiary" containerClass="no-p-l">
                <FontAwesomeIcon icon={faUsers} className="icon left" />
                <span>{t('MANAGE_ROLES')}</span>
                <FontAwesomeIcon icon={faExternalLink} className="icon right" />
              </Button>
            </a>
          )}

          <Button
            type="tertiary"
            onClick={onSyncRolesAndScopeClick}
            containerClass="no-p-l no-p-r has-as-e"
          >
            <FontAwesomeIcon icon={faSync} className="icon left" />
            <span>{t('SYNC')}</span>
          </Button>
        </div>
      </div>
    </>
  );
};

SyncRolesAndScope.defaultProps = defaultProps;

SyncRolesAndScope.propTypes = {
  detail: PropTypes.object,
  scopeSupport: PropTypes.bool,
};

export default SyncRolesAndScope;
