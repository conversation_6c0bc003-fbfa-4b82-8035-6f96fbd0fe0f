import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faArrowLeft, faCircle, faCircleDot, faUser } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  DropDown,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  MultiSelection,
  Search,
  Selector,
  TableContainer,
  TextWithTooltip,
  ToggleButton,
  getApiPOSTNotificationOptions,
  useApiCall,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { find, findIndex, isEqual, noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  assignEntities,
  getAllEntitiesForAssignment,
  getRoles,
  getScopes,
  setSameRoleForAllEntities,
  setSameScopeForAllEntities,
  setSelectedEntities,
  setSelectedRole,
  setSelectedScope,
} from '../../ducks/admin-entitlements';
import {
  selectAssignEntitiesTableConfig,
  selectAssignEntitiesTableDetail,
  selectReviewAssignEntitiesTableConfig,
  selectScopeRoleDetail,
  selectScopeRoleList,
  selectScopesRoleDetail,
  selectServiceConstraints,
  selectServiceRoleDetail,
  selectServiceRoleList,
  selectServiceScopeDetail,
  selectServiceScopeList,
  selectServicesRoleDetail,
  selectServicesScopeDetail,
  selectedEntitiesTableDetail,
} from '../../ducks/admin-entitlements/selectors';
import { showErrorNotification } from '../../ducks/global';
import { getList } from '../../ducks/roles';
import {
  selectRolesList,
  selectTableDetail as selectRolesListTableDetail,
} from '../../ducks/roles/selectors';

import GroupUsersTableModal from './GroupUsersTableModal';
import { getAssignEntitiesApiPayload } from './helper';

const AssignEntityView = ({ entityType, onBackClick, serviceDetail }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();

  const isZiamService = serviceDetail.serviceName === 'ZIAM';
  const isMppService = serviceDetail.serviceName === 'MPP';

  const labelText = entityType === 'GROUP' ? 'GROUPS' : 'USERS';
  const serviceConstraints = find(useSelector(selectServiceConstraints), [
    'serviceName',
    serviceDetail.serviceName,
  ]);

  const tableConfig = useSelector(selectAssignEntitiesTableConfig);
  const tableDetail = useSelector(selectAssignEntitiesTableDetail);
  const reviewAssignedEntitiesTableConfig = useSelector(selectReviewAssignEntitiesTableConfig);
  const assignedEntitiesDetail = useSelector(selectedEntitiesTableDetail);

  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [canvasState, setCanvasState] = useState(1);
  const [sameRoles, setSameRoles] = useState(false);
  const [roleForAllEntities, setRoleForAllEntities] = useState([]);
  const [scopeForAllEntities, setScopeForAllEntities] = useState([]);
  const [resetSelection, setResetSelection] = useState(false);
  const [groupUsersTableModalData, setGroupUsersTableModalData] = useState(false);
  const [showGroupUsersTableModal, setShowGroupUsersTableModal] = useState(false);

  const [showUpdateStatusWarning, setShowUpdateStatusWarning] = useState(false);
  const [warningSummary, setWarningSummary] = useState('');

  const rolesListTableDetail = useSelector(selectRolesListTableDetail);
  const ziamRolesList = useSelector(selectRolesList);

  const { onDropDownOpen, onLoadMoreClick: onLoadMoreZiamRolesClick } = useDropDownActions({
    detail: rolesListTableDetail,
    apiCallFunc: getList,
  });

  const dDRef = useRef({
    onDropDownOpen,
    onLoadMoreZiamRolesClick,
    rolesListTableDetail,
    ziamRolesList,
  });

  useEffect(() => {
    dDRef.current = {
      onDropDownOpen,
      onLoadMoreZiamRolesClick,
      rolesListTableDetail,
      ziamRolesList,
    };
  }, [onDropDownOpen, onLoadMoreZiamRolesClick, rolesListTableDetail, ziamRolesList]);

  const [searchTerm, setSearchTerm] = useState('');

  const serviceScopesDetail = selectServiceScopeDetail(
    useSelector(selectServicesScopeDetail),
    serviceDetail.id,
  );
  const serviceScopesList = useMemo(
    () => selectServiceScopeList(serviceScopesDetail),
    [serviceScopesDetail],
  );

  const serviceRolesDetail = selectServiceRoleDetail(
    useSelector(selectServicesRoleDetail),
    serviceDetail.id,
  );

  const serviceRolesList = useMemo(
    () => selectServiceRoleList(serviceRolesDetail),
    [serviceRolesDetail],
  );

  const scopesRoleDetail = useSelector(selectScopesRoleDetail);

  const getRoleList = (selectedScope) => {
    if (serviceConstraints?.scopeSupport && !serviceConstraints?.multiScopeSupport) {
      // as of now sigle scope selection
      const scopeId = selectedScope?.[0]?.value;

      const scopeRoles = selectScopeRoleDetail(scopesRoleDetail, scopeId);

      return selectScopeRoleList(scopeRoles);
    } else {
      return serviceRolesList;
    }
  };

  useEffect(() => {
    apiCall(getAllEntitiesForAssignment({ type: entityType })).catch(noop);
    if (serviceConstraints?.scopeSupport) {
      apiCall(getScopes({ id: serviceDetail.id })).catch(noop);
      if (serviceConstraints?.multiScopeSupport) {
        apiCall(getRoles({ id: serviceDetail.id, isZiamService })).catch(noop);
      }
    } else if (!isZiamService) {
      apiCall(getRoles({ id: serviceDetail.id, isZiamService })).catch(noop);
    }

    dispatch(setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }));
  }, []);

  useEffect(() => {
    if (sameRoles) {
      dispatch(setSameRoleForAllEntities(roleForAllEntities, selectedRowDetail));
      if (serviceConstraints?.scopeSupport) {
        dispatch(setSameScopeForAllEntities(scopeForAllEntities, selectedRowDetail));
      }
    }
  }, [selectedRowDetail]);

  const cancelAssignment = () => {
    setSelectedRowDetail([]);
    onBackClick();
  };

  const backToAssignTable = () => {
    setCanvasState(1);
    setSelectedRowDetail([]);
    setSearchTerm('');
    dispatch(setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }));
    apiCall(getAllEntitiesForAssignment({ type: entityType })).catch(noop);
  };

  const onSearchEnter = (term) => {
    setResetSelection(true);

    apiCall(getAllEntitiesForAssignment({ type: entityType, name: term }))
      .catch(noop)
      .finally(() => {
        setResetSelection(false);
      });
    setSearchTerm(term);
  };

  const getSaveButtonDisabledStatus = () => {
    if (canvasState !== 1) return false;

    const data = tableDetail?.data || [];
    const selectedRows = selectedRowDetail || [];

    if (selectedRows.length === 0) return true;

    // Check each selected row for valid role and scope
    return selectedRows.some((row) => {
      const selectedRowData = find(data, ['id', row.id]);
      return !isValidRow(selectedRowData);
    });
  };

  const isValidRow = (row) => {
    // Check if the row has a selected role
    if (!row?.selectedRole || row.selectedRole.length === 0) return false;

    // If multi-scope support is enabled, check if the row has a selected scope
    if (
      serviceConstraints?.multiScopeSupport &&
      (!row.selectedScope || row.selectedScope.length === 0)
    ) {
      return false;
    }

    return true;
  };

  const renderActionsSection = () => {
    return (
      <div className="action-section buttons">
        {canvasState === 2 && (
          <Button
            containerClass="back-button"
            onClick={() => {
              backToAssignTable();
            }}
          >
            {t('BACK')}
          </Button>
        )}
        <Button
          containerClass="confirm-button"
          disabled={getSaveButtonDisabledStatus()}
          onClick={() => {
            saveEntities();
          }}
        >
          {t(canvasState === 1 ? 'NEXT' : 'ASSIGN')}
        </Button>
        <Button
          type="tertiary"
          containerClass="no-p-l cancel-button"
          onClick={() => {
            cancelAssignment();
          }}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  const reviewAssignedEntitiesColumnConfig = useMemo(() => {
    const newColumns = reviewAssignedEntitiesTableConfig?.columns || [];

    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'loginName' });
    }

    if (!serviceConstraints?.scopeSupport) {
      remove(newColumns, { id: 'scope' });
    } else if (isMppService) {
      const scopeIndex = findIndex(newColumns, { id: 'scope' });
      const roleIndex = findIndex(newColumns, { id: 'role' });
      if (scopeIndex < roleIndex && scopeIndex && roleIndex) {
        [newColumns[scopeIndex], newColumns[roleIndex]] = [
          newColumns[roleIndex],
          newColumns[scopeIndex],
        ];
      }
    }

    return [...(newColumns || [])];
  }, [reviewAssignedEntitiesTableConfig?.columns, canvasState]);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'role') {
        const RoleSelectionComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { id, selectedRole, selectedScope } = props?.row?.original || {};

          let multiSelectProps = {};
          if (serviceConstraints?.multiRoleSupport || isZiamService) {
            multiSelectProps.renderItemsSelection = (props) => {
              return renderRolesItemSelection(props);
            };
          }

          if (isZiamService) {
            multiSelectProps.list = dDRef.current?.ziamRolesList;
            multiSelectProps.onOpen = dDRef.current?.onDropDownOpen;
            multiSelectProps.loadMoreDetail = {
              ...dDRef.current?.rolesListTableDetail,
              onLoadMoreClick: dDRef.current?.onLoadMoreZiamRolesClick,
            };
          }

          return (
            <DropDown
              list={getRoleList(selectedScope)}
              selectedList={selectedRole}
              onSelection={(detail) => {
                dispatch(setSelectedRole({ id, selectedValue: detail }));
              }}
              hasSearch
              isMulti={serviceConstraints?.multiRoleSupport || isZiamService}
              {...multiSelectProps}
              containerClass="full-width"
              containerStyle={{ maxWidth: '250px' }}
              disabled={sameRoles}
            />
          );
        };

        columnDetail.cell = RoleSelectionComponent;
      }
      if (columnDetail.id === 'scope') {
        const ScopeSelectionComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { id, selectedScope } = props?.row?.original || {};

          let multiSelectProps = {};
          if (serviceConstraints.multiScopeSupport) {
            multiSelectProps.isMulti = true;
            multiSelectProps.renderItemsSelection = (props) => {
              return renderScopesItemSelection(props);
            };
          }
          return (
            <DropDown
              list={serviceScopesList}
              selectedList={selectedScope}
              onSelection={(detail) => {
                if (serviceConstraints?.scopeSupport && !serviceConstraints?.multiScopeSupport) {
                  apiCall(
                    getRoles({ id: serviceDetail.id, scopeId: detail[0]?.value, isZiamService }),
                    {
                      showLoader: true,
                    },
                  )
                    .then(() => {
                      dispatch(setSelectedScope({ id, selectedValue: detail }));
                      dispatch(setSelectedRole({ id, selectedValue: [] }));
                    })
                    .catch(noop);
                } else {
                  dispatch(setSelectedScope({ id, selectedValue: detail }));
                }
              }}
              hasSearch
              {...multiSelectProps}
              containerClass="full-width"
              containerStyle={{ maxWidth: '250px' }}
              disabled={sameRoles}
            />
          );
        };

        columnDetail.cell = ScopeSelectionComponent;
      }
      if (columnDetail.id === 'groupUsers') {
        const UsersCellComponent = (props) => {
          return (
            <Button
              type="tertiary"
              onClick={() => {
                // eslint-disable-next-line react/prop-types
                setGroupUsersTableModalData(props?.row?.original);
                setShowGroupUsersTableModal(true);
              }}
            >
              <FontAwesomeIcon icon={faUser} className="icon left" />
              {t('USERS')}
            </Button>
          );
        };

        columnDetail.cell = UsersCellComponent;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { displayName, name, idp } = props?.row?.original || {};

          let derivedName = displayName || name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${derivedName} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    const newColumns = [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 50,
        minSize: 50,
        maxSize: 50,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];

    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'loginName' });
    }

    if (entityType === 'USER') {
      remove(newColumns, { id: 'groupUsers' });
    }

    if (!serviceConstraints?.scopeSupport) {
      remove(newColumns, { id: 'scope' });
    } else if (isMppService) {
      const scopeIndex = findIndex(newColumns, { id: 'scope' });
      const roleIndex = findIndex(newColumns, { id: 'role' });
      if (scopeIndex < roleIndex && scopeIndex && roleIndex) {
        [newColumns[scopeIndex], newColumns[roleIndex]] = [
          newColumns[roleIndex],
          newColumns[scopeIndex],
        ];
      }
    }

    return newColumns;
  }, [tableConfig?.columns, sameRoles, serviceRolesList, serviceScopesList, scopesRoleDetail]);

  const onLoadMoreClick = () => {
    if (canvasState === 1) {
      const { pageSize, pageOffset } = tableDetail;

      apiCall(
        getAllEntitiesForAssignment({
          requireTotal: false,
          pageOffset: pageOffset + pageSize,
          pageSize,
          type: entityType,
        }),
      ).catch(noop);
    } else {
      const { pageSize, pageOffset } = assignedEntitiesDetail;

      dispatch(
        setSelectedEntities({
          selectedEntities: selectedRowDetail,
          pageOffset: pageOffset + pageSize,
        }),
      );
    }
  };

  const onRowSelection = (data) => {
    if (!isEqual(selectedRowDetail, data)) {
      setSelectedRowDetail(data);
    }
  };

  const renderTableComponent = () => {
    if (canvasState === 1) {
      return (
        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          containerClass={'assign-admin-entitlements'}
          data={tableDetail.data}
          onRowSelection={(data) => {
            onRowSelection(data);
          }}
          pagination={{ ...tableDetail, onLoadMoreClick }}
          resetSelection={resetSelection}
        />
      );
    }
    return (
      <TableContainer
        {...reviewAssignedEntitiesTableConfig}
        columns={reviewAssignedEntitiesColumnConfig}
        containerClass={'assign-admin-entitlements review-mode'}
        data={assignedEntitiesDetail.data}
        pagination={{ ...assignedEntitiesDetail, onLoadMoreClick }}
      />
    );
  };

  const onCloseGroupUsersModal = () => {
    setShowGroupUsersTableModal(false);
  };

  const onToggleClick = () => {
    setSameRoles(!sameRoles);
    setRoleForAllEntities([]);
    dispatch(setSameRoleForAllEntities([]));
  };

  const onSameRoleSelection = (detail) => {
    dispatch(setSameRoleForAllEntities(detail, selectedRowDetail));
    setRoleForAllEntities(detail);
  };

  const onSameScopeSelection = (detail) => {
    if (!serviceConstraints?.multiScopeSupport) {
      apiCall(
        getRoles({
          id: serviceDetail.id,
          scopeId: detail[0]?.value,
          isZiamService,
        }),
        { showLoader: true },
      ).catch(noop);
    }

    dispatch(setSameScopeForAllEntities(detail, selectedRowDetail));
    setScopeForAllEntities(detail);
  };

  const renderRolesItemSelection = (props) => {
    return (
      <MultiSelection
        unselectedTitle={t('UNSELECTED_ROLES')}
        selectedTitle={t('SELECTED_ROLES')}
        {...props}
      />
    );
  };

  const renderScopesItemSelection = (props) => {
    return (
      <MultiSelection
        unselectedTitle={t('UNSELECTED_SCOPES')}
        selectedTitle={t('SELECTED_SCOPES')}
        {...props}
      />
    );
  };

  const renderSelectionSection = () => {
    const label = t(`SAME_ROLE_FOR_SELECTED_${labelText}`);
    const scopeSupportLabel = t(`SAME_SCOPE_AND_ROLE_FOR_SELECTED_${labelText}`);

    const dropDownProps = {
      list: getRoleList(serviceConstraints?.scopeSupport ? scopeForAllEntities : []),
      selectedList: roleForAllEntities,
      onSelection: onSameRoleSelection,
      placeholderList: [{ label: 'SELECT_ROLE', value: 'SELECT_ROLE' }],
      hasSearch: true,
      containerClass: 'role-selection-drop-down',
      containerStyle: { maxWidth: '250px' },
    };

    if (serviceConstraints?.multiRoleSupport || isZiamService) {
      dropDownProps.isMulti = true;
      dropDownProps.renderItemsSelection = (props) => {
        return renderRolesItemSelection(props);
      };
    }

    if (isZiamService) {
      dropDownProps.list = dDRef.current?.ziamRolesList;
      dropDownProps.onOpen = dDRef.current?.onDropDownOpen;
      dropDownProps.loadMoreDetail = {
        ...dDRef.current?.rolesListTableDetail,
        onLoadMoreClick: dDRef.current?.onLoadMoreZiamRolesClick,
      };
    }

    const scopeDropDownProps = {
      list: serviceScopesList,
      selectedList: scopeForAllEntities,
      onSelection: onSameScopeSelection,
      placeholderList: [{ label: 'SELECT_SCOPE', value: 'SELECT_SCOPE' }],
      hasSearch: true,
      containerClass: 'scope-selection-drop-down',
      containerStyle: { maxWidth: '250px' },
    };

    if (serviceConstraints?.multiScopeSupport) {
      scopeDropDownProps.isMulti = true;
      scopeDropDownProps.renderItemsSelection = (props) => {
        return renderScopesItemSelection(props);
      };
    }
    return (
      <div className="assign-role-selection-container">
        <ToggleButton
          containerClass={'role-selection-toggle-button'}
          type="success"
          isOn={sameRoles}
          onToggleClick={onToggleClick}
          onLabel={serviceConstraints?.scopeSupport ? scopeSupportLabel : label}
          offLabel={serviceConstraints?.scopeSupport ? scopeSupportLabel : label}
        />
        {sameRoles && serviceConstraints?.scopeSupport && !isMppService && (
          <DropDown {...scopeDropDownProps} />
        )}
        {sameRoles && <DropDown {...dropDownProps} />}
        {sameRoles && serviceConstraints?.scopeSupport && isMppService && (
          <DropDown {...scopeDropDownProps} />
        )}
      </div>
    );
  };

  const saveEntities = () => {
    if (canvasState === 1) {
      setCanvasState(2);
      dispatch(
        setSelectedEntities({
          selectedEntities: selectedRowDetail,
          pageOffset: 0,
        }),
      );
      return false;
    }

    const apiPayload = getAssignEntitiesApiPayload({
      selectedEntities: selectedRowDetail,
      isZiamService,
      serviceId: serviceDetail.id,
      type: entityType,
      constraints: serviceConstraints,
    });

    apiCall(assignEntities({ id: serviceDetail.id, payload: apiPayload, isZiamService }), {
      successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      hasNotification: false,
    })
      .then(() => {
        onBackClick();
        dispatch(
          setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }),
        );
        setResetSelection(true);
      })
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onSaveClick = () => {
    const apiPayload = getAssignEntitiesApiPayload({
      selectedEntities: selectedRowDetail,
      isZiamService,
      serviceId: serviceDetail.id,
      type: entityType,
    });

    apiCall(
      assignEntities({
        id: serviceDetail.id,
        payload: apiPayload,
        isZiamService,
        auditorOverride: true,
      }),
      {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
        hasNotification: false,
      },
    )
      .then(() => {
        onBackClick();
        dispatch(
          setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }),
        );
        setResetSelection(true);
      })
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onCloseClick = () => {
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const displayCanvasState = () => {
    let currentState = {
      label: canvasState === 1 ? t(`SELECT_${labelText}_AND_ROLE`) : t('SUMMARY'),
    };

    return (
      <div>
        <section className="typography-header3">{currentState.label}</section>
        <section className="typography-paragraph2 subtitle with-border-bottom">
          <div className="services-label">
            {t(serviceDetail.serviceDescription)} - {t('ADMINISTRATIVE')}
          </div>
          {serviceDetail.cloudName && (
            <div className="cloud-label">
              <span className="">{t('CLOUD_NAME')}</span>
              <span className="value">{t(serviceDetail.cloudName)}</span>
            </div>
          )}
          {serviceDetail.orgName && (
            <div className="org-label">
              <span className="">{t('ORG_NAME')}</span>
              <span className="value">{t(serviceDetail.orgName)}</span>
            </div>
          )}
        </section>
        {canvasState === 1 && renderSelectionSection()}
        <div className="is-flex has-jc-sb">
          {canvasState === 1 && (
            <div className="buttons" style={{ marginLeft: 'auto' }}>
              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerStyle={{ width: '260px' }}
              />
            </div>
          )}
        </div>
        {renderTableComponent()}
      </div>
    );
  };

  const renderBodySection = () => {
    return <GroupUsersTableModal detail={groupUsersTableModalData} />;
  };

  return (
    <div className="assign-entities">
      <section className="typography-header3 page-title services-heading">
        <FontAwesomeIcon icon={faArrowLeft} className="icon left pointer" onClick={onBackClick} />
        {t(`ASSIGN_${labelText}`)}
      </section>
      <Card containerClass="assign-view">
        <div className="left-panel">
          <div className={`select-view ${canvasState === 1 ? 'active' : ''}`}>
            <div className="icon-container">
              <FontAwesomeIcon icon={faCircleDot} className="icon current-state" />
              <div className="icon-bar"></div>
            </div>
            {t(`SELECT_${labelText}_AND_ROLE`)}
          </div>
          <div className={`summary-view ${canvasState === 2 ? 'active' : ''}`}>
            <div className="icon-container">
              <div className="icon-bar"></div>
              <FontAwesomeIcon icon={faCircle} className="icon end-state" />
            </div>
            {t('SUMMARY')}
          </div>
        </div>
        <div className="right-panel">{displayCanvasState()}</div>
        {showGroupUsersTableModal && (
          <Modal
            show={showGroupUsersTableModal}
            onEscape={onCloseGroupUsersModal}
            containerClass="group-users-modal crud-modal"
          >
            <ModalHeader text="USERS" onClose={onCloseGroupUsersModal} />
            <ModalBody> {renderBodySection()}</ModalBody>
            <ModalFooter cancelText="OK" showSave={false} onCancel={onCloseGroupUsersModal} />
          </Modal>
        )}
      </Card>
      {renderActionsSection()}

      {showUpdateStatusWarning && (
        <Modal
          show={showUpdateStatusWarning}
          onEscape={onCloseClick}
          containerClass="update-status-warning-container"
        >
          <ModalHeader text="AUDITOR_OVERRIDE" onClose={onCloseClick} />
          <ModalBody>{warningSummary}</ModalBody>
          <ModalFooter saveText="REMOVE" onSave={onSaveClick} onCancel={onCloseClick} />
        </Modal>
      )}
    </div>
  );
};

AssignEntityView.propTypes = {
  entityType: PropTypes.string,
  onBackClick: PropTypes.func,
  serviceDetail: PropTypes.object,
};

export default AssignEntityView;
