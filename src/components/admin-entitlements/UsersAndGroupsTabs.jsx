import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Card,
  DropDown,
  FieldGroup,
  HelpContainer,
  Input,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Tab,
  Tabs,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getServiceConstraints } from '../../ducks/admin-entitlements';
import {
  selectServiceConstraintDetail,
  selectServiceConstraints,
} from '../../ducks/admin-entitlements/selectors';

import { HELP_ARTICLES } from '../../config';
import SyncRolesModal from './SyncRoles';
import TabView from './TabView';
import { manageOptions } from './helper';

const TABS = {
  PRIMARY: 'PRIMARY',
  SECONDARY: 'SECONDARY',
};

const defaultProps = {
  serviceDetail: {},
  privileges: {
    hasFullAccess: false,
  },
  servicePermissions: {},
};

const UsersAndGroupsTabs = ({ onBackClick, serviceDetail, privileges, servicePermissions }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { serviceDescription, serviceName } = serviceDetail;
  const { hasFullAccess } = privileges;

  const [selectedTab, setSelectedTab] = useState(TABS.SECONDARY);
  const [hidePageHeader, setHidePageHeader] = useState(false);
  const [userCount, setUserCount] = useState(0);
  const [groupCount, setGroupCount] = useState(0);

  const [showSyncRoles, setShowSyncRoles] = useState(false);
  const [scopeSupport, setScopeSupport] = useState(false);

  const serviceConstraints = useSelector(selectServiceConstraints);

  useEffect(() => {
    apiCall(getServiceConstraints()).catch(noop);
  }, []);

  useEffect(() => {
    const { scopeSupport } = selectServiceConstraintDetail(serviceConstraints, serviceName);
    setScopeSupport(scopeSupport);
  }, [serviceConstraints]);

  const togglePageHeader = (value) => {
    setHidePageHeader(value);
  };

  const onUserDetailsFetch = (data) => {
    setUserCount(data);
  };

  const onGroupDetailsFetch = (data) => {
    setGroupCount(data);
  };

  const onCloseSyncRolesModal = () => {
    setShowSyncRoles(false);
  };

  const renderSyncRolesBodySection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('GENERAL_INFORMATION')}</section>

        <Card>
          <FieldGroup>
            <Input
              name="name"
              label="NAME"
              value={serviceDescription}
              readOnly
              style={{ color: 'black' }}
            />
          </FieldGroup>

          <SyncRolesModal detail={serviceDetail} scopeSupport={scopeSupport} />
        </Card>
      </>
    );
  };

  const renderTabsSelectionSection = () => {
    return (
      <Tabs>
        <Tab
          label={groupCount > 0 ? `${t('USER_GROUPS')} (${groupCount})` : t('USER_GROUPS')}
          isActive={selectedTab === TABS.SECONDARY}
          onClick={() => {
            setSelectedTab(TABS.SECONDARY);
          }}
        />
        <Tab
          label={userCount > 0 ? `${t('USERS')} (${userCount})` : t('USERS')}
          isActive={selectedTab === TABS.PRIMARY}
          onClick={() => {
            setSelectedTab(TABS.PRIMARY);
          }}
        />
      </Tabs>
    );
  };

  const renderPrimarySection = () => {
    return (
      <TabView
        service={serviceDetail}
        onAssignEntityClick={togglePageHeader}
        onDetailsFetch={onUserDetailsFetch}
        privileges={privileges}
        servicePermissions={servicePermissions}
        entityType={'USER'}
      />
    );
  };

  const renderSecondarySection = () => {
    return (
      <TabView
        service={serviceDetail}
        onAssignEntityClick={togglePageHeader}
        onDetailsFetch={onGroupDetailsFetch}
        privileges={privileges}
        servicePermissions={servicePermissions}
        entityType={'GROUP'}
      />
    );
  };

  const renderSelectedTabSection = () => {
    if (selectedTab === TABS.SECONDARY) {
      return renderSecondarySection();
    }

    return renderPrimarySection();
  };

  return (
    <div id="services-users-and-groups">
      <HelpContainer src={HELP_ARTICLES.ADMINISTRATIVE_ENTITLEMENTS_ADMINISTRATIVE} />

      {!hidePageHeader && (
        <div className="services-header">
          <div>
            <section className="typography-header3">
              <FontAwesomeIcon
                icon={faArrowLeft}
                className="icon left pointer"
                onClick={onBackClick}
              />
              {t(serviceDetail.serviceDescription)} - {t('ADMINISTRATIVE')}
            </section>
            <div className="typography-paragraph2 subtitle">
              {serviceDetail.cloudName && (
                <div className="cloud-label">
                  <span className="">{t('CLOUD_NAME')}</span>
                  <span className="value">{t(serviceDetail.cloudName)}</span>
                </div>
              )}
              {serviceDetail.orgName && (
                <div className="org-label">
                  <span className="">{t('ORG_NAME')}</span>
                  <span className="value">{t(serviceDetail.orgName)}</span>
                </div>
              )}
            </div>
          </div>
          {hasFullAccess && (
            <div>
              <DropDown
                list={manageOptions}
                onSelection={(payload) => {
                  const selectedOption = payload[0];

                  if (selectedOption?.value === 'VIEW_ROLES') {
                    setShowSyncRoles(true);
                  }
                }}
                placeholderList={[{ label: 'MANAGE', value: 'MANAGE' }]}
                kind="tertiary"
              />
            </div>
          )}
        </div>
      )}
      {!hidePageHeader && renderTabsSelectionSection()}

      {renderSelectedTabSection()}
      {showSyncRoles && (
        <Modal
          show={showSyncRoles}
          onEscape={onCloseSyncRolesModal}
          containerClass="entitlements-center-modal-container"
        >
          <ModalHeader text="APPLICATION_DETAIL" onClose={onCloseSyncRolesModal} />
          <ModalBody>{renderSyncRolesBodySection()}</ModalBody>
          <ModalFooter
            showSave={false}
            cancelText="CLOSE"
            onCancel={onCloseSyncRolesModal}
            cancelProps={{ containerClass: 'no-p-l' }}
          />
        </Modal>
      )}
    </div>
  );
};

UsersAndGroupsTabs.defaultProps = defaultProps;

UsersAndGroupsTabs.propTypes = {
  onBackClick: PropTypes.func,
  serviceDetail: PropTypes.object,
  privileges: PropTypes.object,
  servicePermissions: PropTypes.object,
};

export default UsersAndGroupsTabs;
