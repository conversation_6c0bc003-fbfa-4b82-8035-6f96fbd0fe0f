import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  DropDown,
  Field,
  MultiSelection,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { getApiCLientsList } from '../../ducks/access-policies';
import {
  selectApiClientsList,
  selectApiClientsTableDetail,
} from '../../ducks/access-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const AssignApiClients = () => {
  const { detail = {}, setDetail } = useContext(CRUDPageContext);

  const { apiClients } = detail;

  const apiClientsTableDetail = useSelector(selectApiClientsTableDetail);
  const apiClientsList = useSelector(selectApiClientsList);

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: apiClientsTableDetail,
    apiCallFunc: getApiCLientsList,
  });

  const [selectedOption, setSelectedOption] = useState([]);

  useEffect(() => {
    let newApiClients = [];

    if (apiClients) {
      newApiClients = apiClients.map(({ id, name }) => ({ label: name, value: id }));
    }

    setSelectedOption(newApiClients);
  }, [apiClients]);

  const onSelection = (selected) => {
    const selectedApiClients =
      selected.map((detail) => ({ name: detail.label, id: detail.value })) || [];

    setDetail((prevState) => ({
      ...prevState,
      apiClients: selectedApiClients,
    }));
  };

  return (
    <Field label="API_CLIENTS" htmlFor="apiClients">
      <DropDown
        list={apiClientsList}
        selectedList={selectedOption}
        onSelection={onSelection}
        renderItemsSelection={(props) => (
          <MultiSelection
            unselectedTitle="Unselected API CLIENTS"
            selectedTitle="Selected API CLIENTS"
            {...props}
          />
        )}
        isMulti
        hasSearch
        containerClass="full-width"
        onOpen={onDropDownOpen}
        loading={isDropDownLoading}
        loadMoreDetail={{ ...apiClientsTableDetail, onLoadMoreClick }}
        containerStyle={{ maxWidth: '550px' }}
      />
    </Field>
  );
};

export default AssignApiClients;
