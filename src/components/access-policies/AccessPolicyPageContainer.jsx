import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { HelpContainer } from '@zscaler/zui-component-library';

import { HELP_ARTICLES } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import AccessPolicyActions from './AccessPolicyActions';
import AccessPolicyCRUD from './AccessPolicyCRUD';
import AccessPolicyTable from './AccessPolicyTable';

const AccessPolicyPageContainer = () => {
  const { t } = useTranslation();

  const { modalMode } = useContext(CRUDPageContext);

  return (
    <>
      {!modalMode && <HelpContainer src={HELP_ARTICLES.ACCESS_POLICY} />}

      <article id="access-policy-page" className="page-container">
        <div className="is-flex has-jc-sb" style={{ marginBottom: '16px' }}>
          <div>
            <section className="typography-header3 page-title">
              {t('API_CLIENT_ACCESS_POLICY')}
            </section>
            <p>
              <FontAwesomeIcon icon={faInfoCircle} className="icon left" />
              If there is no rule defined for API Client, the client is permitted to call APIs
              without any restrictions.
            </p>
          </div>

          <AccessPolicyActions />
        </div>

        <AccessPolicyTable />

        <AccessPolicyCRUD />
      </article>
    </>
  );
};

export default AccessPolicyPageContainer;
