import { useContext } from 'react';

import {
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { add, remove, update } from '../../ducks/access-policies';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import AccessPolicyForm from './AccessPolicyForm';
import ViewAccessPolicy from './ViewAccessPolicy';
import { modalModeDetail } from './helper';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_POLICY_DETAIL = {};

const AccessPolicyCRUD = () => {
  const { apiCall } = useApiCall();

  const { modalMode, setModalMode, detail, setDetail, isFormReadOnly } =
    useContext(CRUDPageContext);

  const onCloseClick = () => {
    setModalMode(DEFAULT_MODAL_MODE);
    setDetail(DEFAULT_POLICY_DETAIL);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add({ ...detail }), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  if (modalMode === 'edit' || modalMode === 'add') {
    return (
      <AccessPolicyForm mode={modalMode} onCloseClick={onCloseClick} onSaveClick={onSaveClick} />
    );
  }

  return (
    <CRUDModal
      mode={modalMode}
      containerClass={`access-policy-crud ${modalMode}`}
      showSave={!isFormReadOnly}
      renderFormSection={(props) => <ViewAccessPolicy {...props} />}
      cancelText={isFormReadOnly ? 'CLOSE' : 'CANCEL'}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default AccessPolicyCRUD;
