import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import { getList } from '../../ducks/access-policies';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const AccessPolicyActions = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
    searchTerm,
    setSearchTerm,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(cloneDeep(defaultDetail));
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 100);
  };

  const onSearchEnter = (term) => {
    const searchField = 'name';

    if (searchField) {
      apiCall(getList({ [searchField]: term })).catch(noop);

      setSearchTerm(term);
    }
  };

  return (
    <div className="is-flex has-fd-c has-ai-e" style={{ gap: '8px' }}>
      {hasFullAccess ? (
        <>
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('POLICY_RULE')}</span>
          </Button>

          <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
        </>
      ) : null}
    </div>
  );
};

export default AccessPolicyActions;
