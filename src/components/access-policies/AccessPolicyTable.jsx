import { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  StatusTag,
  TableContainer,
  TextWithTooltip,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/access-policies';
import {
  selectDaysOfWeekEnums,
  selectFrequenciesEnums,
  selectOperationsEnums,
  selectTableConfig,
  selectTableDetail,
  selectTimeZonesEnums,
  selectWeeksOfMonthEnums,
} from '../../ducks/access-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { getPolicyCriteriaExplination } from './helper';

const AccessPolicyTable = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const operationsList = useSelector(selectOperationsEnums);
  const frequenciesList = useSelector(selectFrequenciesEnums);
  const timeZonesList = useSelector(selectTimeZonesEnums);
  const daysOfWeekList = useSelector(selectDaysOfWeekEnums);
  const weeksOfMonthList = useSelector(selectWeeksOfMonthEnums);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'criteria') {
        const Explaination = (props) => (
          <TextWithTooltip>
            {getPolicyCriteriaExplination(
              // eslint-disable-next-line react/prop-types
              props?.row?.original?.accessPolicyCriteria,
              t,
              { operationsList, frequenciesList, timeZonesList, daysOfWeekList, weeksOfMonthList },
            )}
          </TextWithTooltip>
        );

        columnDetail.cell = Explaination;
      }

      if (columnDetail.id === 'action') {
        const RuleActionCellComponent = (props) =>
          // eslint-disable-next-line react/prop-types
          t(props?.row?.original?.ruleAction);

        columnDetail.cell = RuleActionCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const isActive = props?.row?.original?.ruleStatus === 'ACTIVE';

          return (
            <StatusTag
              value={isActive}
              truthyLabel="ACTIVE"
              falsyLabel="EXPIRED"
              type="ALLOWED_BLOCKED"
            />
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            editIcon={isFormReadOnly ? faEye : faPencilAlt}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            showDelete={!isFormReadOnly}
            showAddUsers
            addUsersIcon={faEye}
            onAddUsersClick={onViewClick}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [
    tableConfig?.columns,
    isFormReadOnly,
    frequenciesList,
    operationsList,
    timeZonesList,
    daysOfWeekList,
    weeksOfMonthList,
  ]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onViewClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'view');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default AccessPolicyTable;
