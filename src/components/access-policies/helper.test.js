import { getErrorMessageFromApiResponse } from '@zscaler/zui-component-library';

import {
  showErrorNotification,
  showSuccessNotification,
  showWarningNotification,
} from '../../ducks/global';
import { apiErrorNotifier, apiSuccessNotifier, getAttachmentDetail } from '../../ducks/helper';

jest.mock('../../ducks/global', () => ({
  showErrorNotification: jest.fn(),
  showSuccessNotification: jest.fn(),
  showWarningNotification: jest.fn(),
}));

jest.mock('@zscaler/zui-component-library', () => ({
  getErrorMessageFromApiResponse: jest.fn(),
}));

describe('helper functions', () => {
  let dispatch;

  beforeEach(() => {
    dispatch = jest.fn();
  });

  describe('apiSuccessNotifier', () => {
    it('should dispatch success notification if showNotification is true and payload has message', async () => {
      const response = { data: 'test' };
      const payload = { message: 'Success' };
      const notifier = apiSuccessNotifier(dispatch, true, payload);

      await notifier(response);

      expect(dispatch).toHaveBeenCalledWith(showSuccessNotification(payload));
    });

    it('should resolve with response if showNotification is false', async () => {
      const response = { data: 'test' };
      const payload = { message: 'Success' };
      const notifier = apiSuccessNotifier(dispatch, false, payload);

      const result = await notifier(response);

      expect(result).toEqual(response);
      expect(dispatch).not.toHaveBeenCalled();
    });
  });

  describe('apiErrorNotifier', () => {
    it('should dispatch error notification if showNotification is true and errorMessage exists', async () => {
      const error = { apiErrorLevel: 'ERROR' };
      const payload = { message: 'Error' };
      const errorMessage = 'Error message';
      getErrorMessageFromApiResponse.mockReturnValue(errorMessage);
      const notifier = apiErrorNotifier(dispatch, true, payload);

      try {
        await notifier({ error });
      } catch (e) {
        expect(dispatch).toHaveBeenCalledWith(
          showErrorNotification({ autoHide: false, ...payload, message: errorMessage }),
        );
        expect(e).toEqual({ notificationId: undefined, ...error });
      }
    });

    it('should dispatch warning notification if showNotification is true and errorMessage exists with WARN level', async () => {
      const error = { apiErrorLevel: 'WARN' };
      const payload = { message: 'Warning' };
      const errorMessage = 'Warning message';
      getErrorMessageFromApiResponse.mockReturnValue(errorMessage);
      const notifier = apiErrorNotifier(dispatch, true, payload);

      try {
        await notifier({ error });
      } catch (e) {
        expect(dispatch).toHaveBeenCalledWith(
          showWarningNotification({ autoHide: false, ...payload, message: errorMessage }),
        );
        expect(e).toEqual({ notificationId: undefined, ...error });
      }
    });

    it('should reject with error if showNotification is false', async () => {
      const error = { apiErrorLevel: 'ERROR' };
      const payload = { message: 'Error' };
      const notifier = apiErrorNotifier(dispatch, false, payload);

      try {
        await notifier({ error });
      } catch (e) {
        expect(e).toEqual(error);
        expect(dispatch).not.toHaveBeenCalled();
      }
    });
  });

  describe('getAttachmentDetail', () => {
    it('should return attachment details', () => {
      const data = 'file data';
      const headers = {
        'content-type': 'application/pdf',
        'content-disposition': 'attachment; filename="test.pdf"',
      };

      const result = getAttachmentDetail({ data, headers });

      expect(result).toEqual({
        data,
        fileName: 'test.pdf',
        contentType: 'application/pdf',
      });
    });

    it('should return default file name if content-disposition header is missing', () => {
      const data = 'file data';
      const headers = {
        'content-type': 'application/pdf',
      };

      const result = getAttachmentDetail({ data, headers });

      expect(result).toEqual({
        data,
        fileName: 'sample.txt',
        contentType: 'application/pdf',
      });
    });
  });
});
