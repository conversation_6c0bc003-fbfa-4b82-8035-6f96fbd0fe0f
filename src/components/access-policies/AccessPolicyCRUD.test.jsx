/* eslint-disable react/prop-types */
import { fireEvent, render, screen } from '@testing-library/react';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import AccessPolicyCRUD from './AccessPolicyCRUD';

const { apiCall } = require('@zscaler/zui-component-library').useApiCall();

jest.mock('@zscaler/zui-component-library', () => ({
  CRUDModal: ({ children, ...props }) => <div {...props}>{children}</div>,
  useApiCall: () => ({ apiCall: jest.fn() }),
  getApiDELETENotificationOptions: jest.fn(),
  getApiPOSTNotificationOptions: jest.fn(),
  getApiPUTNotificationOptions: jest.fn(),
}));

jest.mock('lodash-es', () => ({
  noop: jest.fn(),
}));

const mockContextValue = {
  modalMode: '',
  setModalMode: jest.fn(),
  detail: {},
  setDetail: jest.fn(),
  isFormReadOnly: false,
};

describe('AccessPolicyCRUD', () => {
  it('renders AccessPolicyForm when modalMode is add', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'add' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    expect(screen.getByText('AccessPolicyForm')).toBeInTheDocument();
  });

  it('renders AccessPolicyForm when modalMode is edit', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'edit' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    expect(screen.getByText('AccessPolicyForm')).toBeInTheDocument();
  });

  it('renders CRUDModal when modalMode is delete', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'delete' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    expect(screen.getByText('CRUDModal')).toBeInTheDocument();
  });

  it('calls setModalMode and setDetail on close click', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'delete' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('CANCEL'));

    expect(mockContextValue.setModalMode).toHaveBeenCalledWith('');
    expect(mockContextValue.setDetail).toHaveBeenCalledWith({});
  });

  it('calls apiCall on save click when modalMode is add', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'add' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('SAVE'));

    expect(apiCall).toHaveBeenCalled();
  });

  it('calls apiCall on save click when modalMode is edit', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'edit' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('SAVE'));

    expect(apiCall).toHaveBeenCalled();
  });

  it('calls apiCall on save click when modalMode is delete', () => {
    render(
      <CRUDPageContext.Provider value={{ ...mockContextValue, modalMode: 'delete' }}>
        <AccessPolicyCRUD />
      </CRUDPageContext.Provider>,
    );

    fireEvent.click(screen.getByText('SAVE'));

    expect(apiCall).toHaveBeenCalled();
  });
});
