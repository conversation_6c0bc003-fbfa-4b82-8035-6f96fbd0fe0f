/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useSelector } from 'react-redux';

import { fireEvent, render, screen } from '@testing-library/react';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import CriteriaSection from './CriteriaSection';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
}));

jest.mock('@zscaler/zui-component-library', () => ({
  Button: ({ children, ...props }) => <button {...props}>{children}</button>,
  DropDown: ({ list, selectedList, onSelection, containerStyle }) => (
    <select
      style={containerStyle}
      value={selectedList[0]?.value || ''}
      onChange={(e) => onSelection([{ value: e.target.value }])}
    >
      {list.map((item) => (
        <option key={item.value} value={item.value}>
          {item.label}
        </option>
      ))}
    </select>
  ),
  InlineDatePicker: ({ selectedDate, onChange, containerStyle }) => (
    <input
      type="date"
      value={selectedDate ? selectedDate.toISOString().split('T')[0] : ''}
      onChange={onChange}
      style={containerStyle}
    />
  ),
  MultiSelection: ({ unselectedTitle, selectedTitle, ...props }) => <div {...props} />,
  useDropDownActions: () => ({
    isDropDownLoading: false,
    onDropDownOpen: jest.fn(),
    onLoadMoreClick: jest.fn(),
  }),
}));

const mockDetail = {
  accessPolicyCriteria: {
    operation: '',
    resourceConditions: [],
    timeCondition: {},
  },
};

const mockSetDetail = jest.fn();

const renderComponent = (props) =>
  render(
    <CRUDPageContext.Provider value={{ detail: mockDetail, setDetail: mockSetDetail }}>
      <CriteriaSection {...props} />
    </CRUDPageContext.Provider>,
  );

describe('CriteriaSection', () => {
  beforeEach(() => {
    useSelector.mockImplementation((selector) =>
      selector({
        accessPolicies: {
          resourceServerScopesTableDetail: {},
          resourceServerScopesList: [],
          operationsEnums: [{ label: 'Operation 1', value: 'operation1' }],
          frequenciesEnums: [{ label: 'Daily', value: 'DAILY' }],
          timeZonesEnums: [{ label: 'UTC', value: 'UTC' }],
          daysOfWeekEnums: [{ label: 'Monday', value: 'MONDAY' }],
          occurrencesEnums: [{ label: 'First', value: 'FIRST' }],
        },
      }),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    expect(screen.getByText('Expression')).toBeInTheDocument();
  });

  it('handles operation selection', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    fireEvent.change(screen.getByRole('combobox'), { target: { value: 'operation1' } });
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles frequency selection', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    fireEvent.change(screen.getAllByRole('combobox')[1], { target: { value: 'DAILY' } });
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles from date change', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    fireEvent.change(screen.getAllByRole('textbox')[0], { target: { value: '2023-01-01' } });
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles to date change', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    fireEvent.change(screen.getAllByRole('textbox')[1], { target: { value: '2023-01-02' } });
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles time zone selection', () => {
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection: jest.fn() });
    fireEvent.change(screen.getAllByRole('combobox')[2], { target: { value: 'UTC' } });
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles delete time condition', () => {
    const setShowTimeSection = jest.fn();
    renderComponent({ setShowTimeSection, setShowScopeSection: jest.fn() });
    fireEvent.click(screen.getAllByRole('button')[1]);
    expect(setShowTimeSection).toHaveBeenCalledWith(false);
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });

  it('handles delete scope condition', () => {
    const setShowScopeSection = jest.fn();
    renderComponent({ setShowTimeSection: jest.fn(), setShowScopeSection });
    fireEvent.click(screen.getAllByRole('button')[2]);
    expect(setShowScopeSection).toHaveBeenCalledWith(false);
    expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
  });
});
