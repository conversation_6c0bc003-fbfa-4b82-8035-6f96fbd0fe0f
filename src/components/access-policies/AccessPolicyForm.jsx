import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Field, Label, RadioButtons } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { selectRuleActionsEnums } from '../../ducks/access-policies/selectors';
import { selectIsNavMinimized } from '../../ducks/global/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import StepsWizardModal from '../steps-wizard/StepsWizardModal';
import AssignApiClients from './AssignApiClients';
import CriteriaSection from './CriteriaSection';
import SummarySection from './SummarySection';
import { getFormValidationDetail } from './helper';

const STEPS = {
  DEFINE_CRITERIA: 'DEFINE_CRITERIA',
  ASSIGN_API_CLIENTS: 'ASSIGN_API_CLIENTS',
  SUMMARY: 'SUMMARY',
};

const STEP_LIST = Object.keys(STEPS).map((step) => ({ label: STEPS[step], value: step }));

const AccessPolicyForm = ({ mode = '', onCloseClick = noop, onSaveClick = noop }) => {
  const isNavMinimized = useSelector(selectIsNavMinimized);

  const { detail, setDetail } = useContext(CRUDPageContext);

  const [validationDetail, setValidationDetail] = useState({});

  const [activeStep, setActiveStep] = useState(STEP_LIST[0]);
  const [showTimeSection, setShowTimeSection] = useState(true);
  const [showScopeSection, setShowScopeSection] = useState(true);

  const { ruleAction } = detail;

  const ruleActionsList = useSelector(selectRuleActionsEnums);

  const [selectedRuleAction, setSelectedRuleAction] = useState();

  useEffect(() => {
    setValidationDetail(
      getFormValidationDetail({
        formValues: detail,
        showTimeSection,
        showScopeSection,
        activeStep,
      }),
    );
  }, [detail]);

  useEffect(() => {
    if (ruleAction) {
      setSelectedRuleAction(ruleAction);
    }
  }, [ruleAction]);

  const renderStepHeader = () => {
    if (activeStep.value == STEPS.DEFINE_CRITERIA) {
      return (
        <>
          <p className="step-header">Define Criteria</p>
          <p className="step-sub-header">
            Define criteria for the policy rule using the query builder below.
          </p>
        </>
      );
    }

    if (activeStep.value == STEPS.ASSIGN_API_CLIENTS) {
      return (
        <>
          <p className="step-header">Assign API Clients</p>
          <p className="step-sub-header">
            Select the API Clients from below list this policy rule applies to.
          </p>
        </>
      );
    }

    if (activeStep.value == STEPS.SUMMARY) {
      return (
        <>
          <p className="step-header">Summary</p>
          <p className="step-sub-header">
            Ensure the information below is correct before creating this policy rule.
          </p>
        </>
      );
    }

    return null;
  };

  const isRuleSelected = (detail = {}) => {
    const { value } = detail;

    return value === selectedRuleAction;
  };

  const onRuleSelection = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setDetail((prevState) => ({
        ...prevState,
        ruleAction: value,
      }));
    }
  };

  const renderActionSection = () => {
    return (
      <Field
        label="RULE_ACTION_THEN"
        htmlFor="ruleAction"
        info={validationDetail}
        containerClass="radio-container"
      >
        <RadioButtons
          list={ruleActionsList}
          isSelected={isRuleSelected}
          onClick={onRuleSelection}
        />
      </Field>
    );
  };

  const renderDefineCriteriaSection = () => {
    if (activeStep.value !== STEPS.DEFINE_CRITERIA) {
      return null;
    }

    const isConditionsValid = validationDetail.context == 'criteriaSection';

    return (
      <div>
        <Label
          text="CRITERIA_IF"
          info={isConditionsValid ? validationDetail : {}}
          containerStyle={{ marginBottom: '0' }}
        />

        <CriteriaSection
          showTimeSection={showTimeSection}
          setShowTimeSection={setShowTimeSection}
          showScopeSection={showScopeSection}
          setShowScopeSection={setShowScopeSection}
          validationDetail={validationDetail}
        />

        {renderActionSection()}
      </div>
    );
  };

  const renderApiClientsSection = () => {
    if (activeStep.value !== STEPS.ASSIGN_API_CLIENTS) {
      return null;
    }

    return <AssignApiClients />;
  };

  const renderSummarySection = () => {
    if (activeStep.value !== STEPS.SUMMARY) {
      return null;
    }

    return <SummarySection />;
  };

  const onNextClick = () => {
    const activeStepIndex = STEP_LIST.findIndex((step) => step.value === activeStep.value);

    if (activeStepIndex === STEP_LIST.length - 1) {
      onSaveClick();
    } else {
      setActiveStep(STEP_LIST[activeStepIndex + 1]);
    }
  };

  const onBackClick = () => {
    const activeStepIndex = STEP_LIST.findIndex((step) => step.value === activeStep.value);

    if (activeStepIndex === 0) {
      return;
    }

    setActiveStep(STEP_LIST[activeStepIndex - 1]);
  };

  if (mode === '') {
    return null;
  }

  const isNextButtonDisabled = () => {
    return !validationDetail.isValid;
  };

  const getNextText = () => {
    if (activeStep.value === STEPS.SUMMARY) {
      return mode == 'edit' ? 'UPDATE' : 'SAVE';
    }

    return 'NEXT';
  };

  return (
    <StepsWizardModal
      show={!!mode}
      list={STEP_LIST}
      selectedItem={activeStep}
      onSelection={setActiveStep}
      containerClass={`access-policy-form-modal ${isNavMinimized ? 'nav-minimized' : ''}`}
      headerText={mode == 'edit' ? 'EDIT_POLICY_RULE' : 'ADD_POLICY_RULE'}
      onClose={onCloseClick}
      nextText={getNextText()}
      onNextClick={onNextClick}
      isNextButtonDisabled={isNextButtonDisabled()}
      onBackClick={onBackClick}
      isBackButtonDisabled={activeStep.value === STEPS.DEFINE_CRITERIA}
    >
      <div className="step-header-section">{renderStepHeader()}</div>
      {renderDefineCriteriaSection()}
      {renderApiClientsSection()}
      {renderSummarySection()}
    </StepsWizardModal>
  );
};
AccessPolicyForm.propTypes = {
  mode: PropTypes.string,
  onCloseClick: PropTypes.func,
  onSaveClick: PropTypes.func,
};

export default AccessPolicyForm;
