// /* eslint-disable no-undef */
// /* eslint-disable no-unused-vars */
// /* eslint-disable react/prop-types */
// import { useSelector } from 'react-redux';

// import { fireEvent, render, screen } from '@testing-library/react';

// import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
// import AssignApiClients from './AssignApiClients';

// jest.mock('react-redux', () => ({
//   useSelector: jest.fn(),
// }));

// jest.mock('@zscaler/zui-component-library', () => ({
//   DropDown: ({
//     list,
//     selectedList,
//     onSelection,
//     renderItemsSelection,
//     onOpen,
//     loading,
//     loadMoreDetail,
//   }) => (
//     <div>
//       <div data-testid="dropdown" onClick={onOpen}>
//         {list.map((item) => (
//           <div key={item.value}>{item.label}</div>
//         ))}
//       </div>
//       <div data-testid="selected-list">
//         {selectedList.map((item) => (
//           <div key={item.value}>{item.label}</div>
//         ))}
//       </div>
//       <button data-testid="load-more" onClick={loadMoreDetail.onLoadMoreClick}>
//         Load More
//       </button>
//     </div>
//   ),
//   Field: ({ label, children }) => (
//     <div>
//       <label>{label}</label>
//       {children}
//     </div>
//   ),
//   MultiSelection: ({ unselectedTitle, selectedTitle }) => (
//     <div>
//       <div>{unselectedTitle}</div>
//       <div>{selectedTitle}</div>
//     </div>
//   ),
//   useDropDownActions: () => ({
//     isDropDownLoading: false,
//     onDropDownOpen: jest.fn(),
//     onLoadMoreClick: jest.fn(),
//   }),
// }));

// describe('AssignApiClients', () => {
//   const mockApiClientsList = [
//     { label: 'Client 1', value: '1' },
//     { label: 'Client 2', value: '2' },
//   ];

//   const mockApiClientsTableDetail = {
//     hasMore: true,
//   };

//   const mockSetDetail = jest.fn();

//   beforeEach(() => {
//     useSelector.mockImplementation((selector) => {
//       if (selector === selectApiClientsList) {
//         return mockApiClientsList;
//       }
//       if (selector === selectApiClientsTableDetail) {
//         return mockApiClientsTableDetail;
//       }
//       return [];
//     });
//   });

//   afterEach(() => {
//     jest.clearAllMocks();
//   });

//   it('renders correctly', () => {
//     render(
//       <CRUDPageContext.Provider value={{ detail: {}, setDetail: mockSetDetail }}>
//         <AssignApiClients />
//       </CRUDPageContext.Provider>,
//     );

//     expect(screen.getByLabelText('API_CLIENTS')).toBeInTheDocument();
//     expect(screen.getByTestId('dropdown')).toBeInTheDocument();
//   });

//   it('displays the list of API clients', () => {
//     render(
//       <CRUDPageContext.Provider value={{ detail: {}, setDetail: mockSetDetail }}>
//         <AssignApiClients />
//       </CRUDPageContext.Provider>,
//     );

//     mockApiClientsList.forEach((client) => {
//       expect(screen.getByText(client.label)).toBeInTheDocument();
//     });
//   });

//   it('handles selection of API clients', () => {
//     render(
//       <CRUDPageContext.Provider value={{ detail: {}, setDetail: mockSetDetail }}>
//         <AssignApiClients />
//       </CRUDPageContext.Provider>,
//     );

//     fireEvent.click(screen.getByText('Client 1'));

//     expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
//   });

//   it('handles load more click', () => {
//     render(
//       <CRUDPageContext.Provider value={{ detail: {}, setDetail: mockSetDetail }}>
//         <AssignApiClients />
//       </CRUDPageContext.Provider>,
//     );

//     fireEvent.click(screen.getByTestId('load-more'));

//     expect(mockSetDetail).toHaveBeenCalledWith(expect.any(Function));
//   });
// });
