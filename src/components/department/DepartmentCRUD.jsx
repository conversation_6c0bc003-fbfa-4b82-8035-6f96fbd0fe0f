import { useContext, useMemo } from 'react';

import {
  CRUDModal,
  FileBrowserForm,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import {
  add,
  bulkRemove,
  downloadTemplateCSV,
  importFromCsvPolling,
  remove,
  update,
} from '../../ducks/departments';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import DepartmentForm from './DepartmentForm';
import { getFormTooltipDetail, modalModeDetail } from './helper';

const DepartmentCRUD = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setCsvImportDetail(defaultCsvImportDetail);
    setCsvImportResult(defaultCsvImportResultDetail);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(detail), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(update(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'bulkDelete') {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsvPolling(csvImportDetail))
        .then((response = {}) => {
          setCsvImportResult({ ...response });
        })
        .catch(noop);
    }
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids }), {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          message: 'ITEMS_HAVE_BEEN_DELETED',
        },
      })
        .catch(noop)
        .finally(onCloseClick);
    }
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isImportResultValid = !!csvImportResult;

  const showSave = useMemo(() => {
    if (modalMode === 'view') {
      return false;
    }

    return !isImportResultValid && hasFullAccess;
  }, [modalMode, isImportResultValid, hasFullAccess]);

  const cancelText = useMemo(() => {
    return isImportResultValid || modalMode === 'view' ? 'CLOSE' : 'CANCEL';
  }, [modalMode, isImportResultValid]);

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) =>
        modalMode === 'importFile' ? (
          <FileBrowserForm
            onDetailChange={setCsvImportDetail}
            detail={csvImportDetail}
            onDownloadClick={onDownloadClick}
            overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
            tooltip={getFormTooltipDetail('csvFile')}
            result={csvImportResult}
            {...props}
          />
        ) : (
          <DepartmentForm {...props} />
        )
      }
      saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
      showSave={showSave}
      cancelText={cancelText}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default DepartmentCRUD;
