import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'DEPARTMENT',
  },
  add: {
    headerText: 'ADD_DEPARTMENT',
  },
  edit: {
    headerText: 'EDIT_DEPARTMENT',
  },
  delete: {
    headerText: 'DELETE_DEPARTMENT',
    confirmationMessage:
      'Are you sure you want to delete this Department? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage:
      'Are you sure you want to bulk delete these Departments? The changes cannot be undone.',
  },
  importFile: {
    headerText: 'IMPORT_DEPARTMENT',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const defaultSearchOption = {
  label: 'SELECT',
  value: 'SELECT',
};

export const getSearchField = (selectedSearchField = {}) => {
  const { value } = selectedSearchField || {};

  let searchField = '';

  if (value === 'GROUP_NAME') {
    searchField = 'name';
  }

  if (value === 'USER_NAME') {
    searchField = 'userName';
  }

  return searchField;
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (name && name.length > 128) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'max 128 characters allowed';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a department name`;
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. Comments cannot exceed 256 characters.`;
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Select if you want to update your existing department information, delete existing
        department, or add new department. If you only want to add new departments, Zscaler
        doesn&apos;t recommend selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-department-information-csv-file">
          Importing Department Information from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
