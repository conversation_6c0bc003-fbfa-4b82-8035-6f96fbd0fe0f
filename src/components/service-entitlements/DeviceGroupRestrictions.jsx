import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  Card,
  Checkbox,
  Field,
  FieldGroup,
  TableContainer,
  ToggleButton,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getDeviceGroupAssignments, updateSelection } from '../../ducks/service-entitlements';
import {
  selectDeviceGroupsAssignmentsDetail,
  selectDeviceGroupsTableConfig,
  selectDeviceGroupsTableDetail,
} from '../../ducks/service-entitlements/selectors';

const DeviceGroupRestrictions = ({ detail }) => {
  // eslint-disable-next-line react/prop-types
  const { id } = detail;

  const { apiCall } = useApiCall();
  const dispatch = useDispatch();
  const tableConfig = useSelector(selectDeviceGroupsTableConfig);
  const tableDetail = useSelector(selectDeviceGroupsTableDetail);

  const [enableDeviceGroupRestrictions, setEnableDeviceGroupRestrictions] = useState(false);
  const selectedDeviceGroupsDetail = useSelector(selectDeviceGroupsAssignmentsDetail);

  useEffect(() => {
    apiCall(
      getDeviceGroupAssignments({
        id,
      }),
    ).catch(noop);
  }, []);

  useEffect(() => {
    if (selectedDeviceGroupsDetail?.length > 0 && !enableDeviceGroupRestrictions) {
      setEnableDeviceGroupRestrictions(true);
    }
  }, [selectedDeviceGroupsDetail]);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'groupSelector') {
        const GroupSelector = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { id, name, isSelected },
            },
          } = props;

          const onSelectionChange = (evt) => {
            const data = { value: evt?.target?.value, id, name };
            dispatch(updateSelection(data));
          };

          return (
            <>
              <div className="is-flex has-ai-c pointer">
                <Checkbox name={name} checked={isSelected} onChange={onSelectionChange} />
              </div>
            </>
          );
        };
        columnDetail.cell = GroupSelector;
        return columnDetail;
      }
    });
    // const newColumns = [
    //   {
    //     id: 'selection',
    //     Header: '',
    //     cell: (props) => <Selector {...props} />,
    //     size: 10,
    //     minSize: 10,
    //     maxSize: 10,
    //     enableResizing: false,
    //     disableSortBy: true,
    //     defaultCanSort: false,
    //   },
    //   ...(tableConfig?.columns || []),
    // ];

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const onToggle = () => {
    if (enableDeviceGroupRestrictions) {
      dispatch(updateSelection(null));
    }
    setEnableDeviceGroupRestrictions(!enableDeviceGroupRestrictions);
  };
  // const onLoadMoreClick = () => {
  //   const { pageSize, pageOffset } = tableDetail;

  //   apiCall(
  //     getDeviceGroups({
  //       requireTotal: false,
  //       pageOffset: pageOffset + pageSize,
  //       pageSize,
  //       id,
  //     }),
  //   ).catch(noop);
  // };

  // const onRowSelection = (data) => {
  //   onGroupsSelection(data);
  // };

  return (
    <article>
      <Card>
        <div className={'is-flex full-width device-groups-restrictions-container'}>
          <FieldGroup containerClass="has-width-auto">
            <Field label="ENABLE_DEVICE_GROUP_RESTRICTIONS">
              <ToggleButton
                isOn={enableDeviceGroupRestrictions}
                onToggleClick={onToggle}
                showLabel={false}
              />
            </Field>
          </FieldGroup>

          {enableDeviceGroupRestrictions && (
            <TableContainer
              {...tableConfig}
              columns={tableColumnConfig}
              containerClass={'device-group-retrictions-table'}
              data={tableDetail.data}
              hidePagination={true}
              // onRowSelection={onRowSelection}
              // pagination={{ ...tableDetail, onLoadMoreClick }}
            />
          )}
        </div>
      </Card>
    </article>
  );
};
DeviceGroupRestrictions.propTypes = {
  detail: PropTypes.object,
  onGroupsSelection: PropTypes.func,
};

export default DeviceGroupRestrictions;
