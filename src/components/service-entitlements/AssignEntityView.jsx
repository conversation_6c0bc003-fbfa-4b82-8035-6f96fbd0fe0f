import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faArrowLeft, faCircle, faCircleDot, faUser } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Search,
  Selector,
  TableContainer,
  TextWithTooltip,
  getApiPOSTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop, remove } from 'lodash-es';
import PropTypes from 'prop-types';

import { showErrorNotification } from '../../ducks/global';
import {
  assignEntities,
  getAllEntitiesForAssignment,
  setSelectedEntities,
} from '../../ducks/service-entitlements';
import {
  selectAssignEntitiesTableConfig,
  selectAssignEntitiesTableDetail,
  selectedEntitiesTableDetail,
} from '../../ducks/service-entitlements/selectors';

import GroupUsersTableModal from './GroupUsersTableModal';
import { getAssignEntitiesApiPayload } from './helper';

const AssignEntityView = ({ onBackClick, serviceDetail, entityType }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { apiCall } = useApiCall();
  const labelText = entityType === 'GROUP' ? 'GROUPS' : 'USERS';

  const tableConfig = useSelector(selectAssignEntitiesTableConfig);
  const tableDetail = useSelector(selectAssignEntitiesTableDetail);
  const assignedEntitiesDetail = useSelector(selectedEntitiesTableDetail);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [canvasState, setCanvasState] = useState(1);
  const [groupUsersTableModalData, setGroupUsersTableModalData] = useState(false);
  const [showGroupUsersTableModal, setShowGroupUsersTableModal] = useState(false);

  const [showUpdateStatusWarning, setShowUpdateStatusWarning] = useState(false);
  const [warningSummary, setWarningSummary] = useState('');

  useEffect(() => {
    apiCall(getAllEntitiesForAssignment({ type: entityType })).catch(noop);
    dispatch(setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }));
  }, []);

  const cancelAssignment = () => {
    setSelectedRowDetail([]);
    dispatch(setSelectedEntities([]));
    onBackClick();
  };

  const backToAssignTable = () => {
    setCanvasState(1);
    setSelectedRowDetail([]);
    setSearchTerm('');
    dispatch(setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }));
    apiCall(getAllEntitiesForAssignment({ type: entityType })).catch(noop);
  };

  const renderActionsSection = () => {
    return (
      <div className="action-section buttons">
        {canvasState === 2 && (
          <Button
            containerClass="back-button"
            onClick={() => {
              backToAssignTable();
            }}
          >
            {t('BACK')}
          </Button>
        )}
        <Button
          containerClass="confirm-button"
          disabled={selectedRowDetail?.length === 0}
          onClick={() => {
            saveEntities();
          }}
        >
          {t(canvasState === 1 ? 'NEXT' : 'ASSIGN')}
        </Button>
        <Button
          type="tertiary"
          containerClass="no-p-l cancel-button"
          onClick={() => {
            cancelAssignment();
          }}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  const tableReviewColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { displayName, name, idp } = props?.row?.original || {};

          let derivedName = displayName || name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${derivedName} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }
      return columnDetail;
    });

    const newColumns = [...(tableConfig?.columns || [])];
    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'loginName' });
    }

    if (entityType === 'USER') {
      remove(newColumns, { id: 'groupUsers' });
    }

    return newColumns;
  }, [tableConfig?.columns]);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'groupUsers') {
        const UsersCellComponent = (props) => {
          return (
            <Button
              type="tertiary"
              onClick={() => {
                // eslint-disable-next-line react/prop-types
                setGroupUsersTableModalData(props?.row?.original);
                setShowGroupUsersTableModal(true);
              }}
            >
              <FontAwesomeIcon icon={faUser} className="icon left" />
              {t('USERS')}
            </Button>
          );
        };

        columnDetail.cell = UsersCellComponent;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { displayName, name, idp } = props?.row?.original || {};

          let derivedName = displayName || name;

          const idpName = idp?.name || '';

          if (idpName) {
            derivedName = `${derivedName} (${idpName})`;
          }

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    const newColumns = [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 50,
        minSize: 50,
        maxSize: 50,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
    if (entityType === 'GROUP') {
      remove(newColumns, { id: 'loginName' });
    }

    if (entityType === 'USER') {
      remove(newColumns, { id: 'groupUsers' });
    }
    return newColumns;
  }, [tableConfig?.columns]);

  const onLoadMoreClick = () => {
    if (canvasState === 1) {
      const { pageSize, pageOffset } = tableDetail;

      apiCall(
        getAllEntitiesForAssignment({
          requireTotal: false,
          pageOffset: pageOffset + pageSize,
          pageSize,
          type: entityType,
        }),
      ).catch(noop);
    } else {
      const { pageSize, pageOffset } = assignedEntitiesDetail;

      dispatch(
        setSelectedEntities({
          selectedEntities: selectedRowDetail,
          pageOffset: pageOffset + pageSize,
        }),
      );
    }
  };

  const onCloseGroupUsersModal = () => {
    setShowGroupUsersTableModal(false);
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);
  };

  const renderTableComponent = () => {
    if (canvasState === 1) {
      return (
        <TableContainer
          {...tableConfig}
          columns={tableColumnConfig}
          containerClass={'assign-service-entitlements'}
          data={tableDetail.data}
          onRowSelection={onRowSelection}
          pagination={{ ...tableDetail, onLoadMoreClick }}
        />
      );
    }
    return (
      <TableContainer
        {...tableConfig}
        columns={tableReviewColumnConfig}
        containerClass={'assign-service-entitlements review-mode'}
        data={assignedEntitiesDetail.data}
        pagination={{ ...assignedEntitiesDetail, onLoadMoreClick }}
      />
    );
  };

  const saveEntities = () => {
    if (canvasState === 1) {
      setCanvasState(2);
      dispatch(
        setSelectedEntities({
          selectedEntities: selectedRowDetail,
          pageOffset: 0,
        }),
      );
      return false;
    }

    const apiPayload = getAssignEntitiesApiPayload({
      selectedEntities: selectedRowDetail,
      entityType,
    });

    apiCall(assignEntities({ id: serviceDetail.id, payload: apiPayload }), {
      successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      hasNotification: false,
    })
      .then(() => {
        onBackClick();
        dispatch(
          setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }),
        );
      })
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onSaveClick = () => {
    const apiPayload = getAssignEntitiesApiPayload({
      selectedEntities: selectedRowDetail,
      entityType,
    });

    apiCall(assignEntities({ id: serviceDetail.id, payload: apiPayload }), {
      successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      hasNotification: false,
    })
      .then(() => {
        onBackClick();
        dispatch(
          setSelectedEntities({ selectedEntities: [], pageOffset: 0, resetEntitiesList: true }),
        );
      })
      .catch((response) => {
        if (response.apiErrorLevel === 'WARN') {
          setShowUpdateStatusWarning(true);
          setWarningSummary(response.errorSummary);
        } else {
          dispatch(showErrorNotification({ message: response?.errorSummary }));
        }
      });
  };

  const onCloseClick = () => {
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const displayCanvasState = () => {
    let currentState = {
      label: canvasState === 1 ? t(`SELECT_${labelText}`) : t('SUMMARY'),
    };

    return (
      <div>
        <section className="typography-header3">{currentState.label}</section>
        <section className="typography-paragraph2 subtitle with-border-bottom">
          <div className="services-label">
            {t(serviceDetail.serviceDescription)} - {t('SERVICES')}
          </div>
          {serviceDetail.cloudName && (
            <div className="cloud-label">
              <span className="">{t('CLOUD_NAME')}</span>
              <span className="value">{t(serviceDetail.cloudName)}</span>
            </div>
          )}
          {serviceDetail.orgName && (
            <div className="org-label">
              <span className="">{t('ORG_NAME')}</span>
              <span className="value">{t(serviceDetail.orgName)}</span>
            </div>
          )}
        </section>
        {canvasState === 1 && (
          <div className="is-flex has-jc-e">
            <div className="buttons">
              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerStyle={{ width: '260px' }}
              />
            </div>
          </div>
        )}
        {renderTableComponent()}
      </div>
    );
  };

  const onSearchEnter = (term) => {
    apiCall(getAllEntitiesForAssignment({ type: entityType, name: term })).catch(noop);

    setSearchTerm(term);
  };

  const renderBodySection = () => {
    return <GroupUsersTableModal detail={groupUsersTableModalData} />;
  };

  return (
    <div className="assign-entities">
      <section className="typography-header3 page-title services-heading">
        <FontAwesomeIcon icon={faArrowLeft} className="icon left pointer" onClick={onBackClick} />
        {t(`ASSIGN_${labelText}`)}
      </section>
      <Card containerClass="assign-view">
        <div className="left-panel">
          <div className={`select-view ${canvasState === 1 ? 'active' : ''}`}>
            <div className="icon-container">
              <FontAwesomeIcon icon={faCircleDot} className="icon current-state" />
              <div className="icon-bar"></div>
            </div>
            {t(`SELECT_${labelText}`)}
          </div>
          <div className={`summary-view ${canvasState === 2 ? 'active' : ''}`}>
            <div className="icon-container">
              <div className="icon-bar"></div>
              <FontAwesomeIcon icon={faCircle} className="icon end-state" />
            </div>
            {t('SUMMARY')}
          </div>
        </div>
        <div className="right-panel">{displayCanvasState()}</div>
        {showGroupUsersTableModal && (
          <Modal
            show={showGroupUsersTableModal}
            onEscape={onCloseGroupUsersModal}
            containerClass="group-users-modal crud-modal"
          >
            <ModalHeader text="USERS" onClose={onCloseGroupUsersModal} />
            <ModalBody> {renderBodySection()}</ModalBody>
            <ModalFooter cancelText="OK" showSave={false} onCancel={onCloseGroupUsersModal} />
          </Modal>
        )}
      </Card>
      {renderActionsSection()}

      {showUpdateStatusWarning && (
        <Modal
          show={showUpdateStatusWarning}
          onEscape={onCloseClick}
          containerClass="update-status-warning-container"
        >
          <ModalHeader text="AUDITOR_OVERRIDE" onClose={onCloseClick} />
          <ModalBody>{warningSummary}</ModalBody>
          <ModalFooter saveText="REMOVE" onSave={onSaveClick} onCancel={onCloseClick} />
        </Modal>
      )}
    </div>
  );
};

AssignEntityView.propTypes = {
  entityType: PropTypes.string,
  onBackClick: PropTypes.func,
  serviceDetail: PropTypes.object,
};

export default AssignEntityView;
