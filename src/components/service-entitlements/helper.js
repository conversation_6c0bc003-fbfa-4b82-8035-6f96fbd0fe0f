export const modalModeDetail = (entityType, modalMode) => {
  if (modalMode === 'delete') {
    return {
      headerText: 'DELETE_ASSIGNMENT',
      confirmationMessage:
        'Are you sure you want to delete this assignment? The changes cannot be undone.',
    };
  } else if (modalMode === 'bulkDelete') {
    return {
      headerText: 'BULK_DELETE',
      confirmationMessage:
        'Are you sure you want to bulk delete these assignments? The changes cannot be undone.',
    };
  }
  return {};
};

export const getAssignEntitiesApiPayload = ({ selectedEntities, entityType }) => {
  const payload = [];

  selectedEntities.forEach((entity) => {
    let assignment = {};
    assignment.resource = {
      id: entity.id,
    };
    assignment.type = entityType;
    payload.push(assignment);
  });
  return payload;
};

export const manageOptions = [
  { label: 'DEVICE_GROUP_RESTRICTIONS', value: 'DEVICE_GROUP_RESTRICTIONS' },
];

export const isManageRestrictionsAllowed = (type) => {
  return type === 'ZIA' || type === 'ZPA' || type === 'ZDX';
};
