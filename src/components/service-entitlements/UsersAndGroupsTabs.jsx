import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  DropDown,
  HelpContainer,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Tab,
  Tabs,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  getDeviceGroups,
  setDeviceGroups,
  updateSelection,
} from '../../ducks/service-entitlements';
import { selectDeviceGroupsAssignmentsDetail } from '../../ducks/service-entitlements/selectors';

import { HELP_ARTICLES } from '../../config';
import DeviceGroupRestrictions from './DeviceGroupRestrictions';
import TabView from './TabView';
import { isManageRestrictionsAllowed, manageOptions } from './helper';

const TABS = {
  PRIMARY: 'PRIMARY',
  SECONDARY: 'SECONDARY',
};

const defaultProps = {
  serviceDetail: {},
  privileges: {
    hasFullAccess: false,
  },
};

const UsersAndGroupsTabs = ({ onBackClick, serviceDetail, privileges, services }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();

  const { hasFullAccess } = privileges;
  const isRestrictionsAllowed = isManageRestrictionsAllowed(serviceDetail?.serviceName, services);

  const [selectedTab, setSelectedTab] = useState(TABS.SECONDARY);
  const [hidePageHeader, setHidePageHeader] = useState(false);
  const [userCount, setUserCount] = useState(0);
  const [groupCount, setGroupCount] = useState(0);
  const [showDeviceGroups, setShowDeviceGroups] = useState(false);
  const [selectedDeviceGroups, setSelectedDevcieGroups] = useState([]);

  const selectedDeviceGroupsDetail = useSelector(selectDeviceGroupsAssignmentsDetail);

  useEffect(() => {
    setSelectedDevcieGroups(selectedDeviceGroupsDetail);
  }, [selectedDeviceGroupsDetail]);

  const togglePageHeader = (value) => {
    setHidePageHeader(value);
  };

  const onUserDetailsFetch = (data) => {
    setUserCount(data);
  };

  const onGroupDetailsFetch = (data) => {
    setGroupCount(data);
  };

  const renderTabsSelectionSection = () => {
    return (
      <Tabs>
        <Tab
          label={groupCount > 0 ? `${t('USER_GROUPS')} (${groupCount})` : t('USER_GROUPS')}
          isActive={selectedTab === TABS.SECONDARY}
          onClick={() => {
            setSelectedTab(TABS.SECONDARY);
          }}
        />
        <Tab
          label={userCount > 0 ? `${t('USERS')} (${userCount})` : t('USERS')}
          isActive={selectedTab === TABS.PRIMARY}
          onClick={() => {
            setSelectedTab(TABS.PRIMARY);
          }}
        />
      </Tabs>
    );
  };

  const renderPrimarySection = () => {
    return (
      <TabView
        service={serviceDetail}
        onAssignEntityClick={togglePageHeader}
        onDetailsFetch={onUserDetailsFetch}
        privileges={privileges}
        entityType={'USER'}
      />
    );
  };

  const renderSecondarySection = () => {
    return (
      <TabView
        service={serviceDetail}
        onAssignEntityClick={togglePageHeader}
        onDetailsFetch={onGroupDetailsFetch}
        privileges={privileges}
        entityType={'GROUP'}
      />
    );
  };

  const renderSelectedTabSection = () => {
    if (selectedTab === TABS.SECONDARY) {
      return renderSecondarySection();
    }

    return renderPrimarySection();
  };

  const onCloseDeviceGroupsModal = () => {
    setShowDeviceGroups(false);
    dispatch(updateSelection(null));
  };

  const onSaveRestrictions = () => {
    apiCall(
      setDeviceGroups({
        id: serviceDetail?.id,
        groups: selectedDeviceGroups,
      }),
    )
      .then(() => {
        setShowDeviceGroups(false);
        dispatch(updateSelection(null));
      })
      .catch(noop);
  };

  // const onGroupsSelection = (data) => {
  //   setSelectedDevcieGroups(data);
  // };

  const renderDeviceGroupsRestrictionsBodySection = () => {
    return <DeviceGroupRestrictions detail={serviceDetail} onGroupsSelection={noop} />;
  };

  return (
    <div id="services-users-and-groups" className="page-container">
      <HelpContainer
        src={
          showDeviceGroups
            ? HELP_ARTICLES.MANAGE_DEVICE_GROUPS
            : HELP_ARTICLES.SERVICE_ENTITLEMENTS_SERVICE
        }
      />
      {!hidePageHeader && (
        <div className="services-header">
          <div>
            <section className="typography-header3">
              <FontAwesomeIcon
                icon={faArrowLeft}
                className="icon left pointer"
                onClick={onBackClick}
              />
              {t(serviceDetail.serviceDescription)} - {t('SERVICE')}
            </section>
            <div className="typography-paragraph2 subtitle">
              {serviceDetail.cloudName && (
                <div className="cloud-label">
                  <span className="">{t('CLOUD_NAME')}</span>
                  <span className="value">{t(serviceDetail.cloudName)}</span>
                </div>
              )}
              {serviceDetail.orgName && (
                <div className="org-label">
                  <span className="">{t('ORG_NAME')}</span>
                  <span className="value">{t(serviceDetail.orgName)}</span>
                </div>
              )}
            </div>
          </div>
          {hasFullAccess && isRestrictionsAllowed && (
            <div>
              <DropDown
                list={manageOptions}
                onSelection={(payload) => {
                  const selectedOption = payload[0];
                  if (selectedOption?.value === 'DEVICE_GROUP_RESTRICTIONS') {
                    apiCall(
                      getDeviceGroups({
                        id: serviceDetail?.id,
                      }),
                    )
                      .then(() => {
                        setShowDeviceGroups(true);
                      })
                      .catch(noop);
                  }
                }}
                itemsSelectionContainerStyle={{ minWidth: '200px' }}
                placeholderList={[{ label: 'MANAGE', value: 'MANAGE' }]}
                selectedItemsProps={{
                  containerClass: 'entitlements-drop-down-container',
                }}
              />
            </div>
          )}
        </div>
      )}

      {!hidePageHeader && renderTabsSelectionSection()}

      {renderSelectedTabSection()}

      {showDeviceGroups && (
        <Modal
          show={showDeviceGroups}
          onEscape={onCloseDeviceGroupsModal}
          containerClass="entitlements-center-modal-container"
        >
          <ModalHeader text="MANAGE_DEVICE_GROUP_RESTRICTIONS" onClose={onCloseDeviceGroupsModal} />
          <ModalBody>{renderDeviceGroupsRestrictionsBodySection()}</ModalBody>
          <ModalFooter
            cancelText="CANCEL"
            showSave={true}
            onCancel={onCloseDeviceGroupsModal}
            onSave={onSaveRestrictions}
          />
        </Modal>
      )}
    </div>
  );
};

UsersAndGroupsTabs.defaultProps = defaultProps;

UsersAndGroupsTabs.propTypes = {
  onBackClick: PropTypes.func,
  serviceDetail: PropTypes.object,
  privileges: PropTypes.object,
  services: PropTypes.array,
};

export default UsersAndGroupsTabs;
