import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'GROUP',
  },
  add: {
    headerText: 'ADD_GROUP',
  },
  edit: {
    headerText: 'EDIT_GROUP',
  },
  delete: {
    headerText: 'DELETE_GROUP',
    confirmationMessage:
      'Are you sure you want to delete this group? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage:
      'Are you sure you want to bulk delete these groups? The changes cannot be undone.',
  },
  importFile: {
    headerText: 'IMPORT_GROUPS',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const searchOptions = [
  { label: 'GROUP_NAME', value: 'GROUP_NAME' },
  { label: 'USER_NAME', value: 'USER_NAME' },
];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const defaultSearchOption = {
  label: 'SELECT',
  value: 'SELECT',
};

export const getSearchField = (selectedSearchField = {}) => {
  const { value } = selectedSearchField || {};

  let searchField = '';

  if (value === 'GROUP_NAME') {
    searchField = 'name';
  }

  if (value === 'USER_NAME') {
    searchField = 'userName';
  }

  return searchField;
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  if (name && name.length > 128) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'max 128 characters allowed';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a group name`;
  }

  if (name === 'assignUsers') {
    tooltipDetail.content = (
      <p>
        Assign users to the group. Click the checkbox against the username to add them to the group.
        Click <strong className="tooltip-bold">X</strong> to remove a user or{' '}
        <strong className="tooltip-bold">Clear All</strong> to remove all selections.
      </p>
    );
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. Comments cannot exceed 512 characters.`;
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Select if you want to update your existing user information, delete existing user group, or
        add new user group. If you only want to add new user groups, Zscaler doesn&apos;t recommend
        selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-group-information-csv-file">
          Importing User Group Information from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
