import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faFileSpreadsheet, faPlus, faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, DropDown, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/groups';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { bulkActionOptions, getSearchField, searchOptions } from './helper';

const GroupActions = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    selectedSearchField,
    setSelectedSearchField,
    searchTerm,
    setSearchTerm,
    defaultBulkActionOption,
    defaultSearchOption,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onCSVImportClick = () => {
    setModalMode('importFile');
  };

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onBulkActionSelectionClick = (payload) => {
    const newSelection = payload[0] || defaultBulkActionOption;

    setSelectedBulkAction(newSelection);

    if (selectedRowDetail?.length > 0) {
      const { value } = newSelection;

      let mode = '';

      if (value === 'DELETE') {
        mode = 'bulkDelete';
      }

      setModalMode(mode);
    }
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);

    setSelectedSearchField(defaultSearchOption);
    setSelectedBulkAction(defaultBulkActionOption);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    const searchField = getSearchField(selectedSearchField);

    if (searchField) {
      apiCall(getList({ [searchField]: term })).catch(noop);

      setSearchTerm(term);
    }
  };

  const isBulkActionEnabled = selectedRowDetail.length > 1;

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      {hasFullAccess && (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_USER_GROUP')}</span>
          </Button>
          <Button type="secondary" onClick={onCSVImportClick}>
            <FontAwesomeIcon icon={faFileSpreadsheet} className="icon left" />
            <span>{t('IMPORT_CSV')}</span>
          </Button>

          {isBulkActionEnabled && (
            <DropDown
              list={bulkActionOptions}
              selectedList={[selectedBulkAction]}
              onSelection={onBulkActionSelectionClick}
              selectedItemsProps={{
                kind: 'secondary',
                containerStyle: {
                  justifyContent: 'center',
                },
              }}
              disabled={!isBulkActionEnabled}
            />
          )}
        </div>
      )}
      <div className="buttons">
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          style={{ minWidth: '0px', paddingRight: '0px' }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>

        <div className="dropdown-with-search">
          <DropDown
            list={searchOptions}
            selectedList={[selectedSearchField]}
            onSelection={(payload) => {
              setSelectedSearchField(payload[0] || defaultSearchOption);
            }}
            selectedItemsProps={{ kind: 'tertiary' }}
          />

          <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
        </div>
      </div>
    </div>
  );
};

export default GroupActions;
