import { startsWith } from 'lodash-es';
import PropTypes from 'prop-types';

import ZscalerLogoFull from '../../images/zs-logo-full.svg';
import Zscaler<PERSON>ogo from '../../images/zs-logo.svg';

const defaultProps = {
  alt: 'company-logo',
  containerClass: '',
  imageClass: '',
  isFull: false,
};

const Logo = ({ alt, imageClass, isFull, src }) => {
  let imageSrc = isFull ? ZscalerLogoFull : ZscalerLogo;
  if (src) {
    imageSrc = startsWith(src, 'data:') ? src : 'data:image/png;base64, ' + src;
  }
  return <img src={imageSrc} className={`${imageClass} mb-4xl`} alt={alt} />;
};

Logo.defaultProps = defaultProps;

Logo.propTypes = {
  src: PropTypes.string,
  alt: PropTypes.string,
  containerClass: PropTypes.string,
  imageClass: PropTypes.string,
  isFull: PropTypes.bool,
};

export default Logo;
