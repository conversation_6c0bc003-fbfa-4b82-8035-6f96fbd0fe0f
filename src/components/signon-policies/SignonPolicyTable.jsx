import { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  InlineText,
  StatusTag,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList, update } from '../../ducks/signon-policies';
import { selectTableConfig, selectTableDetail } from '../../ducks/signon-policies/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const tableShowMoreList = [
  { label: 'ENABLE', value: 'ENABLE' },
  { label: 'DISABLE', value: 'DISABLE' },
];

const SignonPolicyTable = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, isFormReadOnly } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'criteria') {
        const CriteriaCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const conditions = props?.row?.original?.conditions;

          return (
            <InlineText>
              {conditions
                .map(({ field, op, value: { name } }) => `${t(field)} ${t(op)} ${name ? name : ''}`)
                .join(', ') || ''}
            </InlineText>
          );
        };

        columnDetail.cell = CriteriaCellComponent;
      }

      if (columnDetail.id === 'action') {
        const RuleActionCellComponent = (props) =>
          // eslint-disable-next-line react/prop-types
          t(props?.row?.original?.action);

        columnDetail.cell = RuleActionCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const disabled = props?.row?.original?.disabled;

          return <StatusTag value={!disabled} type="ENABLED_DISABLED" />;
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            editIcon={isFormReadOnly ? faEye : faPencilAlt}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            showDelete={!isFormReadOnly}
            showMore={!isFormReadOnly}
            list={tableShowMoreList}
            onActionClick={onShowMoreClick}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onShowMoreClick = (detail, payload = {}) => {
    const policyDetail = payload?.row?.original || {};

    const { setIsOpen = noop } = payload;

    if (detail && policyDetail) {
      apiCall(update({ ...policyDetail, disabled: detail === 'DISABLE' }))
        .catch(noop)
        .finally(() => {
          setIsOpen?.(false);
        });
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default SignonPolicyTable;
