import { useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, useApiCall } from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import { add, update } from '../../ducks/authentication-levels';
import {
  getApiPayloadFromMapping,
  getAuthenticationLevelMapping,
} from '../../ducks/authentication-levels/helper';
import { selectList } from '../../ducks/authentication-levels/selectors';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';

const AuthenticationLevelsActions = () => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();

  const list = useSelector(selectList);

  const {
    selectedTree,
    detail,
    pageMode,
    setPageMode,
    setPageModeActionType,
    setLevelsList,
    setDetail,
    setSelectedTree,
  } = useContext(AuthenticationLevelsPageContext);

  useEffect(() => {
    if (list.length === 0) {
      setPageMode('');
    }
  }, [list]);

  const onSaveClick = () => {
    const { treeMapping = {}, levelDetailMapping = {} } = detail;

    const payload = getApiPayloadFromMapping({ treeMapping, levelDetailMapping });

    apiCall(add(payload))
      .then(() => {
        setPageModeActionType('');
      })
      .catch(noop);
  };

  const onEditClick = () => {
    setPageMode('edit');
  };

  const onUpdateClick = () => {
    const { treeMapping = {}, levelDetailMapping = {} } = detail;

    const payload = getApiPayloadFromMapping({ treeMapping, levelDetailMapping });

    apiCall(update({ id: selectedTree, payload }))
      .then(() => {
        setPageModeActionType('');
      })
      .catch(noop);
  };

  const onCancelClick = () => {
    const levelsList = [];

    list.forEach((level) => {
      const treeMapping = {};
      const levelDetailMapping = {};

      const detail = getAuthenticationLevelMapping({
        levelDetail: cloneDeep(level),
        levelDetailMapping,
        treeMapping,
      });

      levelsList.push(detail);
    });

    const selectedTree = list?.[0]?.id || '';

    setLevelsList(levelsList);
    setDetail(levelsList[0] || {});
    setSelectedTree(selectedTree);

    if (list.length > 0) {
      setPageMode('view');
    } else {
      setPageMode('');
    }

    setPageModeActionType('');
  };

  const renderActionSection = () => {
    if (pageMode === 'view') {
      return (
        <div className="buttons">
          <Button type="secondary" onClick={onEditClick}>
            <FontAwesomeIcon icon={faPencilAlt} className="icon left" /> {t('EDIT')}{' '}
          </Button>
        </div>
      );
    }

    if (pageMode === 'add') {
      return (
        <div className="buttons">
          <Button onClick={onSaveClick}> {t('SAVE')} </Button>
          <Button type="tertiary" onClick={onCancelClick}>
            {t('CANCEL')}
          </Button>
        </div>
      );
    }

    if (pageMode === 'edit') {
      return (
        <div className="buttons">
          <Button onClick={onUpdateClick}> {t('UPDATE')} </Button>
          <Button type="tertiary" onClick={onCancelClick}>
            {t('CANCEL')}
          </Button>
        </div>
      );
    }
  };

  return (
    <article id="admin-authentication-levels" className="page-container is-flex has-jc-sb">
      <section className="typography-header3 page-title">{t('AUTHENTICATION_LEVELS')}</section>

      <section className="is-flex" style={{ marginBottom: '14px' }}>
        {renderActionSection()}
      </section>
    </article>
  );
};

export default AuthenticationLevelsActions;
