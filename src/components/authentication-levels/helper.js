import { defaultValidationDetail } from '@zscaler/zui-component-library';

import { VALIDITY_TYPE_LIST } from '../../ducks/authentication-levels/helper';

export const getFormTooltipDetail = () => {};

export const addNewLevel = ({
  level,
  levelDetailMapping,
  treeMapping,
  subLevels,
  parentLevel,
  nesting,
}) => {
  const newLevel = `${level}.${subLevels.length + 1}`;

  levelDetailMapping[newLevel] = getAddNewLevelDetail(newLevel);

  treeMapping[newLevel] = {
    id: newLevel,
    level: newLevel,
    nesting,
    parentLevel,
    subLevels: [],
    isNestingAllowed: nesting !== 3,
    isSubLevel: newLevel !== '1',
    isLevelNew: true,
  };

  return newLevel;
};

export const getAddNewLevelDetail = (id) => {
  return {
    id,
    name: '',
    timeout: 0,
    validityType: VALIDITY_TYPE_LIST[2],
    description: '',
    userMessage: '',
    childAuthenticationLevels: [],
    isLevelNew: true,
  };
};

export const getCanMoveList = ({
  list = [],
  treeMapping = {},
  currentLevel = '1',
  excludeLevelDetail,
}) => {
  const {
    level: excludeLevel,
    parentLevel: excludeParentLevel,
    subLevels: excludeSubLevel,
  } = excludeLevelDetail;

  if (!(currentLevel === excludeLevel)) {
    const { subLevels = [], nesting } = treeMapping[currentLevel];

    const newNesting = nesting + 1 + excludeSubLevel.length;

    const canMove = currentLevel !== excludeParentLevel && newNesting < 4;

    if (canMove) {
      list.push({ label: currentLevel, value: currentLevel });
    }

    subLevels.forEach((subLevel) => {
      getCanMoveList({
        list,
        treeMapping,
        currentLevel: subLevel,
        excludeLevelDetail,
      });
    });
  }

  return { list, treeMapping, currentLevel, excludeLevelDetail };
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name, timeout, description } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'Name is Required';

    return validationDetail;
  }

  // The AL name can be maximum 16 character long, only alphanumeric, no space, no special characters.
  const ALNameValidationRegex = /^\w{1,16}$/;

  if (!ALNameValidationRegex.test(name)) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message =
      'The AL name can be maximum 16 character long, only alphanumeric, no space, no special characters';

    return validationDetail;
  }

  if (!timeout) {
    validationDetail.isValid = false;
    validationDetail.context = 'timeout';
    validationDetail.type = 'error';
    validationDetail.message = 'Timeout is Required';

    return validationDetail;
  }

  if (!description) {
    validationDetail.isValid = false;
    validationDetail.context = 'description';
    validationDetail.type = 'error';
    validationDetail.message = 'Description is Required';

    return validationDetail;
  }

  return validationDetail;
};
