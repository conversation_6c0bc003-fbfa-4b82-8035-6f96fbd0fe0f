import { useContext } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@zscaler/zui-component-library';

import { getAuthenticationLevelMapping } from '../../ducks/authentication-levels/helper';
import { selectList } from '../../ducks/authentication-levels/selectors';

import { AuthenticationLevelsPageContext } from '../../contexts/AuthenticationLevelsPageContextProvider';
import AuthenticationLevelImage from '../../images/authentication-level.svg';
import { getAddNewLevelDetail } from './helper';

const EmptyAuthenticationLevel = () => {
  const list = useSelector(selectList);

  const { pageMode, setPageMode, setDetail, setLevelsList, setSelectedTree } = useContext(
    AuthenticationLevelsPageContext,
  );

  if (pageMode === 'add' || list.length > 0) {
    return null;
  }

  const onClick = () => {
    setPageMode('add');

    const levelsList = [];

    const treeMapping = {};
    const levelDetailMapping = {};

    const detail = getAuthenticationLevelMapping({
      levelDetail: getAddNewLevelDetail(1),
      levelDetailMapping,
      treeMapping,
    });

    levelsList.push(detail);

    const selectedTree = list?.[0]?.id || '';

    setLevelsList(levelsList);
    setDetail(levelsList[0] || {});
    setSelectedTree(selectedTree);
  };

  return (
    <div className="is-flex has-fd-c has-jc-c has-ai-c" style={{ gap: '32px' }}>
      <img
        src={AuthenticationLevelImage}
        style={{ height: '230px', width: '230px' }}
        alt="brand icon"
      />

      <p className="typography-header3"> Set your first authentication level</p>

      <p style={{ width: '400px', marginTop: '-16px', lineHeight: '20px' }}>
        <span>
          We recommend you use nesting authentication levels to set better policies to protect your
          organisation.{' '}
        </span>
        <a type="tertiary" className="link">
          Learn more
        </a>
      </p>

      <div>
        <Button onClick={onClick}> New Authentication Level </Button>
      </div>
    </div>
  );
};

export default EmptyAuthenticationLevel;
