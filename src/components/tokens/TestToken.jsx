import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faClipboard } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Card, TextArea, useApiCall } from '@zscaler/zui-component-library';

import { isEmpty, noop } from 'lodash-es';

import { getMatchingTokenValidator } from '../../ducks/token-validators';
import { selectMatchingTokenValidator } from '../../ducks/token-validators/selectors';
import { decodeJwt } from '../../ducks/tokens/helper';

const TestToken = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const [token, setToken] = useState('');
  const [jwtToken, setJwtToken] = useState({});

  const matchingTokenValidator = useSelector(selectMatchingTokenValidator);

  const onTokenInput = (evt) => {
    const value = evt?.target?.value || '';
    setToken(value);
  };

  useEffect(() => {
    if (token) {
      setJwtToken(decodeJwt(token));
    }
  }, [token]);

  useEffect(() => {
    if (!isEmpty(jwtToken)) {
      apiCall(getMatchingTokenValidator(token)).catch(noop);
    }
  }, [jwtToken]);

  const encodeTokenContainer = () => {
    return (
      <Card containerClass="has-ai-c has-jc-c encode-token-container">
        {token === '' && (
          <div className="is-flex has-fd-c has-ai-c header-section">
            <FontAwesomeIcon icon={faClipboard} className="icon" />
            <p className="typography-header3"> Paste a token here </p>
          </div>
        )}
        <TextArea
          name="jwtToken"
          value={token}
          onChange={onTokenInput}
          inputClassName="token-textarea"
        />
      </Card>
    );
  };

  const getMatchingTokenValidatorContent = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase decode-header">
          <span>{t('MATCHING_TOKEN_VALIDATOR')}</span>
        </section>
        <Card
          containerClass="decode-value-container"
          containerStyle={{
            minHeight: '70px',
            color: '#A17664',
          }}
        >
          {!isEmpty(matchingTokenValidator) && <pre>{matchingTokenValidator?.name}</pre>}
        </Card>
      </>
    );
  };

  const getHeader = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase decode-header">
          <span>{t('HEADER')}</span>
          <span className="typography-paragraph3">{' (Algorithm and Token Type)'}</span>
        </section>
        <Card
          containerClass="decode-value-container"
          containerStyle={{
            color: '#A17664',
          }}
        >
          {jwtToken?.header && <pre>{JSON.stringify(jwtToken?.header, null, 4)}</pre>}
        </Card>
      </>
    );
  };

  const getPayload = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase decode-header">
          <span>{t('PAYLOAD')}</span>
          <span className="typography-paragraph3">{' (Data)'}</span>
        </section>
        <Card
          containerClass="decode-value-container"
          containerStyle={{
            color: '#BF3AD9',
          }}
        >
          {jwtToken?.payload && <pre>{JSON.stringify(jwtToken?.payload, null, 4)}</pre>}
        </Card>
      </>
    );
  };

  const getSignature = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase decode-header">
          <span>{t('SIGNATURE')}</span>
        </section>
        <Card
          containerClass="decode-value-container"
          containerStyle={{
            color: '#28A9F1',
            wordBreak: 'break-all',
          }}
        >
          {jwtToken?.signature && <>{jwtToken?.signature}</>}
        </Card>
      </>
    );
  };

  const decodeTokenContainer = () => {
    return (
      <>
        {getMatchingTokenValidatorContent()}

        {getHeader()}

        {getPayload()}

        {getSignature()}
      </>
    );
  };

  return (
    <div className="is-flex test-token" style={{ justifyContent: 'space-evenly' }}>
      <div>
        <section className="typography-paragraph1-uppercase" onKeyDown={noop}>
          <span>{t('ENCODED')}</span>
          <span className="typography-paragraph3">{' (Paste a token here)'}</span>
        </section>
        {encodeTokenContainer()}
      </div>
      <div>
        <section className="typography-paragraph1-uppercase" onKeyDown={noop}>
          <span>{t('DECODED')}</span>
          <span className="typography-paragraph3">{' (Test results appear here)'}</span>
        </section>
        {decodeTokenContainer()}
      </div>
    </div>
  );
};
export default TestToken;
