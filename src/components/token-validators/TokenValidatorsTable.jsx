import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  TableContainer,
  TextWithTooltip,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/token-validators/index';
import { selectTableConfig, selectTableDetail } from '../../ducks/token-validators/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const TokenValidatorsTable = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, searchTerm, isFormReadOnly, privileges } =
    useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const { hasFullAccess } = privileges;

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const isSystemRole = props?.row?.original?.isSystemRole;

          return (
            <Actions
              {...props}
              showEdit={true}
              editIcon={hasFullAccess && !isSystemRole ? faPencilAlt : faEye}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={hasFullAccess && !isSystemRole}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'name') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { name } = props?.row?.original || {};

          return <TextWithTooltip text={name} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'type') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { description } = props?.row?.original || {};

          return <TextWithTooltip text={description} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(hasFullAccess && !detail.isSystemRole ? 'edit' : 'view');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default TokenValidatorsTable;
