import { defaultValidationDetail } from '@zscaler/zui-component-library';

import { isEmpty } from 'lodash-es';
import forge from 'node-forge';

import { PERMISSIONS_KEY, PERMISSION_LEVEL } from '../../config';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'TOKEN_VALIDATOR',
  },
  add: {
    headerText: 'ADD_TOKEN_VALIDATOR',
  },
  edit: {
    headerText: 'EDIT_TOKEN_VALIDATOR',
  },
  delete: {
    headerText: 'DELETE_TOKEN_VALIDATOR',
    confirmationMessage:
      'Are you sure you want to delete this Token validator? The changes cannot be undone.',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const PERMISSIONS_LEVEL_DETAIL = {
  [PERMISSION_LEVEL.FULL]: {
    id: PERMISSION_LEVEL.FULL,
    label: 'Full',
    order: 1,
  },
  [PERMISSION_LEVEL.RESTRICTED_FULL]: {
    id: PERMISSION_LEVEL.RESTRICTED_FULL,
    label: 'Restricted Full',
    order: 2,
  },
  [PERMISSION_LEVEL.VIEW]: {
    id: PERMISSION_LEVEL.VIEW,
    label: 'View Only',
    order: 3,
  },
  [PERMISSION_LEVEL.RESTRICTED_VIEW]: {
    id: PERMISSION_LEVEL.RESTRICTED_VIEW,
    label: 'Restricted View',
    order: 4,
  },
  [PERMISSION_LEVEL.NONE]: {
    id: PERMISSION_LEVEL.NONE,
    label: 'None',
    order: 5,
  },
};

export const getFormValidationDetail = ({ formValues, isZdkCluster }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name, formattedCustomerKey, keyUrl, tokenConfig } = formValues || {};

  const claims = isZdkCluster ? [...tokenConfig.claims] : [...tokenConfig];

  if (!name) {
    return setError(validationDetail, 'name', 'NAME_REQUIRED_MESSAGE');
  }

  // Check for ISS claim
  if (!isZdkCluster && !formValues?.iss) {
    return setError(validationDetail, 'iss', 'ISS_CLIAM_REQUIRED');
  }

  // Check for duplicate ISS claim
  if (!isZdkCluster && claims.length > 0) {
    const issClaim = claims.find((config) => config?.claim === 'iss');
    if (issClaim) {
      return setError(
        validationDetail,
        `CLAIM_NAME${issClaim.id}`,
        'ISS_CLIAM_IS_ALREADY_CONFIGURED',
      );
    }
  }

  // Check for claim duplicates and empty claims
  if (claims.length > 0) {
    const claimIndices = new Map();
    const duplicates = {};
    let hasClaimNameError = false;
    let hasClaimValueError = false;

    claims.forEach((obj, index) => {
      if (claimIndices.has(obj.claim)) {
        duplicates[obj.claim] = obj;
      }
      claimIndices.set(obj.claim, index);
    });

    if (!isEmpty(duplicates)) {
      const duplicate = Object.values(duplicates)[0];
      return setError(
        validationDetail,
        `CLAIM_NAME${duplicate.id}`,
        'DUPLICATE_CLAIM_CANNOT_BE_CONFIGURED',
      );
    }

    for (let i = 0; i < claims.length; i++) {
      if (!claims[i].claim) {
        hasClaimNameError = true;
        break;
      }
      if (!claims[i].value) {
        hasClaimValueError = true;
        break;
      }
    }

    if (hasClaimNameError || hasClaimValueError) {
      const claim = claims.find((obj) => !obj.claim || !obj.value);
      return setError(
        validationDetail,
        hasClaimNameError ? `CLAIM_NAME${claim.id}` : `CLAIM_VALUE${claim.id}`,
        hasClaimNameError ? 'CLAIM_CANNOT_BE_EMPTY' : 'VALUE_CANNOT_BE_EMPTY',
      );
    }
  }

  if (isEmpty(formattedCustomerKey) && keyUrl === '') {
    return setError(
      validationDetail,
      'validationType',
      'Either JWKS url or Public certificate is required.',
    );
  }

  if (!isEmpty(formattedCustomerKey) && keyUrl !== '') {
    return setError(
      validationDetail,
      'validationType',
      'Token validators cannot have both JWKS url and Public certificates.',
    );
  }

  if (!isEmpty(formattedCustomerKey)) {
    const errorKey = formattedCustomerKey.find((key) => key.hasError);
    if (errorKey?.hasError) {
      return setError(
        validationDetail,
        `customerKey${errorKey.id}`,
        'Invalid certificate or public key',
      );
    }
  }

  return validationDetail;
};

const setError = (validationDetail, context, message) => {
  validationDetail.isValid = false;
  validationDetail.context = context;
  validationDetail.type = 'error';
  validationDetail.message = message;
  return validationDetail;
};

export const getCustomerKeyInfoDetail = (customerKey) => {
  const validationDetail = { ...defaultValidationDetail };
  if (customerKey.hasError) {
    validationDetail.isValid = false;
    validationDetail.context = `customerKey${customerKey.id}`;
    validationDetail.type = 'error';
    validationDetail.message = 'Invalid certificate or public key';
  }
  return validationDetail;
};

export const PERMISSION_KEY_DETAILS = {
  [PERMISSIONS_KEY.SIGN_ON_POLICY]: {
    label: 'SIGN_ON_POLICIES',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Policy &gt; Admin Sign-On</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_METHODS]: {
    label: 'AUTHENTICATION_METHODS',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Policy &gt; Password</strong> and{' '}
        <strong className="tooltip-bold">Administration &gt; Authentication Methods</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY]: {
    label: 'USERS_AND_GROUPS_TEXT',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Administration &gt; Users </strong>
        , <strong>Administration &gt; Groups </strong> and{' '}
        <strong className="tooltip-bold">Administration &gt; User Attributes</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY]: {
    label: 'USERS_CREDENTIALS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">
          Administration &gt; Users &gt; Edit User &gt; Security Settings{' '}
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.EXTERNAL_IDENTITIES_POLICY]: {
    label: 'EXTERNAL_IDENTITIES',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; External Identities </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.IP_LOCATION_POLICY]: {
    label: 'IP_LOCATIONS_AND_GROUPS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; IP Locations </strong> and{' '}
        <strong className="tooltip-bold">Administration &gt; IP Locations Groups</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.LINKED_TENATS_POLICY]: {
    label: 'LINKED_TENANTS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Linked Services </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY]: {
    label: 'AUTHENTICATION_SESSION',
    tooltip: (
      <p>
        Set the access level to the Authentication Session section in{' '}
        <strong className="tooltip-bold">Administration &gt; Advanced Settings </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY]: {
    label: 'ADMINISTRATIVE_ENTITLEMENTS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Administrative </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY]: {
    label: 'SERVICE_ENTITLEMENTS_TEXT',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Service </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUDIT_LOGS_POLICY]: {
    label: 'AUDIT_LOGS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Audit Logs </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_EVENT_LOG_POLICY]: {
    label: 'AUTHENTICATION_EVENT_LOG',
    tooltip: '',
  },
  [PERMISSIONS_KEY.ROLES_POLICY]: {
    label: 'ROLES',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Administration &gt; Roles </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.GUEST_DOMAIN_POLICY]: {
    label: 'GUEST_DOMAIN',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Domains </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.REMOTE_ASSISTANCE_MANAGEMENT]: {
    label: 'REMOTE_ASSISTANCE',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Help &gt; Remote Assistance </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.BRANDING]: {
    label: 'BRANDING',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Branding </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the location`;
  }

  if (name === 'validationType') {
    tooltipDetail.content = `Select from the validation type list`;
  }

  return tooltipDetail;
};

export const CONFIGURATION_MODE = {
  CLIENT_JWKS: 'URL',
  CERTIFICATES_PUBLIC_KEYS: 'UPLOAD',
};

export const CONFIGURATION_LIST = [
  { label: 'CLIENT_JWKS_URL', value: CONFIGURATION_MODE.CLIENT_JWKS },
  { label: 'CERTIFICATES_PUBLIC_KEYS', value: CONFIGURATION_MODE.CERTIFICATES_PUBLIC_KEYS },
];

export const TOKEN_TYPE = {
  JWT: 1,
  SAML: 2,
  API: 3,
};

export const TOKEN_TYPE_NAME = {
  1: 'JWT',
  2: 'SAML',
  3: 'API',
};

export const KEY_TYPE = {
  ASYMETTRIC: 1,
  SYMETTRIC: 2,
  API: 3,
};

export const KEY_TYPE_NAME = {
  1: 'ASYMETTRIC',
  2: 'SYMETTRIC',
  3: 'API',
};

export const getUploadedFilesData = async (detail) => {
  const filesData = [];

  const readFileAsText = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        const fileContent = event.target.result;
        resolve(fileContent);
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsText(file);
    });
  };

  for (let index = 0; index < detail.length; index++) {
    const file = detail[index];

    try {
      const fileContent = await readFileAsText(file);
      filesData.push({
        fileName: file?.name,
        value: fileContent,
      });
    } catch (error) {
      console.error('Error reading file:', error);
    }
  }

  return filesData;
};

/**
 * Formats the uploaded files data and checks if they are certificates or public keys.
 *
 * @param {Array} keys - An array of objects containing file data.
 * @param {Boolean} isZdkCluster - A flag indicating whether it's a ZDK cluster.
 * @returns {Array} An array of formatted file data.
 */
export const getUploadedFilesStatus = (keys, isZdkCluster) => {
  const cloneFilesData = [...keys];
  const formattedData = [];

  const parseCertificate = (element) => {
    try {
      const certData = forge.pki.certificateFromPem(element.value);
      return {
        id: element.id,
        fileName: element.fileName || certData.subject.getField('CN').value,
        expiry: certData.validity.notAfter,
        hasError: false,
        value: element.value,
      };
    } catch (error) {
      console.log(error);
      return {
        id: element.id,
        hasError: false,
        fileName: element.fileName,
        expiry: 'NA',
        value: element.value,
      };
    }
  };

  const parsePublicKey = (element) => {
    try {
      const pubKeyData = forge.pki.publicKeyFromPem(element.value);
      return {
        id: element.id,
        fileName: element.fileName || `Public Key - ${pubKeyData.e.toString(16)}`,
        expiry: 'NA',
        hasError: false,
        value: element.value,
      };
    } catch (error) {
      console.log(error);
      return {
        id: element.id,
        hasError: true,
        fileName: element.fileName,
        expiry: 'NA',
        value: element.value,
      };
    }
  };

  for (let index = 0; index < cloneFilesData.length; index++) {
    let element = { ...cloneFilesData[index] };
    element.id = index;

    let dataToPush;
    if (element.value.includes('PRIVATE')) {
      dataToPush = {
        id: element.id,
        hasError: true,
        fileName: element.fileName,
        expiry: 'NA',
        value: element.value,
      };
    } else if (isZdkCluster) {
      dataToPush = parseCertificate(element) || parsePublicKey(element);
    } else {
      dataToPush = parseCertificate(element);
    }

    formattedData.push(dataToPush);
  }

  return formattedData;
};
