import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faPlus, faTrash, faUpload } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  DropDown,
  Field,
  FieldGroup,
  Input,
  InputFile,
  RadioButtons,
  TextWithTooltip,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
} from '@zscaler/zui-component-library';

import { cloneDeep, find, findIndex } from 'lodash-es';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { selectZDKIds } from '../../ducks/token-validators/selectors';

import { TokenValidatorsContext } from '../../pages/admin/TokenValidatorsPage';

import { PERMISSIONS_KEY } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import {
  CONFIGURATION_LIST,
  CONFIGURATION_MODE,
  KEY_TYPE,
  TOKEN_TYPE,
  TOKEN_TYPE_NAME,
  getCustomerKeyInfoDetail,
  getFormTooltipDetail,
  getFormValidationDetail,
  getUploadedFilesData,
  getUploadedFilesStatus,
} from './helper';

const defaultProps = {
  ...defaultFormProps,
};

const TokenValidatorsForm = ({ mode, validationDetail, setValidationDetail }) => {
  const { t } = useTranslation();

  const protocol = window?.location?.protocol || '';
  const hostName = window?.location?.hostname || '';

  const audience = `${protocol}//${hostName?.replace('-admin', '')}`;

  const { isZdkCluster } = useContext(TokenValidatorsContext);

  const { detail, setDetail } = useContext(CRUDPageContext);

  const [selectedZDKIdsList, setSelectedZDKIdsList] = useState([]);

  const zdkIdsList = useSelector(selectZDKIds);

  const [formValues, setFormValues] = useState({
    name: '',
    tenantId: '',
    tokenType: TOKEN_TYPE.JWT,
    keyType: KEY_TYPE.ASYMETTRIC,
    keyUrl: '',
    customerKey: [],
    formattedCustomerKey: [],
    subjectClaim: 'sub',
    ...(isZdkCluster && { zdkId: '', encrypted: false, configId: '', tokenConfig: { claims: [] } }),
    ...(!isZdkCluster && { validatorId: '', audience, iss: '', tokenConfig: [] }),
    ...detail,
  });

  const [activeConfigurationMode, setActiveConfigurationMode] = useState(
    formValues?.keyUrl
      ? CONFIGURATION_MODE.CLIENT_JWKS
      : CONFIGURATION_MODE.CERTIFICATES_PUBLIC_KEYS,
  );

  useEffect(() => {
    if (isZdkCluster) {
      const selectedOption = find(zdkIdsList, { value: detail?.zdkId });
      if (selectedOption?.value) {
        setSelectedZDKIdsList([selectedOption]);
      }
    }
  }, [zdkIdsList, detail]);

  useEffect(() => {
    const claims = getClaims();

    if (claims?.length > 0) {
      claims.forEach((claim, index) => {
        claim.id = index + 1;
      });
      setClaimFormValues(claims);
    }

    if (formValues?.customerKey.length > 0) {
      const keys = !isZdkCluster
        ? formValues.customerKey
        : formValues.customerKey.map((key) => {
            return {
              fileName: key,
              value: key,
            };
          });
      const formattedData = getUploadedFilesStatus(keys, isZdkCluster);
      setFormValues((prevState) => ({
        ...prevState,
        formattedCustomerKey: [...formValues.formattedCustomerKey, ...formattedData],
      }));
    }
  }, []);

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.TOKEN_VALIDATOR_POLICY));

  const { hasFullAccess } = privileges;

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    setDetail({ ...formValues });

    const formValidationDetail = getFormValidationDetail({ formValues, mode, isZdkCluster });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const checkUpdatePermission = () => {
    return hasFullAccess && !formValues.isSystemRole;
  };

  const onUploadCertificates = async (detail) => {
    if (detail?.length > 0) {
      const filesData = await getUploadedFilesData(detail);
      const formattedData = await getUploadedFilesStatus(
        [...formValues.formattedCustomerKey, ...filesData],
        isZdkCluster,
      );

      setFormValues((prevState) => ({
        ...prevState,
        formattedCustomerKey: [...formattedData],
      }));
    }
  };

  const isRadioButtonSelected = (detail = {}) => {
    const { value } = detail;

    return value === activeConfigurationMode;
  };

  const onRadioButtonsClick = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setActiveConfigurationMode(value);
    }
  };

  const handleDeleteCustomerKey = (id) => {
    const data = cloneDeep(formValues?.formattedCustomerKey);
    data.splice(findIndex(data, ['id', id]), 1);
    setFormValues((prevState) => ({ ...prevState, formattedCustomerKey: [...data] }));
  };

  const renderSelectedValidationType = () => {
    return (
      <>
        {activeConfigurationMode === CONFIGURATION_MODE.CLIENT_JWKS && (
          <>
            <FieldGroup>
              <Input
                name="keyUrl"
                label="CLIENT_JWKS"
                onChange={onFormFieldChange}
                value={formValues.keyUrl}
                disabled={!hasFullAccess || !checkUpdatePermission()}
                maxLength="128"
                info={validationDetail}
              />
            </FieldGroup>
          </>
        )}

        {activeConfigurationMode === CONFIGURATION_MODE.CERTIFICATES_PUBLIC_KEYS && (
          <>
            {formValues?.formattedCustomerKey.length > 0 &&
              formValues?.formattedCustomerKey.map((custKeyData, index) => {
                const customerKeyInfoDetail = getCustomerKeyInfoDetail(custKeyData);
                return (
                  <FieldGroup key={index}>
                    <Field
                      label="NAME"
                      htmlFor={`customerKey${custKeyData.id}`}
                      info={customerKeyInfoDetail}
                    >
                      <TextWithTooltip>{custKeyData.fileName}</TextWithTooltip>
                    </Field>

                    <Field label="EXPIRY">
                      <TextWithTooltip>{custKeyData.expiry.toString()}</TextWithTooltip>
                    </Field>

                    <Button
                      type="tertiary"
                      onClick={() => handleDeleteCustomerKey(custKeyData.id)}
                      containerClass="no-p-l content-width"
                      style={{ marginTop: '20px' }}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </FieldGroup>
                );
              })}

            <Field label="CERTIFICATES_PUBLIC_KEYS">
              <InputFile
                onChange={onUploadCertificates}
                leftIcon={faUpload}
                hasLeftIcon
                buttonLabel="UPLOAD"
                buttonType="tertiary"
                buttonContainerClass="no-p-l"
                fileInfoPosition={'none'}
                isMultipleFiles
              />
            </Field>
          </>
        )}
      </>
    );
  };

  const renderCertificatesPublic = () => {
    return (
      <>
        <TextWithTooltip containerStyle={{ fontSize: '1.5rem', marginTop: '3rem' }}>
          {t('SIGNATURE_VALIDATION')}
        </TextWithTooltip>
        <Field
          htmlFor="validationType"
          label="VALIDATION_TYPE"
          containerClass="radio-container"
          info={validationDetail}
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('validationType')}
        >
          <RadioButtons
            list={CONFIGURATION_LIST}
            isSelected={isRadioButtonSelected}
            onClick={onRadioButtonsClick}
            containerClass={!hasFullAccess ? 'disabled' : ''}
          />
        </Field>

        {renderSelectedValidationType()}
      </>
    );
  };

  const onZDKIdSelection = (payload) => {
    setSelectedZDKIdsList(payload);
    setFormValues((prevState) => ({ ...prevState, zdkId: payload?.[0]?.value || '' }));
  };

  const getClaims = () => {
    if (isZdkCluster) {
      return cloneDeep(formValues?.tokenConfig?.claims);
    }
    return cloneDeep(formValues?.tokenConfig);
  };

  const setClaimFormValues = (claims) => {
    setFormValues((prevState) => ({
      ...prevState,
      ...(isZdkCluster ? { tokenConfig: { claims: claims } } : { tokenConfig: [...claims] }),
    }));
  };

  const setTokenConfigClaims = (name, value, index) => {
    const claims = getClaims();
    claims[index][name] = value;
    setClaimFormValues(claims);
  };

  const handleAddClaim = () => {
    let claims = getClaims();

    if (claims.length === 0) {
      claims = [{ id: 1, claim: '', value: '' }];
    } else {
      claims.push({ id: claims.length + 1, claim: '', value: '' });
    }
    setClaimFormValues(claims);
  };

  const handleDeleteClaim = (index) => {
    let claims = getClaims();
    claims.splice(index, 1);
    setClaimFormValues(claims);
  };

  const renderGeneralSection = () => {
    let claims = getClaims();

    return (
      <>
        <Field
          label="TYPE"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpCertificate')}
        >
          <TextWithTooltip>{TOKEN_TYPE_NAME[TOKEN_TYPE.JWT]}</TextWithTooltip>
        </Field>

        <FieldGroup>
          <Input
            name="name"
            label="NAME"
            onChange={onFormFieldChange}
            value={formValues.name}
            disabled={!hasFullAccess || !checkUpdatePermission()}
            maxLength="128"
            info={validationDetail}
          />
        </FieldGroup>

        {isZdkCluster && (
          <Field label="APPLICATION">
            <DropDown
              list={zdkIdsList}
              selectedList={selectedZDKIdsList}
              onSelection={onZDKIdSelection}
              hasSearch
              containerClass="full-width"
              containerStyle={{ maxWidth: '250px' }}
            />
          </Field>
        )}

        <TextWithTooltip containerStyle={{ fontSize: '1.5rem', marginTop: '3rem' }}>
          {t('CLAIM_REQUIREMENTS')}
        </TextWithTooltip>

        {!isZdkCluster && (
          <Field label="AUDIENCE" htmlFor="audience" containerStyle={{ maxWidth: '260px' }}>
            <TextWithTooltip text={audience} canCopy showCopy>
              {audience}
            </TextWithTooltip>
          </Field>
        )}
        <FieldGroup>
          <Input
            name="subjectClaim"
            label="SUBJECT_CLAIM"
            value={formValues.subjectClaim}
            onChange={onFormFieldChange}
            disabled={!hasFullAccess || !checkUpdatePermission()}
            maxLength="128"
            info={validationDetail}
          />
        </FieldGroup>
        {!isZdkCluster && (
          <FieldGroup containerStyle={{ width: '93%' }}>
            <Input label="CLAIM" value="iss" disabled maxLength="128" info={validationDetail} />
            <Input
              name="iss"
              label="VALUE"
              onChange={onFormFieldChange}
              value={formValues.iss}
              maxLength="256"
              disabled={!hasFullAccess || !checkUpdatePermission()}
              info={validationDetail}
            />
          </FieldGroup>
        )}

        {claims?.length > 0 &&
          claims?.map((claimData, index) => {
            return (
              <FieldGroup key={index}>
                <Input
                  name={`CLAIM_NAME${claimData.id}`}
                  label="CLAIM"
                  onChange={(evt) => setTokenConfigClaims('claim', evt.target.value, index)}
                  value={claimData.claim}
                  disabled={!hasFullAccess || !checkUpdatePermission()}
                  maxLength="128"
                  info={validationDetail}
                />
                <Input
                  name={`CLAIM_VALUE${claimData.id}`}
                  label="VALUE"
                  onChange={(evt) => setTokenConfigClaims('value', evt.target.value, index)}
                  value={claimData.value}
                  maxLength="256"
                  disabled={!hasFullAccess || !checkUpdatePermission()}
                  info={validationDetail}
                />

                <Button
                  type="tertiary"
                  onClick={() => handleDeleteClaim(index)}
                  containerClass="no-p-l content-width"
                  style={{ marginTop: '20px' }}
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              </FieldGroup>
            );
          })}

        <Button type="secondary" onClick={handleAddClaim}>
          <FontAwesomeIcon icon={faPlus} className="icon left" />
          {t('ADD_CLAIM')}
        </Button>
      </>
    );
  };

  return (
    <section className="token-validator-form-container">
      {renderGeneralSection()}
      {renderCertificatesPublic()}
    </section>
  );
};

TokenValidatorsForm.defaultProps = defaultProps;

TokenValidatorsForm.propTypes = { ...defaultFormPropTypes };

export default TokenValidatorsForm;
