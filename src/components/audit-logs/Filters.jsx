import { useSelector } from 'react-redux';

import {
  CalendarDropDown,
  DropDown,
  Field,
  FieldGroup,
  MultiSelection,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  getActionTypesEnums,
  getCategoriesEnumsMapping,
  getInterfacesEnums,
  getResultsEnums,
} from '../../ducks/audit-logs';
import {
  selectActionTypesEnums,
  selectActionTypesEnumsList,
  selectActiveSubCategoriesEnums,
  selectActiveSubCategoriesEnumsList,
  selectCategoriesEnums,
  selectCategoriesEnumsList,
  selectCategoriesMapping,
  selectInterfacesEnums,
  selectInterfacesEnumsList,
  selectResultsEnums,
  selectResultsEnumsList,
} from '../../ducks/audit-logs/selectors';

import { calendarConfig } from './helper';

const defaultSelectedItem = [{ label: 'ALL', value: 'ALL' }];

const tooltipProps = {
  placement: 'right',
  offset: 30,
};

const defaultProps = {
  selectedFilter: {},
  setSelectedFilter: noop,
};

const Filters = ({ selectedFilter, setSelectedFilter }) => {
  const interfacesEnums = useSelector(selectInterfacesEnums);
  const interfacesEnumsList = useSelector(selectInterfacesEnumsList);

  const {
    isDropDownLoading: isInterfacesDropDownLoading,
    onDropDownOpen: onInterfacesDropDownOpen,
  } = useDropDownActions({
    detail: interfacesEnums,
    apiCallFunc: getInterfacesEnums,
    fetchOnce: true,
  });

  const resultsEnums = useSelector(selectResultsEnums);
  const resultsEnumsList = useSelector(selectResultsEnumsList);

  const { isDropDownLoading: isResultsDropDownLoading, onDropDownOpen: onResultsDropDownOpen } =
    useDropDownActions({
      detail: resultsEnums,
      apiCallFunc: getResultsEnums,
      fetchOnce: true,
    });

  const actionTypesEnums = useSelector(selectActionTypesEnums);
  const actionTypesEnumsList = useSelector(selectActionTypesEnumsList);

  const {
    isDropDownLoading: isActionTypesDropDownLoading,
    onDropDownOpen: onActionTypesDropDownOpen,
  } = useDropDownActions({
    detail: actionTypesEnums,
    apiCallFunc: getActionTypesEnums,
    fetchOnce: true,
  });

  const categoriesEnums = useSelector(selectCategoriesEnums);
  const categoriesList = useSelector(selectCategoriesEnumsList);

  const {
    isDropDownLoading: isCategoriesDropDownLoading,
    onDropDownOpen: onCategoriesDropDownOpen,
  } = useDropDownActions({
    detail: categoriesEnums,
    apiCallFunc: getCategoriesEnumsMapping,
    fetchOnce: true,
  });

  const getSelectedList = (filterName) => {
    const filterDetail = selectedFilter[filterName];

    if (filterDetail?.length > 0) {
      return filterDetail;
    } else {
      return [];
    }
  };

  const subCategoriesEnums = selectActiveSubCategoriesEnums(
    useSelector(selectCategoriesMapping),
    getSelectedList('category')?.[0]?.value || 'ALL',
  );
  const subCategoriesList = selectActiveSubCategoriesEnumsList(
    useSelector(selectCategoriesMapping),
    getSelectedList('category')?.[0]?.value || 'ALL',
  );

  const {
    isDropDownLoading: isSubCategoriesDropDownLoading,
    onDropDownOpen: onSubCategoriesDropDownOpen,
  } = useDropDownActions({
    detail: subCategoriesEnums,
    apiCallFunc: getCategoriesEnumsMapping,
    fetchOnce: true,
  });

  const onFilterSelection = (filterName) => (detail) =>
    setSelectedFilter((prevState) => {
      const selectedFilter = cloneDeep(prevState);

      if (filterName === 'category') {
        delete selectedFilter.subcategories;
      }

      return { ...selectedFilter, [filterName]: detail };
    });

  return (
    <FieldGroup containerClass="buttons audit-logs-filter-contianer">
      <Field label="TIME_RANGE" containerClass="no-m-b">
        <CalendarDropDown
          level="REPORT"
          selectedList={getSelectedList('timeRange')}
          setSelectedList={onFilterSelection('timeRange')}
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
          config={calendarConfig}
        />
      </Field>

      <Field label="ACTION" containerClass="no-m-b">
        <DropDown
          list={actionTypesEnumsList}
          selectedList={getSelectedList('actionTypes')}
          onSelection={onFilterSelection('actionTypes')}
          onOpen={onActionTypesDropDownOpen}
          loading={isActionTypesDropDownLoading}
          placeholderList={defaultSelectedItem}
          renderItemsSelection={(props) => (
            <MultiSelection
              unselectedTitle="Unselected Items"
              selectedTitle="Selected Items"
              {...props}
            />
          )}
          isMulti
          hasSearch
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
        />
      </Field>

      <Field label="CATEGORY" containerClass="no-m-b">
        <DropDown
          list={[...defaultSelectedItem, ...categoriesList]}
          selectedList={getSelectedList('category')}
          onSelection={onFilterSelection('category')}
          onOpen={onCategoriesDropDownOpen}
          loading={isCategoriesDropDownLoading}
          placeholderList={defaultSelectedItem}
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
        />
      </Field>

      <Field label="SUB_CATEGORY" containerClass="no-m-b">
        <DropDown
          list={subCategoriesList}
          selectedList={getSelectedList('subcategories')}
          onSelection={onFilterSelection('subcategories')}
          onOpen={onSubCategoriesDropDownOpen}
          loading={isSubCategoriesDropDownLoading}
          placeholderList={defaultSelectedItem}
          renderItemsSelection={(props) => (
            <MultiSelection
              unselectedTitle="Unselected Items"
              selectedTitle="Selected Items"
              {...props}
            />
          )}
          isMulti
          hasSearch
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
        />
      </Field>

      <Field label="INTERFACE" containerClass="no-m-b">
        <DropDown
          list={[...defaultSelectedItem, ...interfacesEnumsList]}
          selectedList={getSelectedList('actionInterface')}
          onSelection={onFilterSelection('actionInterface')}
          onOpen={onInterfacesDropDownOpen}
          loading={isInterfacesDropDownLoading}
          placeholderList={defaultSelectedItem}
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
        />
      </Field>

      <Field label="RESULT" containerClass="no-m-b">
        <DropDown
          list={[...defaultSelectedItem, ...resultsEnumsList]}
          selectedList={getSelectedList('actionResult')}
          onSelection={onFilterSelection('actionResult')}
          onOpen={onResultsDropDownOpen}
          loading={isResultsDropDownLoading}
          placeholderList={defaultSelectedItem}
          selectedItemsProps={{
            containerClass: 'audit-logs-drop-down-container',
          }}
          selectedItemsTooltipProps={{
            ...tooltipProps,
          }}
        />
      </Field>
    </FieldGroup>
  );
};

Filters.defaultProps = defaultProps;

Filters.propTypes = {
  selectedFilter: PropTypes.object,
  setSelectedFilter: PropTypes.func,
};

export default Filters;
