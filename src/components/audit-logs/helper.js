import dayjs from 'dayjs';

const singleEnumSelection = ['category', 'actionInterface', 'actionResult'];

export const calendarConfig = {
  minDate: dayjs().subtract(6, 'M'),
  maxInterval: dayjs().diff(dayjs().subtract(6, 'M'), 'seconds'),
  message: { text: 'Logs are avaiable for the last 6 Months' },
};

export const getFilterApiPayload = ({ selectedFilter }) => {
  const filterApiPayload = {};

  Object.keys(selectedFilter).forEach((filterName) => {
    const filterDetail = selectedFilter[filterName];

    if (Array.isArray(filterDetail) && filterDetail.length > 0) {
      if (filterName == 'timeRange') {
        const { startTime, endTime } = selectedFilter[filterName]?.[0] || {};

        filterApiPayload['startTime'] = dayjs(startTime).valueOf();
        filterApiPayload['endTime'] = dayjs(endTime).valueOf();

        filterApiPayload.isDateRangeValid =
          filterApiPayload['endTime'] > filterApiPayload['startTime'];

        return;
      }

      filterApiPayload[filterName] = filterDetail.map(({ value }) => value);

      if (singleEnumSelection.indexOf(filterName) !== -1) {
        filterApiPayload[filterName] = filterApiPayload[filterName].join('');

        if (filterApiPayload[filterName] === 'ALL') {
          delete filterApiPayload[filterName];
        }
      }
    }
  });

  return filterApiPayload;
};
