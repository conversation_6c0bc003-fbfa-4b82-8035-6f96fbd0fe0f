import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Card, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { selectPermissionsByKey } from '../../../ducks/permissions/selectors';
import { getList } from '../../../ducks/session-attributes';

import { PERMISSIONS_KEY } from '../../../config';
import CRUDPageContextProvider from '../../../contexts/CRUDPageContextProvider';
import NoAccess from '../../no-access/NoAccess';
import SessionAttributeActions from './SessionAttributeActions';
import SessionAttributeCRUD from './SessionAttributeCRUD';
import SessionAttributeTable from './SessionAttributeTable';

const SessionAttributeContainer = () => {
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <CRUDPageContextProvider privileges={privileges}>
      <Card>
        <SessionAttributeActions />

        <SessionAttributeTable />
      </Card>

      <SessionAttributeCRUD />
    </CRUDPageContextProvider>
  );
};

export default SessionAttributeContainer;
