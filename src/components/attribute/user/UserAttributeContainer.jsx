import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Card, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { selectPermissionsBy<PERSON>ey } from '../../../ducks/permissions/selectors';
import { getList } from '../../../ducks/user-attributes';

import { PERMISSIONS_KEY } from '../../../config';
import CRUDPageContextProvider from '../../../contexts/CRUDPageContextProvider';
import NoAccess from '../../no-access/NoAccess';
import UserAttributeActions from './UserAttributeActions';
import UserAttributeCRUD from './UserAttributeCRUD';
import UserAttributeTable from './UserAttributeTable';

const UserAttributeContainer = () => {
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges || {};

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <CRUDPageContextProvider privileges={privileges}>
      <Card>
        <UserAttributeActions />

        <UserAttributeTable />
      </Card>

      <UserAttributeCRUD />
    </CRUDPageContextProvider>
  );
};

export default UserAttributeContainer;
