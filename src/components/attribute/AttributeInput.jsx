import { DropDown, Field, InlineDatePicker, Input } from '@zscaler/zui-component-library';

import { find, noop } from 'lodash-es';
import PropTypes from 'prop-types';

const BOOLEAN_OPTIONS = [
  { label: 'TRUE', value: 'True' },
  { label: 'FALSE', value: 'False' },
];

const defaultProps = {
  detail: {},
  onChange: noop,
  value: '',
};

const getInputType = (dataType) => {
  if (dataType === 'DECIMAL' || dataType === 'INTEGER') {
    return 'number';
  }

  if (dataType === 'DATE') {
    return 'date';
  }

  return 'text';
};

const AttributeInput = ({ detail, onChange, value, ...rest }) => {
  const inputType = getInputType(detail.dataType);

  const onValueChange = (evt) => {
    if (detail.dataType === 'INTEGER') {
      if (evt.target.value < -2147483648) {
        evt.target.value = -2147483648;
      }

      if (evt.target.value > 2147483647) {
        evt.target.value = 2147483647;
      }
    }

    onChange(evt);
  };

  const onBooleanSelection = (selectionDetail) => {
    const { value } = selectionDetail[0] || {};

    const evt = {
      target: {
        name: detail.attrName,
        value: value,
      },
    };

    onChange(evt);
  };

  if (detail.dataType === 'BOOLEAN') {
    const selectedDetail = find(BOOLEAN_OPTIONS, { value });

    const selectedList = selectedDetail ? [selectedDetail] : [];

    return (
      <Field label={detail.displayName} htmlFor={detail.attrName} {...rest}>
        <DropDown
          list={BOOLEAN_OPTIONS}
          selectedList={selectedList}
          onSelection={onBooleanSelection}
          toggleSelected
          containerStyle={{ minWidth: '100%' }}
          disabled={rest.disabled}
        />
      </Field>
    );
  }

  if (detail.dataType === 'DATE') {
    return (
      <InlineDatePicker
        label={detail.displayName}
        name={detail.attrName}
        selectedDate={value}
        onChange={onValueChange}
        elementProps={{ containerStyle: { height: '32px' }, disabled: rest.disabled }}
        {...rest}
      />
    );
  }

  return (
    <Input
      type={inputType}
      label={detail.displayName}
      name={detail.attrName}
      onChange={onValueChange}
      value={value}
      maxLength="128"
      {...rest}
    />
  );
};

AttributeInput.defaultProps = defaultProps;

AttributeInput.propTypes = {
  detail: PropTypes.object,
  onChange: PropTypes.func,
  value: PropTypes.any,
};

export default AttributeInput;
