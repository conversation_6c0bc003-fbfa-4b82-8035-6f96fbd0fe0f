import { useState } from 'react';
import { useDispatch } from 'react-redux';

import { faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Card,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { showErrorNotification } from '../../ducks/global';
import { updateMigrationStatus } from '../../ducks/migration';

const MigrationProvisioned = ({ status = {}, extIdpAuthnSuccess = true }) => {
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();

  const [showMigrateAdminModal, setShowMigrateAdminModal] = useState(false);

  const [showUpdateStatusWarning, setShowUpdateStatusWarning] = useState(false);
  const [warningSummary, setWarningSummary] = useState('');

  const onMigrateClick = () => {
    setShowMigrateAdminModal(true);
  };

  const onSaveClick = () => {
    apiCall(updateMigrationStatus(status.nextStatus), {
      hasNotification: false,
    }).catch((response) => {
      if (response.apiErrorLevel === 'WARN') {
        setShowUpdateStatusWarning(true);
        setWarningSummary(response.errorSummary);
      } else {
        dispatch(showErrorNotification({ message: response?.errorSummary }));
      }
    });
  };

  const onCloseClick = () => {
    setShowMigrateAdminModal(false);
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const onOverrideAuditorsSaveClick = () => {
    apiCall(updateMigrationStatus(status.nextStatus, true), {
      hasNotification: false,
    }).catch((response) => {
      if (response.apiErrorLevel === 'WARN') {
        setShowUpdateStatusWarning(true);
        setWarningSummary(response.errorSummary);
      } else {
        dispatch(showErrorNotification({ message: response?.errorSummary }));
      }
    });
  };

  const onOverrideAuditorsCloseClick = () => {
    setShowUpdateStatusWarning(false);
    setWarningSummary('');
  };

  const renderMigrationForm = () => {
    if (!extIdpAuthnSuccess) {
      return (
        <Card containerStyle={{ lineHeight: '140%' }}>
          <p className="typography-header5" style={{ color: '#1e1f22', margin: '2rem 0' }}>
            Configure External Identity Provider
          </p>

          <p style={{ marginBottom: '2rem' }}>
            Before proceeding with admin migration, ensure that an external IdP is configured,
            enabled, and at least one single sign-on transaction has succeeded in the last 10 days.
          </p>

          <p>
            Our records indicate you used an external IdP to authenticate administrators to one or
            more of the services linked to ZIdentity. Ensuring users can authenticate and SSO from
            an IdP reduces the risk of disruption.
          </p>
        </Card>
      );
    }

    return (
      <Card containerStyle={{ lineHeight: '140%', padding: '0px' }}>
        <div className="admin-migration-warning is-flex">
          <FontAwesomeIcon icon={faExclamationTriangle} className="has-color-warning has-as-c" />
          {
            'ZIdentity assigned administrative entitlements will override the underlying linked service (e.g. Internet Access, Private Access) admin entitlements. The overridden entitlements cannot be reverted.'
          }
        </div>

        <p>
          This action changes the way administrators access the system. Before proceeding with the
          migration, ensure the following:
        </p>

        <ul style={{ margin: '1rem 3rem' }}>
          <li>Your external IdP is configured.</li>
          <li>
            Your administrators are able to log in to ZIdentity and view the assigned Zscaler
            service tiles.
          </li>
        </ul>
        <p> You can revert this action within the next 24 hours. Do you want to continue?</p>
      </Card>
    );
  };

  return (
    <div className="migration-provisioned-container">
      <p>
        <FontAwesomeIcon
          icon={faExclamationTriangle}
          style={{ color: '#cc9200', fontSize: '16px' }}
        />{' '}
        Complete Migration:
        <span className="strong"> ZIdentity is provisioned.</span> Next Stage: ZIdentity for
        Administrators.{' '}
        <span className="link" onClick={onMigrateClick} onKeyDown={noop}>
          Click here to migrate now.
        </span>
      </p>

      {showMigrateAdminModal && (
        <Modal
          show={showMigrateAdminModal}
          onEscape={onCloseClick}
          containerClass="migrate-admin-modal-container"
        >
          <ModalHeader text="COMPLETE_MIGRATION" onClose={onCloseClick} />
          <ModalBody>{renderMigrationForm()}</ModalBody>
          <ModalFooter
            saveText={extIdpAuthnSuccess ? 'CONFIRM' : 'CLOSE'}
            showCancel={extIdpAuthnSuccess}
            onSave={extIdpAuthnSuccess ? onSaveClick : onCloseClick}
            onCancel={onCloseClick}
            cancelText="CLOSE"
          />
        </Modal>
      )}

      {showUpdateStatusWarning && (
        <Modal
          show={showUpdateStatusWarning}
          onEscape={onOverrideAuditorsCloseClick}
          containerClass="update-status-warning-container"
        >
          <ModalHeader
            text="IDP_AUTHENTICATION_NOT_SUCCESSFUL"
            onClose={onOverrideAuditorsCloseClick}
          />
          <ModalBody>{warningSummary}</ModalBody>
          <ModalFooter
            cancelText="CLOSE"
            onSave={onOverrideAuditorsSaveClick}
            onCancel={onOverrideAuditorsCloseClick}
          />
        </Modal>
      )}
    </div>
  );
};

MigrationProvisioned.propTypes = {
  status: PropTypes.object,
  extIdpAuthnSuccess: PropTypes.bool,
};

export default MigrationProvisioned;
