import { useState } from 'react';

import { faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { CRUDModal, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { updateMigrationStatus } from '../../ducks/migration';

const modalModeDetail = {
  delete: {
    headerText: 'REVERT_TO_PROVISIONED',
  },
};

const defaultProps = { status: {} };

const MigrationAdminLinked = ({ status }) => {
  const { apiCall } = useApiCall();

  const [modalMode, setModalMode] = useState('');

  const onMigrateClick = () => {
    setModalMode('delete');
  };

  const onSaveClick = () => {
    apiCall(updateMigrationStatus(status.nextStatus)).catch(noop);
  };

  const onCloseClick = () => {
    setModalMode('');
  };

  return (
    <div className="migration-provisioned-container">
      <p>
        <FontAwesomeIcon icon={faExclamationTriangle} style={{ color: '#cc9200' }} /> ZIdentity
        Migration: ZIdentity for Administrators{' '}
        {status.showRevertOption && (
          <span className="link" onClick={onMigrateClick} onKeyDown={noop}>
            Revert to ZIdentity Provisioned
          </span>
        )}
      </p>

      {modalMode && (
        <CRUDModal
          mode={modalMode}
          saveText={'REVERT'}
          onSaveClick={onSaveClick}
          onCloseClick={onCloseClick}
          {...modalModeDetail[modalMode]}
        />
      )}
    </div>
  );
};

MigrationAdminLinked.defaultProps = defaultProps;

MigrationAdminLinked.propTypes = {
  status: PropTypes.object,
};

export default MigrationAdminLinked;
