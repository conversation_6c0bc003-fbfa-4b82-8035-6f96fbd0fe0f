import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  CRUDModal,
  FileBrowserForm,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList as getDomainsList, getIdpDomains } from '../../ducks/tenant-domains';
import {
  selectIDPDomains,
  selectTableDetail as selectTenantDomainsTableDetail,
} from '../../ducks/tenant-domains/selectors';
import { selectTableDetail as selectAttributeTableDetail } from '../../ducks/user-attributes/selectors';
import {
  add,
  bulkActivate,
  bulkPasswordReset,
  bulkRemove,
  downloadTemplateCSV,
  importFromCsvPolling,
  remove,
  update,
} from '../../ducks/users';

import { PERMISSIONS_KEY } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import UserForm from './UserForm';
import TABS from './constants';
import {
  getCustomAttributeApiPayload,
  getFormTooltipDetail,
  modalModeDetail,
  securitySettingsInfo,
} from './helper';

const UserCRUD = () => {
  const { apiCall } = useApiCall();

  const [selectedTab, setSelectedTab] = useState(TABS.GENERAL);

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    selectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
    isFormReadOnly,
  } = useContext(CRUDPageContext);

  const userCredentialsPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY),
  );

  const { hasFullAccess } = privileges;

  const idpDomains = useSelector(selectIDPDomains);
  const { data: attributeList = [] } = useSelector(selectAttributeTableDetail);
  const { data: tenantDomainList } = useSelector(selectTenantDomainsTableDetail);

  const customAttributeList = attributeList.filter((detail) => !detail.systemDefined);

  useEffect(() => {
    apiCall(getIdpDomains()).catch(noop);
  }, []);

  useEffect(() => {
    if (modalMode) {
      apiCall(
        getDomainsList({
          requireTotal: true,
          requirePseudoDomain: true,
          all: true,
          requireInternalDomain: modalMode === 'edit',
        }),
      ).catch(noop);
    }
  }, [modalMode]);

  useEffect(() => {
    if (
      !hasFullAccess &&
      modalMode === 'view' &&
      userCredentialsPrivileges?.hasFullAccess &&
      privileges.hasViewAccess
    ) {
      setSelectedTab(TABS.SECURITY);
    }
  }, [userCredentialsPrivileges, privileges, modalMode]);

  const onCloseClick = () => {
    setSelectedTab(TABS.GENERAL);
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setCsvImportDetail(defaultCsvImportDetail);
    setCsvImportResult(defaultCsvImportResultDetail);
    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      const payload = {
        ...detail,
        customAttrsInfo: getCustomAttributeApiPayload({
          customAttrsInfo: detail.customAttrsInfo,
          attributeList: customAttributeList,
        }),
      };

      const { isSecurityVisible } = securitySettingsInfo({
        loginName: detail.loginName,
        idpDomains,
        tenantDomainList,
        isHostedIdpUser: false,
      });

      if (!isSecurityVisible) {
        delete payload.pwdConfig;
        delete payload.password;
      }

      apiCall(add(payload), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      const payload = {
        ...detail,
        customAttrsInfo: getCustomAttributeApiPayload({
          customAttrsInfo: { ...detail.customAttrsInfo },
          attributeList: customAttributeList,
        }),
      };

      const { isSecurityVisible } = securitySettingsInfo({
        loginName: detail.loginName,
        idpDomains,
        tenantDomainList,
        isHostedIdpUser: detail?.hostedIdp,
      });

      if (!isSecurityVisible) {
        delete payload.pwdConfig;
        delete payload.password;
      }

      apiCall(update(payload), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'view') {
      const payload = {
        ...detail,
        customAttrsInfo: getCustomAttributeApiPayload({
          customAttrsInfo: { ...detail.customAttrsInfo },
          attributeList: customAttributeList,
        }),
      };

      const { isSecurityVisible } = securitySettingsInfo({
        loginName: detail.loginName,
        idpDomains,
        tenantDomainList,
        isHostedIdpUser: detail?.hostedIdp,
      });

      if (!isSecurityVisible) {
        delete payload.pwdConfig;
        delete payload.password;
      }

      apiCall(update(payload), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(remove(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (
      modalMode === 'bulkActivate' ||
      modalMode === 'bulkDeactivate' ||
      modalMode === 'bulkDelete'
    ) {
      onBulkActionSelection();
    }

    if (modalMode === 'importFile') {
      apiCall(importFromCsvPolling(csvImportDetail))
        .then((response = {}) => {
          setCsvImportResult({ ...response });
        })
        .catch(noop);
    }

    if (modalMode === 'actionConfirmation') {
      onBulkResetPassword();
    }
  };

  const onBulkActionSelection = () => {
    const { value } = selectedBulkAction;

    const ids = selectedRowDetail.map(({ id }) => id);

    if (value === 'ACTIVATE' || value === 'DE_ACTIVATE') {
      const enable = value === 'ACTIVATE';

      apiCall(bulkActivate({ ids, enable }), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .catch(noop)
        .finally(onCloseClick);
    }

    if (value === 'DELETE') {
      apiCall(bulkRemove({ ids }), {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          message: 'ITEMS_HAVE_BEEN_DELETED',
        },
      })
        .catch(noop)
        .finally(onCloseClick);
    }
  };

  const onBulkResetPassword = () => {
    const ids = selectedRowDetail.map(({ id }) => id);

    apiCall(bulkPasswordReset({ ids })).catch(noop).finally(onCloseClick);
  };

  const onDownloadClick = async () => {
    return await apiCall(downloadTemplateCSV()).catch(noop);
  };

  const isImportResultValid = !!csvImportResult;

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => {
        return modalMode === 'importFile' ? (
          <FileBrowserForm
            onDetailChange={setCsvImportDetail}
            detail={csvImportDetail}
            onDownloadClick={onDownloadClick}
            overrideProps={{ tooltip: getFormTooltipDetail('overrideExistingEntries') }}
            tooltip={getFormTooltipDetail('csvFile')}
            result={csvImportResult}
            {...props}
          />
        ) : (
          <UserForm {...props} selectedTab={selectedTab} setSelectedTab={setSelectedTab} />
        );
      }}
      containerClass={
        modalMode === 'add' || modalMode === 'edit'
          ? `user-form ${selectedTab === TABS.ENTITLEMENTS ? 'entitlement-section' : ''}`
          : ''
      }
      saveText={
        modalMode === 'actionConfirmation'
          ? 'RESET'
          : modalMode === 'view' &&
              userCredentialsPrivileges?.hasFullAccess &&
              privileges.hasViewAccess
            ? 'UPDATE'
            : ''
      }
      showSave={
        !isImportResultValid &&
        (hasFullAccess ||
          (userCredentialsPrivileges?.hasFullAccess &&
            privileges.hasViewAccess &&
            selectedTab === TABS.SECURITY))
      }
      cancelText={isFormReadOnly || isImportResultValid ? 'CLOSE' : 'CANCEL'}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default UserCRUD;
