import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON>ton, Card, FieldGroup, getOnChangeValue } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';
import AttributeInput from '../attribute/AttributeInput';
import AddUserAttribute from './AddUserAttribute';

const defaultProps = {
  customAttributeList: [],
  setFormValues: noop,
  attributeFormFileds: {},
  setAttributeFormFields: noop,
  validationDetail: {},
  privileges: {
    hasFullAccess: false,
    noAccess: false,
  },
  isFormReadOnly: false,
};

const AttributeSection = ({
  customAttributeList,
  setFormValues,
  attributeFormFileds,
  setAttributeFormFields,
  validationDetail,
  privileges,
  isFormReadOnly,
}) => {
  const { t } = useTranslation();

  const { hasFullAccess } = privileges || {};

  const [showModal, setShowModal] = useState('');

  const onAttributeFormFieldChange = (evt) => {
    const updatedValue = getOnChangeValue(evt.target);

    for (const attrKey in updatedValue) {
      const attrValue = updatedValue[attrKey];

      if (attrValue === '') {
        setFormValues((prevState) => {
          const customAttrsInfo = { ...prevState.customAttrsInfo };

          delete customAttrsInfo[attrKey];

          return {
            ...prevState,
            customAttrsInfo: { ...customAttrsInfo },
          };
        });
      } else {
        setFormValues((prevState) => ({
          ...prevState,
          customAttrsInfo: { ...prevState.customAttrsInfo, ...updatedValue },
        }));
      }
    }

    setAttributeFormFields((prevState) => ({ ...prevState, ...updatedValue }));
  };

  const hasAttributes = customAttributeList.length > 0;

  const onAddClick = () => {
    setShowModal('add');
  };

  return (
    <>
      <section className="typography-paragraph1-uppercase">{t('INFORMATION')}</section>

      <div className="user-form-attribute-section">
        {hasAttributes && (
          <Card containerClass="attributes">
            <FieldGroup containerClass="has-jc-sb" containerStyle={{ flexWrap: 'wrap' }}>
              {customAttributeList.map((detail) => (
                <AttributeInput
                  key={detail.attrName}
                  detail={detail}
                  onChange={onAttributeFormFieldChange}
                  value={attributeFormFileds[detail.attrName]}
                  info={isFormReadOnly ? {} : validationDetail}
                  containerStyle={{ minWidth: '235px', maxWidth: '240px', marginRight: '30px' }}
                  disabled={!hasFullAccess}
                />
              ))}
            </FieldGroup>
          </Card>
        )}

        {hasFullAccess && (
          <Card containerClass="new-attributes-cta">
            <FieldGroup
              containerClass={`${hasAttributes ? 'has-jc-e' : 'has-jc-c'}`}
              containerStyle={{ paddingTop: !hasAttributes ? '20px' : '', marginTop: '20px' }}
            >
              {
                <Button type="secondary" onClick={onAddClick}>
                  {t('CREATE_NEW_ATTRIBUTES')}
                </Button>
              }
            </FieldGroup>
          </Card>
        )}
      </div>

      <CRUDPageContextProvider privileges={privileges}>
        <AddUserAttribute showModal={showModal} setShowModal={setShowModal} />
      </CRUDPageContextProvider>
    </>
  );
};

AttributeSection.defaultProps = defaultProps;

AttributeSection.propTypes = {
  customAttributeList: PropTypes.array,
  setFormValues: PropTypes.func,
  attributeFormFileds: PropTypes.object,
  setAttributeFormFields: PropTypes.func,
  validationDetail: PropTypes.object,
  privileges: PropTypes.object,
  isFormReadOnly: PropTypes.bool,
};

export default AttributeSection;
