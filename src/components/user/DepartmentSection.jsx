import { useContext, useState } from 'react';
import { useSelector } from 'react-redux';

import { faPlusCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  DropDown,
  Field,
  FieldGroup,
  Label,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { getList } from '../../ducks/departments';
import { selectDepartmentList, selectTableDetail } from '../../ducks/departments/selectors';

import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';
import AddDepartment from './AddDepartment';
import { UserFormContext } from './UserForm';
import { getFormTooltipDetail } from './helper';

const DepartmentSection = () => {
  const [showModal, setShowModal] = useState('');

  const departmentListTableDetail = useSelector(selectTableDetail);
  const departmentList = useSelector(selectDepartmentList);

  const { setFormValues, validationDetail, isFormReadOnly, detail, privileges } =
    useContext(UserFormContext);

  const [selectedDepartementList, setSelectedDepartmentList] = useState(() => {
    const { id, name } = detail.department || {};

    if (id) {
      return [{ value: id, label: name }];
    } else {
      return [];
    }
  });

  const onDepartmentSelection = (detail) => {
    const { label: name, value: id } = detail?.[0] || {};

    setFormValues((prevState) => ({
      ...prevState,
      department: { id, name },
    }));

    setSelectedDepartmentList(detail);
  };

  const { isDropDownLoading, onDropDownOpen, onLoadMoreClick } = useDropDownActions({
    detail: departmentListTableDetail,
    apiCallFunc: getList,
  });

  const onAddClick = () => {
    setShowModal('add');
  };

  return (
    <>
      <FieldGroup>
        <Field
          label="DEPARTMENT"
          tooltip={isFormReadOnly ? {} : getFormTooltipDetail('department')}
          htmlFor="department"
          info={validationDetail}
          containerClass="no-m-r"
        >
          <DropDown
            list={departmentList}
            selectedList={selectedDepartementList}
            onSelection={onDepartmentSelection}
            onOpen={onDropDownOpen}
            loadMoreDetail={{ ...departmentListTableDetail, onLoadMoreClick }}
            loading={isDropDownLoading}
            disabled={isFormReadOnly}
            toggleSelected
            containerClass="full-width"
          />
        </Field>

        <Label
          htmlFor="add-department"
          tooltip={{
            content: isFormReadOnly ? '' : 'Add Department',
            placement: 'top',
            shiftConfig: { padding: 5 },
          }}
          containerClass="has-as-c no-m-b"
          containerStyle={{ marginTop: '16px' }}
        >
          <Button
            type="tertiary"
            isLarge
            containerClass="content-width"
            onClick={onAddClick}
            disabled={isFormReadOnly}
          >
            <FontAwesomeIcon icon={faPlusCircle} className="icon" />
          </Button>
        </Label>
      </FieldGroup>

      <CRUDPageContextProvider privileges={privileges}>
        <AddDepartment showModal={showModal} setShowModal={setShowModal} />
      </CRUDPageContextProvider>
    </>
  );
};

export default DepartmentSection;
