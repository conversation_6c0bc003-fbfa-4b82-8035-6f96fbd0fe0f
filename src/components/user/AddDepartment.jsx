import { useContext, useEffect } from 'react';

import { CRUDModal, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { add } from '../../ducks/departments';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import DepartmentForm from '../department/DepartmentForm';
import { modalModeDetail } from '../department/helper';

const defaultProps = {
  showModal: '',
  setShowModal: noop,
};

const AddDepartment = ({ showModal, setShowModal }) => {
  const { apiCall } = useApiCall();

  const { modalMode, setModalMode, detail, setDetail, defaultModalMode, defaultDetail } =
    useContext(CRUDPageContext);

  useEffect(() => {
    setModalMode('add');
  }, [showModal]);

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(add(detail)).then(onCloseClick).catch(noop);
    }
  };

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
    setShowModal('');
  };

  if (!showModal) {
    return null;
  }

  return (
    <CRUDModal
      mode={modalMode}
      renderFormSection={(props) => <DepartmentForm {...props} />}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

AddDepartment.defaultProps = defaultProps;

AddDepartment.propTypes = {
  showModal: PropTypes.string,
  setShowModal: PropTypes.func,
};

export default AddDepartment;
