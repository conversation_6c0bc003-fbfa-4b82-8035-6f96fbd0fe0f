import { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  RowNumber,
  Selector,
  StatusTag,
  TableContainer,
  TextWithTooltip,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { selectMyProfileDetail } from '../../ducks/profile/selectors';
import { bulkActivate, getList } from '../../ducks/users';
import { selectTableConfig, selectTableDetail } from '../../ducks/users/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import { defaultBulkActionOption } from './helper';

const tableShowMoreList = [
  { label: 'ACTIVATE', value: 'ACTIVATE' },
  { label: 'DE_ACTIVATE', value: 'DE_ACTIVATE' },
];

const UserTable = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    setDetail,
    setSelectedRowDetail,
    setSelectedBulkAction,
    searchTerm,
    selectedSearchField,
    isFormReadOnly,
  } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const profileDetail = useSelector(selectMyProfileDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'number') {
        columnDetail.cell = RowNumber;
      }

      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { id } = props?.row?.original || {};

          const showDelete = id !== profileDetail?.id;

          return (
            <Actions
              {...props}
              editIcon={isFormReadOnly ? faEye : faPencilAlt}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              showDelete={showDelete && !isFormReadOnly}
              showMore={!isFormReadOnly}
              list={tableShowMoreList}
              onActionClick={onShowMoreClick}
            />
          );
        };

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { status } = props?.row?.original || {};

          return (
            <StatusTag
              value={status}
              truthyLabel="ACTIVE"
              falsyLabel="INACTIVE"
              type="ENABLED_DISABLED"
            />
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (columnDetail.id === 'loginName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { loginName, guest } = props?.row?.original || {};

          return (
            <>
              <TextWithTooltip containerClass="login-name-container"> {loginName} </TextWithTooltip>
              {guest && (
                <div className="guest-annotation-container">
                  <div className="guest-annotaion">{t('GUEST_USER')}</div>
                </div>
              )}
            </>
          );
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'displayName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { displayName } = props?.row?.original || {};

          const derivedName = displayName || '--';

          return <TextWithTooltip> {derivedName} </TextWithTooltip>;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    if (isFormReadOnly) {
      return [...(tableConfig?.columns || [])];
    }

    return [
      {
        id: 'selection',
        Header: '',
        cell: (props) => <Selector {...props} />,
        size: 50,
        minSize: 50,
        maxSize: 50,
        enableResizing: false,
        disableSortBy: true,
        defaultCanSort: false,
      },
      ...(tableConfig?.columns || []),
    ];
  }, [tableConfig?.columns, profileDetail, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode(isFormReadOnly ? 'view' : 'edit');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onShowMoreClick = (detail, payload = {}) => {
    const id = payload?.row?.original?.id;

    const { setIsOpen = noop } = payload;

    if (detail && id) {
      apiCall(bulkActivate({ ids: [id], enable: detail === 'ACTIVATE' }), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .catch(noop)
        .finally(() => {
          setIsOpen?.(false);
        });
    }
  };

  const getSearchField = () => {
    const { value } = selectedSearchField || {};

    let searchField = '';

    if (value === 'NAME') {
      searchField = 'name';
    }

    if (value === 'GROUP') {
      searchField = 'groupname';
    }

    return searchField;
  };

  const onRowSelection = (data) => {
    setSelectedRowDetail(data);

    setSelectedBulkAction(defaultBulkActionOption);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const searchField = getSearchField();

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchField) {
      payload[searchField] = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      onRowSelection={onRowSelection}
    />
  );
};

export default UserTable;
