import { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Button,
  Card,
  Checkbox,
  DropDown,
  Field,
  FieldGroup,
  PasswordInput,
  Tab,
  Tabs,
  Toast,
  ToggleButton,
  defaultFormPropTypes,
  defaultFormProps,
  getOnChangeValue,
  useApiCall,
} from '@zscaler/zui-component-library';

import { filter, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { getPasswordPolicy } from '../../ducks/password-policy';
import { selectActiveConfigDetail } from '../../ducks/password-policy/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import {
  selectIDPDomains,
  selectTableDetail as selectTenantDomainsTableDetail,
} from '../../ducks/tenant-domains/selectors';
import { getList as getAttributeList } from '../../ducks/user-attributes';
import { selectTableDetail as selectAttributeTableDetail } from '../../ducks/user-attributes/selectors';
import {
  getAutoGeneratedPassword,
  getUserEntitlements,
  removePasswordForHostedIdpUser,
} from '../../ducks/users';
import { selectAutoGenPass } from '../../ducks/users/selectors';

import { PERMISSIONS_KEY } from '../../config';
import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import AttributeSection from './AttributeSection';
import EntitlementSection from './EntitlementSection';
import GeneralSection from './GeneralSection';
import MultifactorBypassSection from './MultifactorBypassSection';
import MultifactorSection from './MultifactorSection';
import TABS from './constants';
import {
  getCustomAttributeValidationDetail,
  getCustomFormValidationDetail,
  getFormTooltipDetail,
  getFormValidationDetail,
  getSecuritySettingsValidationDetail,
  securitySettingsInfo,
} from './helper';

const defaultProps = {
  ...defaultFormProps,
  privileges: {
    hasFullAccess: false,
    noAccess: false,
  },
};

export const UserFormContext = createContext({});

const UserForm = ({ validationDetail, setValidationDetail, mode, selectedTab, setSelectedTab }) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { detail, setDetail, isFormReadOnly, privileges } = useContext(CRUDPageContext);

  const adminEntitlementPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY),
  );

  const serviceEntitlementPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY),
  );

  const userCredentialsPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY),
  );

  const userAndGroupPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY),
  );

  const idpDomains = useSelector(selectIDPDomains);
  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  const [isDisplayNameDirty, setIsDisplayNameDirty] = useState(false);

  const { data: attributeList = [] } = useSelector(selectAttributeTableDetail);

  const customAttributeList = attributeList.filter((detail) => !detail.systemDefined);

  const { data: tenantDomainList } = useSelector(selectTenantDomainsTableDetail);
  const activeConfigDetail = useSelector(selectActiveConfigDetail);

  const autoGenPass = useSelector(selectAutoGenPass);

  const isEditMode = mode === 'edit';

  const isHostedIdpUser = isEditMode && detail?.hostedIdp;

  let passwordOptions = [{ label: 'SET_BY_USER', value: 'SET_BY_USER' }];
  if (activeConfigDetail.allowAdminSetPasswords) {
    passwordOptions = [
      { label: 'SET_BY_USER', value: 'SET_BY_USER' },
      { label: 'SET_BY_ADMIN', value: 'SET_BY_ADMIN' },
      { label: 'AUTO_GENERATED', value: 'AUTO_GENERATED' },
    ];
  }

  if (isHostedIdpUser) {
    passwordOptions = [
      { label: 'SET_BY_ADMIN', value: 'SET_BY_ADMIN' },
      { label: 'AUTO_GENERATED', value: 'AUTO_GENERATED' },
    ];
  }

  const [showSecuritySettings, setShowSecuritySettings] = useState(!(isEditMode || isFormReadOnly));
  const [isPrimaryEmailSameAsUserId, setIsPrimaryEmailSameAsUserId] = useState(
    detail?.loginName && detail?.loginName === detail?.primaryEmail,
  );
  const [selectedPasswordOption, setSelectedPasswordOption] = useState(passwordOptions[0]);

  const [formValues, setFormValues] = useState({
    loginName: '',
    displayName: '',
    firstName: '',
    lastName: '',
    primaryEmail: '',
    secondaryEmail: '',
    password: '',
    confirmPassword: '',
    customAttrsInfo: {},
    department: {},
    pwdConfig: { resetOnLogin: true, setByUser: false },
    status: true,
    skipMfaUntil: 0,
    ...detail,
  });

  const [attributeFormFileds, setAttributeFormFields] = useState(formValues.customAttrsInfo);

  const [passwordPolicyValidation, setPasswordPolicyValidation] = useState({
    validationDetail: {},
    isValid: false,
  });

  const { isArbitraryDomainConfigured, isSecurityVisible, isEmailType } = securitySettingsInfo({
    loginName: formValues.loginName,
    idpDomains,
    tenantDomainList,
    isHostedIdpUser,
  });

  const onFormFieldChange = (evt) => {
    setFormValues((prevState) => {
      const { name, value } = evt.target;

      const updatedValue = getOnChangeValue(evt.target);

      if (name === 'displayName') {
        if (
          value?.trim() === '' &&
          prevState.firstName?.trim() === '' &&
          prevState.lastName?.trim() === ''
        ) {
          setIsDisplayNameDirty(false);
        } else {
          setIsDisplayNameDirty(true);
        }
      }

      if (name === 'firstName') {
        if (!isDisplayNameDirty) {
          updatedValue['displayName'] = value?.trim() + ' ' + prevState.lastName?.trim();

          updatedValue['displayName'] = updatedValue['displayName']?.trim();
        } else if (
          value?.trim() === '' &&
          prevState.lastName?.trim() === '' &&
          prevState.displayName?.trim() === ''
        ) {
          setIsDisplayNameDirty(false);
        }
      }

      if (name === 'lastName') {
        if (!isDisplayNameDirty) {
          updatedValue['displayName'] = prevState.firstName?.trim() + ' ' + value?.trim();

          updatedValue['displayName'] = updatedValue['displayName']?.trim();
        } else if (
          value?.trim() === '' &&
          prevState.firstName?.trim() === '' &&
          prevState.displayName?.trim() === ''
        ) {
          setIsDisplayNameDirty(false);
        }
      }

      if (name === 'loginName' && isPrimaryEmailSameAsUserId) {
        updatedValue['primaryEmail'] = updatedValue[name];
      }

      if (
        updatedValue[name] &&
        ((name === 'loginName' && prevState['primaryEmail'] === updatedValue[name]) ||
          (name === 'primaryEmail' && prevState['loginName'] === updatedValue[name]))
      ) {
        setIsPrimaryEmailSameAsUserId(true);
      }

      const newState = { ...prevState, ...updatedValue };

      return newState;
    });
  };

  useEffect(() => {
    setSelectedPasswordOption(passwordOptions[0]);

    if (!activeConfigDetail.allowAdminSetPasswords) {
      setFormValues((prevState) => {
        const pwdConfig = { resetOnLogin: false, setByUser: true };

        return { ...prevState, pwdConfig };
      });
    }
  }, [activeConfigDetail.allowAdminSetPasswords]);

  useEffect(() => {
    const { displayName, firstName, lastName } = formValues;
    if (
      displayName.trim() !== '' &&
      displayName.trim() !== firstName.trim() + ' ' + lastName.trim()
    ) {
      setIsDisplayNameDirty(true);
    }
  }, []);

  useEffect(() => {
    if (detail?.id) {
      apiCall(getUserEntitlements(detail?.id)).catch(noop);
    }
  }, [detail?.id]);

  useEffect(() => {
    apiCall(getAttributeList({ requireTotal: true })).catch(noop);

    apiCall(getPasswordPolicy()).catch(noop);
  }, []);

  useEffect(() => {
    setDetail(formValues);

    const domainList = filter(tenantDomainList, { internalDomain: false }).map(
      ({ objectName }) => objectName,
    );

    let formValidationDetail = getFormValidationDetail({
      formValues,
      attributeList,
      domainList,
      allowAdminSetPasswords: activeConfigDetail.allowAdminSetPasswords,
      selectedPasswordOption,
      passwordPolicyValidation,
      isArbitraryDomainConfigured,
      isSecurityVisible,
    });

    if (mode === 'view' && userCredentialsPrivileges?.hasFullAccess && privileges.hasViewAccess) {
      formValidationDetail = getCustomFormValidationDetail({
        formValues,
        attributeList,
        domainList,
        allowAdminSetPasswords: activeConfigDetail.allowAdminSetPasswords,
        selectedPasswordOption,
        passwordPolicyValidation,
        isArbitraryDomainConfigured,
        isSecurityVisible,
      });
    }

    setValidationDetail(formValidationDetail);
  }, [
    formValues,
    tenantDomainList,
    attributeList,
    activeConfigDetail.allowAdminSetPasswords,
    selectedPasswordOption,
    showSecuritySettings,
    passwordPolicyValidation,
    isArbitraryDomainConfigured,
    isSecurityVisible,
    selectedTab,
  ]);

  useEffect(() => {
    if (showSecuritySettings) {
      setFormValues((prevState) => {
        const newState = { ...prevState, password: '', confirmPassword: '' };
        return newState;
      });

      onPasswordSelection([selectedPasswordOption]);
    } else {
      setFormValues((prevState) => {
        const newState = { ...prevState, password: '', confirmPassword: '' };
        delete newState.pwdConfig;
        return newState;
      });
    }
  }, [showSecuritySettings, selectedPasswordOption]);

  useEffect(() => {
    const { value } = selectedPasswordOption;

    if (value === 'AUTO_GENERATED') {
      setFormValues((prevState) => ({
        ...prevState,
        password: autoGenPass,
        pwdConfig: { resetOnLogin: true, ...prevState.pwdConfig, setByUser: false },
      }));
    }
  }, [autoGenPass]);

  const onPasswordSelection = (payload) => {
    const { value } = payload?.[0] || {};

    setSelectedPasswordOption(payload[0]);

    if (value === 'AUTO_GENERATED') {
      apiCall(getAutoGeneratedPassword()).catch(() => {
        setFormValues((prevState) => {
          const newState = { ...prevState };

          newState.password = '';
          newState.confirmPassword = '';

          return newState;
        });
      });
    } else {
      setFormValues((prevState) => {
        const isSetByUser = value === 'SET_BY_USER';

        const newState = { ...prevState };

        if (isSetByUser) {
          delete newState.password;
          newState.pwdConfig = { setByUser: isSetByUser };
        } else {
          newState.pwdConfig = { resetOnLogin: true, setByUser: isSetByUser };
        }

        newState.password = '';
        newState.confirmPassword = '';

        return newState;
      });
    }
  };

  const onResetOnLoginClick = (evt) => {
    const { checked } = evt.target;

    setFormValues((prevState) => ({
      ...prevState,
      pwdConfig: { ...prevState.pwdConfig, resetOnLogin: checked },
    }));
  };

  const onTogglePasswordClick = () => {
    setShowSecuritySettings((prevState) => !prevState);
  };

  const renderSecuritySettingsSection = () => {
    const isPasswordVisible = selectedPasswordOption.value !== 'SET_BY_USER';

    const isCheckBoxDisabled = selectedPasswordOption.value === 'SET_BY_USER';

    const isConfirmPasswordVisible = selectedPasswordOption.value === 'SET_BY_ADMIN';

    const canCopy = selectedPasswordOption.value === 'AUTO_GENERATED';

    const passwordInputConfig = (
      <PasswordInput
        containerStyle={isHostedIdpUser ? { maxWidth: '50%' } : {}}
        label="PASSWORD_LABEL"
        name="password"
        type="password"
        onChange={onFormFieldChange}
        value={formValues.password}
        canCopy={canCopy}
        showPasswordValidation
        onValidationChange={setPasswordPolicyValidation}
        passwordConfig={activeConfigDetail}
        info={validationDetail}
        tooltip={
          isFormReadOnly ? {} : getFormTooltipDetail(canCopy ? 'canCopyPassword' : 'password')
        }
      />
    );

    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('SECURITY_SETTINGS')}</section>
        <Card>
          {isHostedIdpUser && (
            <Toast type="warning" autoHide={false}>
              <span>
                {
                  'This user record is configured to authenticate at an external IDP and has a password registered here in ZIdentity. This configuration is not recommeneded.'
                }
                <Button
                  type="tertiary"
                  onClick={() => {
                    apiCall(removePasswordForHostedIdpUser({ id: formValues?.id })).catch(noop);
                  }}
                  containerClass="no-p-l has-as-e"
                  containerStyle={{ display: 'inline' }}
                >
                  <span>{t('REMOVE_PASSWORD')}</span>
                </Button>
                {'for the appropritate configuration.'}
              </span>
            </Toast>
          )}
          {(isEditMode || isFormReadOnly) && (
            <Field label="CHANGE_PASSWORD_SETTINGS">
              <ToggleButton
                isOn={showSecuritySettings}
                onToggleClick={onTogglePasswordClick}
                showLabel={false}
                disabled={!userCredentialsPrivileges?.hasFullAccess}
              />
            </Field>
          )}

          {showSecuritySettings ? (
            <>
              <FieldGroup>
                <Field
                  label="PASSWORD_OPTION"
                  tooltip={isFormReadOnly ? {} : getFormTooltipDetail('passwordOption')}
                >
                  <DropDown
                    list={passwordOptions}
                    selectedList={[selectedPasswordOption]}
                    onSelection={onPasswordSelection}
                    disabled={
                      mode === 'view' ? !userCredentialsPrivileges?.hasFullAccess : isFormReadOnly
                    }
                  />
                </Field>
                {isPasswordVisible && !isHostedIdpUser ? passwordInputConfig : null}
              </FieldGroup>

              <FieldGroup>
                {!isCheckBoxDisabled && !isHostedIdpUser && (
                  <Field
                    label="PROMPT_PASSWORD_FIRST_LOGIN"
                    tooltip={isFormReadOnly ? {} : getFormTooltipDetail('promptPasswordFirstLogin')}
                  >
                    <Checkbox
                      name="resetOnLogin"
                      checked={formValues?.pwdConfig?.resetOnLogin}
                      disabled={isCheckBoxDisabled || activeConfigDetail.forcePasswordChange}
                      onChange={onResetOnLoginClick}
                    />
                  </Field>
                )}

                {isPasswordVisible && isHostedIdpUser && passwordInputConfig}
                {isConfirmPasswordVisible ? (
                  <PasswordInput
                    label="CONFIRM_PASSWORD"
                    name="confirmPassword"
                    type="password"
                    onChange={onFormFieldChange}
                    value={formValues.confirmPassword}
                    info={validationDetail}
                    tooltip={isFormReadOnly ? {} : getFormTooltipDetail('confirmPassword')}
                  />
                ) : null}
              </FieldGroup>
            </>
          ) : null}
        </Card>
      </>
    );
  };

  const renderNoSecuritySettingsAccessSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('SECURITY_SETTINGS')}</section>
        <Card>
          <Field label="CHANGE_PASSWORD_SETTINGS">
            {isEmailType ? (
              <ToggleButton isOn={false} offLabel="Only Email OTP is supported for user" />
            ) : (
              <ToggleButton isOn={false} offLabel="Password setting is managed by IDP" />
            )}
          </Field>
        </Card>
      </>
    );
  };

  const renderSecuritySection = () => {
    if (selectedTab === TABS.SECURITY) {
      return (
        <>
          {isSecurityVisible
            ? renderSecuritySettingsSection()
            : renderNoSecuritySettingsAccessSection()}

          <MultifactorBypassSection />

          {(isEditMode || isFormReadOnly) && (
            <MultifactorSection
              userDetail={detail}
              isEditMode={isEditMode}
              isFormReadOnly={isFormReadOnly}
            />
          )}
        </>
      );
    }

    return null;
  };

  const renderAttributesSection = () => {
    if (selectedTab === TABS.ATTRIBUTES) {
      return (
        <AttributeSection
          customAttributeList={customAttributeList}
          setFormValues={setFormValues}
          attributeFormFileds={attributeFormFileds}
          setAttributeFormFields={setAttributeFormFields}
          validationDetail={validationDetail}
          privileges={privileges}
          isFormReadOnly={isFormReadOnly}
        />
      );
    }

    return null;
  };

  const renderGeneralSection = () => {
    if (selectedTab === TABS.GENERAL) {
      return <GeneralSection />;
    }

    return null;
  };

  const getGeneralTabClassName = () => {
    // skip if password is invalid as it is on different tab
    if (
      (validationDetail.context === 'password' || validationDetail.context === 'confirmPassword') &&
      !validationDetail.isValid
    ) {
      return '';
    } else {
      return validationDetail.isValid ? '' : 'has-color-error';
    }
  };

  const getAdditionalAttributeClassName = () => {
    const attributeValidationDetail = getCustomAttributeValidationDetail({
      customAttrsInfo: formValues.customAttrsInfo,
      attributeList,
    });

    if (
      !validationDetail.isValid &&
      validationDetail.context === attributeValidationDetail.context
    ) {
      return attributeValidationDetail.isValid ? '' : 'has-color-error';
    }

    return '';
  };

  const renderEntitlements = () => {
    if (selectedTab === TABS.ENTITLEMENTS) {
      return <EntitlementSection />;
    }
    return null;
  };

  const getSecurityTabClassName = () => {
    const securityValidationDetail = getSecuritySettingsValidationDetail({
      formValues,
      allowAdminSetPasswords: activeConfigDetail.allowAdminSetPasswords,
      selectedPasswordOption,
      passwordPolicyValidation,
    });

    if (!securityValidationDetail.isValid && isSecurityVisible) {
      return securityValidationDetail.isValid ? '' : 'has-color-error';
    }

    return '';
  };

  const canShowSecurityTab = () => {
    if (isEditMode || isFormReadOnly) {
      return !userCredentialsPrivileges.noAccess;
    }

    return !userAndGroupPrivileges.noAccess;
  };

  const canShowEntitlementsTab = () => {
    if (isEditMode || isFormReadOnly) {
      return (
        !adminEntitlementPrivileges.noAccess ||
        (isUserSSOEnabled && !serviceEntitlementPrivileges.noAccess)
      );
    }
    return false;
  };

  const contextValue = {
    onFormFieldChange,
    formValues,
    setFormValues,
    validationDetail,
    setValidationDetail,
    isPrimaryEmailSameAsUserId,
    setIsPrimaryEmailSameAsUserId,
    detail,
    setDetail,
    isFormReadOnly,
    privileges,
  };

  return (
    <UserFormContext.Provider value={contextValue}>
      <Tabs>
        <Tab
          label={TABS.GENERAL}
          isActive={selectedTab === TABS.GENERAL}
          containerClass={getGeneralTabClassName()}
          onClick={() => {
            setSelectedTab(TABS.GENERAL);
          }}
        />
        {canShowEntitlementsTab() ? (
          <Tab
            label={TABS.ENTITLEMENTS}
            isActive={selectedTab === TABS.ENTITLEMENTS}
            onClick={() => {
              setSelectedTab(TABS.ENTITLEMENTS);
            }}
          />
        ) : null}
        {canShowSecurityTab() ? (
          <Tab
            label={TABS.SECURITY}
            isActive={selectedTab === TABS.SECURITY}
            containerClass={getSecurityTabClassName()}
            onClick={() => {
              setSelectedTab(TABS.SECURITY);
            }}
          />
        ) : null}
        <Tab
          label={TABS.ATTRIBUTES}
          isActive={selectedTab === TABS.ATTRIBUTES}
          containerClass={getAdditionalAttributeClassName()}
          onClick={() => {
            setSelectedTab(TABS.ATTRIBUTES);
          }}
        />
      </Tabs>

      {renderGeneralSection()}

      {renderEntitlements()}

      {renderAttributesSection()}

      {renderSecuritySection()}
    </UserFormContext.Provider>
  );
};

UserForm.defaultProps = defaultProps;

UserForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.object };

export default UserForm;
