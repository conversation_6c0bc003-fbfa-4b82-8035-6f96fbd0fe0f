import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { CRUDModal, Card, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getEUSAContent, getEUSAStatus, updateEUSAStatus } from '../../ducks/eusa';
import { selectEUSAContent } from '../../ducks/eusa/selectors';

const modalModeDetail = {
  view: {
    headerText: 'EUSA_AGREEMENT',
  },
};

const DEFAULT_PROPS = {
  isAppSetupDone: false,
};

const EUSABanner = ({ isAppSetupDone = DEFAULT_PROPS.isAppSetupDone }) => {
  const { apiCall } = useApiCall();

  const [modalMode, setModalMode] = useState('');

  const eusaContent = useSelector(selectEUSAContent);

  const canShowEUSA = isAppSetupDone;

  useEffect(() => {
    if (canShowEUSA) {
      apiCall(getEUSAStatus())
        .then(({ accepted }) => {
          if (!accepted) {
            getContent();
          }
        })
        .catch(noop);
    }
  }, [canShowEUSA]);

  const getContent = () => {
    apiCall(getEUSAContent())
      .then(() => {
        setModalMode('view');
      })
      .catch(noop);
  };

  const renderEUSAContent = () => {
    const { agreement } = eusaContent || {};

    return (
      <Card>
        <div className="heading-small" style={{ paddingTop: '2rem' }}>
          <div className="eusa-content" dangerouslySetInnerHTML={{ __html: agreement }}></div>
        </div>
      </Card>
    );
  };

  const onSaveClick = () => {
    apiCall(updateEUSAStatus())
      .then(() => {
        setModalMode('');
      })
      .catch(noop);
  };

  if (!canShowEUSA) {
    return null;
  }

  return (
    modalMode && (
      <CRUDModal
        mode={modalMode}
        renderFormSection={renderEUSAContent}
        saveText={'ACCEPT'}
        onSaveClick={onSaveClick}
        showCancel={false}
        hideOnEscape={false}
        {...modalModeDetail[modalMode]}
      />
    )
  );
};

EUSABanner.propTypes = {
  isAppSetupDone: PropTypes.bool,
};

export default EUSABanner;
