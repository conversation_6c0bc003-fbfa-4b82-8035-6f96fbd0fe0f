import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import { faPlus, faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/api-clients';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiClientsAction = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const {
    setModalMode,
    searchTerm,
    setSearchTerm,
    privileges,
    defaultDetail,
    defaultModalMode,
    setDetail,
  } = useContext(CRUDPageContext);

  const { hasFullAccess } = privileges;

  const onAddClick = () => {
    setDetail(defaultDetail);
    setModalMode(defaultModalMode);
    setTimeout(() => {
      setModalMode('add');
    }, 0);
  };

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  return (
    <div className={`is-flex full-width ${hasFullAccess ? 'has-jc-sb' : 'has-jc-e'}`}>
      {hasFullAccess ? (
        <div className="buttons">
          <Button onClick={onAddClick}>
            <FontAwesomeIcon icon={faPlus} className="icon left" />
            <span>{t('ADD_API_CLIENT')}</span>
          </Button>
        </div>
      ) : null}

      <div className="buttons">
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          style={{ minWidth: '0px', paddingRight: '0px' }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>

        <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
      </div>
    </div>
  );
};

export default ApiClientsAction;
