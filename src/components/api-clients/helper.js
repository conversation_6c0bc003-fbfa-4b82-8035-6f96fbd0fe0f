import { defaultValidationDetail } from '@zscaler/zui-component-library';

import { find, intersection, isEmpty, omit } from 'lodash-es';

export const searchOptions = [
  { label: 'NAME', value: 'NAME' },
  { label: 'GROUP', value: 'GROUP' },
];

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'API_CLIENT',
  },
  add: {
    headerText: 'ADD_API_CLIENT',
  },
  edit: {
    headerText: 'EDIT_API_CLIENT',
  },
  delete: {
    headerText: 'DELETE_API_CLIENT',
    confirmationMessage: 'DELETE_API_CLIENT_MESSAGE',
  },
};

export const STATUS = {
  ENABLED: 'enabled',
  DISABLED: 'disabled',
};

export const VALIDATION_TYPE = {
  CLIENT_JWK: 'JWKS',
  CERTIFICATE_PUBLIC_KEY: 'PUBKEYCERT',
  SECRET: 'SECRET',
};

export const STATUS_LIST = [
  { label: 'ENABLED', value: STATUS.ENABLED },
  { label: 'DISABLED', value: STATUS.DISABLED },
];

export const VALIDATION_TYPE_LIST = [
  { label: 'CLIENT_JWK_URL', value: VALIDATION_TYPE.CLIENT_JWK },
  { label: 'CERTIFICATE_PUBLIC_KEY', value: VALIDATION_TYPE.CERTIFICATE_PUBLIC_KEY },
  { label: 'SECRET', value: VALIDATION_TYPE.SECRET },
];

export const ACCESS_TOKEN_TIME_VALUES_LIST = [
  { label: 'MINUTES', value: 'MINUTES' },
  { label: 'HOURS', value: 'HOURS' },
];

export const CLIENT_SECRET_EXPIRATION_DATES_LIST = [
  { label: '30 Days', value: '2592000' },
  { label: '60 Days', value: '5184000' },
  { label: '90 Days', value: '7776000' },
  { label: '180 Days', value: '15552000' },
  { label: '365 Days', value: '31536000' },
];

export const getFormValidationDetail = ({ formValues, selectedClientSecretMapping }) => {
  const validationDetail = { ...defaultValidationDetail };

  const {
    accessTokenLifeTimeParsedValue,
    clientName,
    clientCertificates,
    publicKeys,
    jwksUrl,
    validationType,
  } = formValues || {};

  if (!clientName) {
    validationDetail.isValid = false;
    validationDetail.context = 'clientName';
    validationDetail.type = 'error';
    validationDetail.message = 'NAME_REQUIRED_MESSAGE';

    return validationDetail;
  }

  if (!accessTokenLifeTimeParsedValue) {
    validationDetail.isValid = false;
    validationDetail.context = 'accessTokenLifeTimeParsedValue';
    validationDetail.type = 'error';
    validationDetail.message = 'Access Token Lifetime is required';

    return validationDetail;
  }

  if (validationType === 'JWKS' && !jwksUrl) {
    validationDetail.isValid = false;
    validationDetail.context = 'jwksUrl';
    validationDetail.type = 'error';
    validationDetail.message = 'JWKs URL is required.';

    return validationDetail;
  }

  if (validationType === 'PUBKEYCERT' && isEmpty(clientCertificates) && isEmpty(publicKeys)) {
    validationDetail.isValid = false;
    validationDetail.context = 'certificates';
    validationDetail.type = 'error';
    validationDetail.message = 'Client certificate or public key info are required.';

    return validationDetail;
  }

  if (validationType === 'SECRET' && isEmpty(selectedClientSecretMapping)) {
    validationDetail.isValid = false;
    validationDetail.context = 'certificates';
    validationDetail.type = 'error';
    validationDetail.message = 'Client certificate or public key info are required.';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the API client`;
  }

  if (name === 'description') {
    tooltipDetail.content = `Enter description for the API client`;
  }

  if (name === 'accessTokenLifeTimeParsedValue') {
    tooltipDetail.content = `Please specify access token life time for the API client`;
  }

  if (name === 'validationType') {
    tooltipDetail.content = `Please select a validation type`;
  }

  if (name === 'certificates') {
    tooltipDetail.content = `Please upload a certitifcate or public key`;
  }

  return tooltipDetail;
};

export const validateCert = (file) => {
  if (!file['name']) {
    return false;
  }

  const fileName = file['name'];
  const extensions = [
    '.cer',
    '.crt',
    '.pem',
    '.key',
    '.pfx',
    '.p12',
    '.p7b',
    '.p7c',
    '.pub',
    '.asc',
    '.der',
    '.keystore',
    '.jks',
  ];
  const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
  return extensions.includes(fileExtension);
};

export const getSanitizedApiPayload = ({
  detail,
  clientSecretMapping,
  radioButtonStatus,
  resourceScopeMap,
}) => {
  const {
    accessTokenLifeTimeParsedValue,
    statusValue,
    validationType,
    jwksUrl,
    clientCertificates,
    publicKeys,
  } = detail;

  const sanitizedDetail = {
    ...omit(detail, [
      'accessTokenLifeTimeParsedValue',
      'statusValue',
      'validationType',
      'jwksUrl',
      'clientCertificates',
      'publicKeys',
      'clientSecrets',
    ]),
    accessTokenLifeTime: parseInt(accessTokenLifeTimeParsedValue) * 60,
    status: statusValue === 'enabled',
    clientAuthentication: {
      authType: validationType,
    },
  };

  if (validationType === VALIDATION_TYPE.CLIENT_JWK) {
    sanitizedDetail.clientAuthentication.clientJWKsUrl = jwksUrl;
  } else if (validationType === VALIDATION_TYPE.CERTIFICATE_PUBLIC_KEY) {
    sanitizedDetail.clientAuthentication.clientCertificates = clientCertificates;
    sanitizedDetail.clientAuthentication.publicKeys = publicKeys;
  } else {
    sanitizedDetail.clientAuthentication.clientSecrets = clientSecretMapping?.map(
      // eslint-disable-next-line no-unused-vars
      ({ createdId, expirationLimit, ...rest }) => rest,
    );
  }

  sanitizedDetail.clientResources = Object.entries(radioButtonStatus).reduce(
    (acc, [resourceId, services]) => {
      const selectedScopes = Object.entries(services).reduce((scopes, [serviceId, scopeIds]) => {
        return scopes.concat(
          scopeIds.map((scopeId) => resourceScopeMap[resourceId][serviceId][scopeId]),
        );
      }, []);

      acc.push({
        id: resourceScopeMap[resourceId].resource?.id,
        name: resourceScopeMap[resourceId].resource?.name,
        defaultApi: resourceScopeMap[resourceId].resource?.defaultApi,
        selectedScopes,
      });

      return acc;
    },
    [],
  );
  return sanitizedDetail;
};

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const getScopeAndRadioButtonStatus = ({
  clientResources = [],
  resourcesList,
  reset = false,
}) => {
  const showScopes = {};
  const radioButtonStatus = {};

  resourcesList?.forEach((resource) => {
    let selectedScopeIds = [];
    if (clientResources?.length > 0) {
      const selectedScopes = find(clientResources, ['id', resource?.id])?.selectedScopes;
      selectedScopeIds = selectedScopes?.map((selectedScope) => {
        if (selectedScope?.zpaScopeId) {
          return `${selectedScope?.zpaScopeId}${selectedScope?.id}`;
        }
        return selectedScope?.id;
      });
    }
    showScopes[resource.id] = {};
    radioButtonStatus[resource.id] = {};
    resource?.serviceScopes?.forEach((scope) => {
      showScopes[resource.id][scope?.service?.name] = false;

      if (reset) {
        radioButtonStatus[resource.id][scope?.service?.id] = [];
      } else {
        const allScopeIds = scope?.scopes?.map((item) => {
          if (item?.zpaScopeId) {
            return `${item?.zpaScopeId}${item?.id}`;
          }
          return item?.id;
        });
        radioButtonStatus[resource.id][scope?.service?.id] = intersection(
          allScopeIds,
          selectedScopeIds,
        );
      }
    });
  });
  return { radioButtonStatus, showScopes };
};

export const generateRandomID = () => {
  const min = 100000;
  const max = 999999;

  const randomID = Math.floor(Math.random() * (max - min + 1)) + min; //NOSONAR not used in secure contexts
  return randomID;
};

export const generateRandomString = () => {
  const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const length = Math.floor(Math.random() * 13) + 36; //NOSONAR not used in secure contexts

  let randomString = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length); //NOSONAR not used in secure contexts
    randomString += characters[randomIndex];
  }

  return randomString;
};
