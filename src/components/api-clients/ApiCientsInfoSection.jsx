import { useTranslation } from 'react-i18next';

import {
  Card,
  Field,
  FieldGroup,
  Input,
  TextWithTooltip,
  ToggleButton,
} from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

import { STATUS, getFormTooltipDetail } from './helper';

const ApiClientsInfoSection = ({
  formValues,
  onFormFieldChange,
  isFormReadOnly,
  validationDetail,
  setFormValues,
}) => {
  const { t } = useTranslation();
  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      statusValue: formValues?.statusValue === STATUS.ENABLED ? STATUS.DISABLED : STATUS.ENABLED,
    }));
  };

  return (
    <Card>
      <FieldGroup>
        <Input
          label="NAME"
          name="clientName"
          onChange={onFormFieldChange}
          value={formValues.clientName}
          maxLength="128"
          info={validationDetail}
          tooltip={isFormReadOnly ? {} : getFormTooltipDetail('clientName')}
          disabled={isFormReadOnly}
        />
        <Input
          label="DESCRIPTION"
          name="description"
          onChange={onFormFieldChange}
          value={formValues.description}
          maxLength="128"
          info={validationDetail}
          tooltip={isFormReadOnly ? {} : getFormTooltipDetail('description')}
          disabled={isFormReadOnly}
        />
      </FieldGroup>
      <FieldGroup>
        <Field
          label="STATUS"
          containerClass="radio-container"
          tooltip={isFormReadOnly ? {} : getFormTooltipDetail('status')}
        >
          <ToggleButton
            type="success"
            isOn={formValues?.statusValue === STATUS.ENABLED}
            onToggleClick={onToggleStatusClick}
            showLabel={false}
            disabled={isFormReadOnly}
          />
        </Field>
        <Input
          containerClass="access-token-life-time"
          label="ACCESS_TOKEN_VALIDITY"
          name="accessTokenLifeTimeParsedValue"
          onChange={onFormFieldChange}
          value={formValues.accessTokenLifeTimeParsedValue}
          maxLength="128"
          info={validationDetail}
          suffixSection={t('MINUTES')}
          tooltip={isFormReadOnly ? {} : getFormTooltipDetail('accessTokenLifeTime')}
          type="number"
          disabled={isFormReadOnly}
        />
      </FieldGroup>
      {formValues?.clientId && (
        <FieldGroup>
          <Field
            label="CLIENT_ID"
            htmlFor="clientId"
            info={validationDetail}
            tooltip={isFormReadOnly ? {} : getFormTooltipDetail('clienId')}
            containerStyle={{ maxWidth: '260px' }}
          >
            <TextWithTooltip
              text={formValues?.clientId}
              canCopy={!isFormReadOnly}
              showCopy={!isFormReadOnly}
              showtooltip={isFormReadOnly ? {} : false}
            >
              {formValues?.clientId}
            </TextWithTooltip>
          </Field>
        </FieldGroup>
      )}
    </Card>
  );
};

ApiClientsInfoSection.propTypes = {
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  isFormReadOnly: PropTypes.bool,
  validationDetail: PropTypes.object,
  setFormValues: PropTypes.func,
};
export default ApiClientsInfoSection;
