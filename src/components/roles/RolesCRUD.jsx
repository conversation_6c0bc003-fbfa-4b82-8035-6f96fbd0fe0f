import { useContext } from 'react';

import {
  CRUDModal,
  getApiDELETENotificationOptions,
  getApiPOSTNotificationOptions,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import { addNewRole, deleteRole, updateUserRole } from '../../ducks/roles/index';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';
import RolesForm from './RolesForm';
import { modalModeDetail } from './helper';

const RolesCRUD = () => {
  const { apiCall } = useApiCall();

  const {
    modalMode,
    setModalMode,
    detail,
    setDetail,
    defaultModalMode,
    defaultDetail,
    privileges,
  } = useContext(CRUDPageContext);

  const { hasFullAccess, noAccess } = privileges;

  const onCloseClick = () => {
    setModalMode(defaultModalMode);
    setDetail(defaultDetail);
  };

  const onSaveClick = () => {
    if (modalMode === 'add') {
      apiCall(addNewRole(detail), {
        successNotificationPayload: { ...getApiPOSTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'edit') {
      apiCall(updateUserRole(detail), {
        successNotificationPayload: { ...getApiPUTNotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }

    if (modalMode === 'delete') {
      apiCall(deleteRole(detail), {
        successNotificationPayload: { ...getApiDELETENotificationOptions() },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  if (noAccess) {
    return <NoAccess />;
  }

  const showSave = hasFullAccess && !detail.isSystemRole;
  const cancelText = hasFullAccess && !detail.isSystemRole ? 'CANCEL' : 'CLOSE';

  return (
    <CRUDModal
      mode={modalMode}
      containerClass={'modal-container-roles'}
      renderFormSection={(props) => <RolesForm {...props} />}
      saveText={modalMode === 'actionConfirmation' ? 'RESET' : ''}
      cancelText={cancelText}
      showSave={showSave}
      onSaveClick={onSaveClick}
      onCloseClick={onCloseClick}
      {...modalModeDetail[modalMode]}
    />
  );
};

export default RolesCRUD;
