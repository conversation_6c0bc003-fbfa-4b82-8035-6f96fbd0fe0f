import { defaultValidationDetail } from '@zscaler/zui-component-library';

import { PERMISSIONS_KEY, PERMISSION_LEVEL } from '../../config';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'ROLE',
  },
  add: {
    headerText: 'ADD_ROLE',
  },
  edit: {
    headerText: 'EDIT_ROLE',
  },
  delete: {
    headerText: 'DELETE_ROLE',
    confirmationMessage: 'Are you sure you want to delete this role? The changes cannot be undone.',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage: 'BULK_DELETE_ROLE_CONFIRMATION_MESSAGE',
  },
  importFile: {
    headerText: 'IMPORT_ROLE',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const PERMISSIONS_LEVEL_DETAIL = {
  [PERMISSION_LEVEL.FULL]: {
    id: PERMISSION_LEVEL.FULL,
    label: 'Full',
    order: 1,
  },
  [PERMISSION_LEVEL.RESTRICTED_FULL]: {
    id: PERMISSION_LEVEL.RESTRICTED_FULL,
    label: 'Restricted Full',
    order: 2,
  },
  [PERMISSION_LEVEL.VIEW]: {
    id: PERMISSION_LEVEL.VIEW,
    label: 'View Only',
    order: 3,
  },
  [PERMISSION_LEVEL.RESTRICTED_VIEW]: {
    id: PERMISSION_LEVEL.RESTRICTED_VIEW,
    label: 'Restricted View',
    order: 4,
  },
  [PERMISSION_LEVEL.NONE]: {
    id: PERMISSION_LEVEL.NONE,
    label: 'None',
    order: 5,
  },
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'NAME_REQUIRED_MESSAGE';

    return validationDetail;
  }

  return validationDetail;
};

export const PERMISSION_KEY_DETAILS = {
  [PERMISSIONS_KEY.SIGN_ON_POLICY]: {
    label: 'SIGN_ON_POLICIES',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Policy &gt; Admin Sign-On</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_METHODS]: {
    label: 'AUTHENTICATION_METHODS',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Policy &gt; Password </strong>,{' '}
        <strong className="tooltip-bold">
          Administration &gt; Authentication &gt; Authentication Methods
        </strong>{' '}
        and{' '}
        <strong className="tooltip-bold">
          Administration &gt; Authentication &gt; Device Token
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY]: {
    label: 'USERS_AND_GROUPS_TEXT',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Directory &gt; Users </strong>,{' '}
        <strong>Directory &gt; User Groups </strong>,{' '}
        <strong className="tooltip-bold">Directory &gt; Attributes</strong> and{' '}
        <strong className="tooltip-bold">Directory &gt; Departments</strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.USERS_CREDENTIALS_POLICY]: {
    label: 'USERS_CREDENTIALS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">
          Directory &gt; Users &gt; Edit User &gt; Security Settings{' '}
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.EXTERNAL_IDENTITIES_POLICY]: {
    label: 'EXTERNAL_IDENTITIES',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Integration &gt; External Identities </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.IP_LOCATION_POLICY]: {
    label: 'IP_LOCATIONS_AND_GROUPS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Environment &gt; IP Locations </strong>{' '}
        and{' '}
        <strong className="tooltip-bold">
          Administration &gt; Environment &gt; IP Locations Groups
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.LINKED_TENATS_POLICY]: {
    label: 'LINKED_TENANTS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; System &gt; Linked Services </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY]: {
    label: 'AUTHENTICATION_SESSION',
    tooltip: (
      <p>
        Set the access level to the Authentication Session section in{' '}
        <strong className="tooltip-bold">
          Administration &gt; Authentication &gt; Authentication Session{' '}
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY]: {
    label: 'ADMINISTRATIVE_ENTITLEMENTS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">
          Administration &gt; Entitlements &gt; Administrative{' '}
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY]: {
    label: 'SERVICE_ENTITLEMENTS_TEXT',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Entitlements &gt; Service </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUDIT_LOGS_POLICY]: {
    label: 'AUDIT_LOGS',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; System &gt; Audit Logs </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.AUTHENTICATION_EVENT_LOG_POLICY]: {
    label: 'AUTHENTICATION_EVENT_LOG',
    tooltip: '',
  },
  [PERMISSIONS_KEY.ROLES_POLICY]: {
    label: 'ROLES',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">
          Administration &gt; Entitlements &gt; ZIdenity Roles{' '}
        </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.GUEST_DOMAIN_POLICY]: {
    label: 'GUEST_DOMAIN',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Environment &gt; Domains </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.REMOTE_ASSISTANCE_MANAGEMENT]: {
    label: 'REMOTE_ASSISTANCE',
    tooltip: (
      <p>
        Set the access level to <strong className="tooltip-bold">Remote Assistance </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.BRANDING_POLICY]: {
    label: 'BRANDING',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Environment &gt; Branding </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.VIEW,
  },
  [PERMISSIONS_KEY.CXO_INSIGHT]: {
    label: 'CXO_INSIGHT',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Administration &gt; Executive Insights </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES]: {
    label: 'API_CLIENTS_AND_RESOURCES',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Integration &gt; API Clients, Resources & Tokens </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.TOKEN_VALIDATOR_POLICY]: {
    label: 'TOKEN_VALIDATOR',
    tooltip: (
      <p>
        Set the access level to{' '}
        <strong className="tooltip-bold">Integration &gt; Token Validators </strong>
      </p>
    ),
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.ZAA_PROFILES]: {
    label: 'ZAA_PROFILES',
    tooltip: <p>Set the access level to </p>,
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.ZAA_CONTEXT_NUGGET_OVERRIDES]: {
    label: 'ZAA_CONTEXT_NUGGET_OVERRIDES',
    tooltip: <p>Set the access level to </p>,
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.ZAA_INTEGRATIONS]: {
    label: 'ZAA_INTEGRATIONS',
    tooltip: <p>Set the access level to </p>,
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
  [PERMISSIONS_KEY.ZAA_SIGNAL_HISTORY]: {
    label: 'ZAA_SIGNAL_HISTORY',
    tooltip: <p>Set the access level to </p>,
    defaultPermission: PERMISSION_LEVEL.NONE,
  },
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the location`;
  }

  if (name === 'country') {
    tooltipDetail.content = `Select the country of the IP location`;
  }

  if (name === 'ipAddress') {
    tooltipDetail.content = (
      <p>
        Enter the IP address of the location and click{' '}
        <strong className="tooltip-bold">Add Items</strong>. You can add mutiple IP addresses for a
        location. Click <strong className="tooltip-bold">X</strong> to remove a IP address or{' '}
        <strong className="tooltip-bold">Remove All</strong> to remove all selections.
      </p>
    );
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Enable this option if you want to update your existing locations, delete existing locations,
        or add new locations. If you only want to add new locations, Zscaler doesn&apos;t recommend
        selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-ip-locations-csv-file">
          Importing IP Locations from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
