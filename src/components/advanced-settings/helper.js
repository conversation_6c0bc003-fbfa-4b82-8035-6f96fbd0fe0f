import { defaultValidationDetail } from '@zscaler/zui-component-library';

const isInValidRange = (value) => {
  let numValue = Number(value);

  return !isNaN(numValue) && numValue >= 5 && numValue <= 600;
};

export const getFormValidationDetail = ({ formValues, isUserSSOEnabled }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { serviceAuthnSessionEnabled, serviceSessionTimeout, sessionIdleTimeout } =
    formValues || {};

  if (!sessionIdleTimeout || !isInValidRange(sessionIdleTimeout)) {
    validationDetail.isValid = false;
    validationDetail.context = 'sessionIdleTimeout';
    validationDetail.type = 'error';
    validationDetail.message =
      'Session Idle Timeout is required and should be between 5 and 600 minutes';

    return validationDetail;
  }

  if (
    isUserSSOEnabled &&
    serviceAuthnSessionEnabled &&
    (!serviceSessionTimeout || !isInValidRange(serviceSessionTimeout))
  ) {
    validationDetail.isValid = false;
    validationDetail.context = 'serviceSessionTimeout';
    validationDetail.type = 'error';
    validationDetail.message =
      'Service Session Timeout is required and should be between 5 and 600 minutes';

    return validationDetail;
  }

  return validationDetail;
};
