import { defaultValidationDetail } from '@zscaler/zui-component-library';

import { startsWith } from 'lodash-es';

export const getFormValidationDetail = ({ formValues }) => {
  const { emailBranding } = formValues;
  const validationDetail = { ...defaultValidationDetail };

  if (Array.isArray(emailBranding) && emailBranding.length > 0) {
    for (let i = 0; i < emailBranding.length; i++) {
      if (!emailBranding[i].subject) {
        validationDetail.isValid = false;
        validationDetail.context = emailBranding[i].name;
        validationDetail.type = 'error';
        validationDetail.message = `Subject for ${emailBranding[i].name} is required`;
        break;
      }
    }
    if (!validationDetail.isValid) {
      return validationDetail;
    }
  }

  return validationDetail;
};

export const validateImage = (file) => {
  if (!file['type']) {
    return false;
  }

  return file['type'].includes('image') && file['size'] <= 560000;
};

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const getApiPayload = (formValues) => {
  if (formValues?.logo) {
    let imageSrc = formValues.logo;

    if (startsWith(imageSrc, 'data:')) {
      const subStr = 'base64,';
      const index = imageSrc.indexOf(subStr);
      imageSrc = index !== -1 ? imageSrc.slice(index + subStr.length) : imageSrc;
      formValues.logo = imageSrc;
    }
  }
  return formValues;
};
