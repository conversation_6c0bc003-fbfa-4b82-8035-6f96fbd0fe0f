import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faUpload } from '@fortawesome/pro-regular-svg-icons';
import {
  Button,
  Card,
  DownloadFile,
  Field,
  FieldGroup,
  Input,
  InputFile,
  RadioButtons,
  TextWithTooltip,
  getOnChangeValue,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import {
  downloadMetadata,
  getMetaData,
  uploadIDPCert,
  uploadMetadata,
} from '../../ducks/external-identities';
import {
  selectIdPCertificate,
  selectSAMLMetadata,
} from '../../ducks/external-identities/selectors';

import { CONFIGURATION_LIST, CONFIGURATION_MODE, getFormTooltipDetail } from './helper';

const defaultProps = {
  isEditMode: false,
  formValues: {},
  setFormValues: noop,
  isActionChoosen: false,
  setIsActionChoosen: noop,
  activeConfigurationMode: '',
  setActiveConfigurationMode: noop,
  privileges: {
    hasFullAccess: false,
  },
};

// https://samltest.id/saml/idp
const SAMLConfigurationForm = ({
  isEditMode,
  formValues,
  setFormValues,
  isActionChoosen,
  setIsActionChoosen,
  activeConfigurationMode,
  setActiveConfigurationMode,
  privileges,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const { hasFullAccess } = privileges;

  const { samlIdpCertificate, samlIdpEntityId, samlIdpSSOUrl, samlSpEntityId, samlSpAcsUrl } =
    formValues.samlConfigInfo || {};

  const samlMetadata = useSelector(selectSAMLMetadata);

  const idpCertificate = useSelector(selectIdPCertificate);

  const isManualMode = activeConfigurationMode === CONFIGURATION_MODE.MANUAL;

  const isRadioButtonSelected = (detail = {}) => {
    const { value } = detail;

    return value === activeConfigurationMode;
  };

  const onRadioButtonsClick = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setActiveConfigurationMode(value);
    }
  };

  useEffect(() => {
    const { samlIdpCertificate, samlIdpEntityId, samlIdpSSOUrl } = samlMetadata;

    if (samlIdpCertificate && samlIdpEntityId && samlIdpSSOUrl) {
      setFormValues((prevState) => {
        const samlConfigInfo = {
          ...prevState.samlConfigInfo,
          samlIdpCertificate,
          samlIdpEntityId,
          samlIdpSSOUrl,
        };

        return cloneDeep({ ...prevState, samlConfigInfo });
      });
    }
  }, [samlMetadata]);

  useEffect(() => {
    if (isManualMode) {
      const { hash } = idpCertificate;

      if (hash) {
        setFormValues((prevState) => {
          const samlConfigInfo = {
            ...prevState.samlConfigInfo,
            samlIdpCertificate: idpCertificate,
          };

          return cloneDeep({ ...prevState, samlConfigInfo });
        });
      } else {
        setFormValues((prevState) => {
          const samlConfigInfo = {
            ...prevState.samlConfigInfo,
            samlIdpCertificate: {},
          };

          return cloneDeep({ ...prevState, samlConfigInfo });
        });
      }
    }
  }, [idpCertificate, isManualMode]);

  const onFetchSAMLIdpMetaDataUrl = () => {
    if (formValues.samlConfigInfo?.samlIdpMetaDataUrl) {
      apiCall(
        getMetaData({
          metadataurl: formValues.samlConfigInfo?.samlIdpMetaDataUrl,
          idpProfileType: 'SAML',
        }),
      )
        .then(() => {
          setIsActionChoosen(true);
        })
        .catch(noop);
    }
  };

  const onUploadMetadata = (detail) => {
    if (detail?.length > 0) {
      apiCall(uploadMetadata(detail))
        .then(() => {
          setIsActionChoosen(true);
        })
        .catch(noop);
    }
  };

  const onUploadCertificate = (detail) => {
    if (detail?.length > 0) {
      apiCall(uploadIDPCert(detail)).catch(noop);
    }
  };

  const onFormFieldChange = (evt) => {
    const updatedValue = getOnChangeValue(evt.target);

    setFormValues((prevState) => {
      const samlConfigInfo = {
        ...prevState.samlConfigInfo,
        ...updatedValue,
      };

      return { ...prevState, samlConfigInfo };
    });
  };

  const onDownloadSPMetadata = async () => {
    if (formValues.id) {
      return await apiCall(downloadMetadata(formValues.id));
    }

    return null;
  };

  const renderCertificatesSection = () => {
    if (!isActionChoosen && hasFullAccess && !isManualMode) {
      return null;
    }

    if (isManualMode) {
      return (
        <Field
          label="IDP_CERTIFICATE"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpCertificate')}
        >
          <InputFile
            onChange={onUploadCertificate}
            leftIcon={faUpload}
            hasLeftIcon
            buttonLabel="UPLOAD_CERTIFICATE"
            buttonType="tertiary"
            buttonContainerClass="no-p-l"
            fileInfoPosition={'right'}
            fileInfoLabel={samlIdpCertificate?.hash || samlIdpCertificate?.id || ''}
          />
        </Field>
      );
    }

    return (
      <Field
        label="IDP_CERTIFICATE"
        tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpCertificate')}
      >
        <TextWithTooltip>
          {samlIdpCertificate?.subjectName ||
            samlIdpCertificate?.hash ||
            samlIdpCertificate?.id ||
            ''}
        </TextWithTooltip>
      </Field>
    );
  };

  const renderURLModeContent = () => {
    if (activeConfigurationMode === CONFIGURATION_MODE.URL) {
      return (
        <Field
          label="IDP_METADATA_URL"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('idpMetadataUrl')}
        >
          <div className="is-flex has-jc-sb has-ai-c full-width">
            <Input
              name="samlIdpMetaDataUrl"
              value={formValues.samlConfigInfo?.samlIdpMetaDataUrl}
              onChange={onFormFieldChange}
              containerStyle={{ margin: '0' }}
              disabled={!hasFullAccess}
            />

            {hasFullAccess && (
              <Button
                onClick={onFetchSAMLIdpMetaDataUrl}
                containerStyle={{ marginLeft: '16px' }}
                disabled={!formValues.samlConfigInfo?.samlIdpMetaDataUrl}
              >
                {t('FETCH')}
              </Button>
            )}
          </div>
        </Field>
      );
    }

    return null;
  };

  const renderUploadModeContent = () => {
    if (activeConfigurationMode === CONFIGURATION_MODE.UPLOAD) {
      return (
        <Field
          label="IDP_METADATA"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('idpMetadata')}
        >
          <InputFile
            onChange={onUploadMetadata}
            leftIcon={faUpload}
            hasLeftIcon
            buttonLabel="UPLOAD_METADATA"
            buttonType="tertiary"
            buttonContainerClass="no-p-l"
            fileInfoPosition={'right'}
          />
        </Field>
      );
    }

    return null;
  };

  const renderCommonFormContent = () => {
    const spDetailSection = (
      <FieldGroup>
        <Field label="SP_ENTITY_ID">
          <TextWithTooltip text={samlSpEntityId} canCopy showCopy>
            {samlSpEntityId}
          </TextWithTooltip>
        </Field>
        <Field label="SP_URL">
          <TextWithTooltip text={samlSpAcsUrl} canCopy showCopy>
            {samlSpAcsUrl}
          </TextWithTooltip>
        </Field>
      </FieldGroup>
    );

    if (!isActionChoosen && hasFullAccess) {
      return (
        <>
          <FieldGroup>
            {renderURLModeContent()}
            {renderUploadModeContent()}
          </FieldGroup>

          {spDetailSection}
        </>
      );
    }

    return (
      <>
        <FieldGroup>
          {renderURLModeContent()}
          {renderUploadModeContent()}
        </FieldGroup>

        <FieldGroup>
          {isManualMode ? (
            <Input
              label="IDP_ENTITY_URI"
              name="samlIdpEntityId"
              value={samlIdpEntityId}
              onChange={onFormFieldChange}
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpEntityId')}
              disabled={!hasFullAccess}
            />
          ) : (
            <Field
              label="IDP_ENTITY_URI"
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpEntityId')}
            >
              <TextWithTooltip text={samlIdpEntityId}>{samlIdpEntityId}</TextWithTooltip>
            </Field>
          )}

          {isManualMode ? (
            <Input
              label="IDP_SINGLE_SIGNON_URL"
              name="samlIdpSSOUrl"
              value={samlIdpSSOUrl}
              onChange={onFormFieldChange}
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpSSOUrl')}
              disabled={!hasFullAccess}
            />
          ) : (
            <Field
              label="IDP_SINGLE_SIGNON_URL"
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('samlIdpSSOUrl')}
            >
              <TextWithTooltip text={samlIdpSSOUrl} canCopy>
                {samlIdpSSOUrl}
              </TextWithTooltip>
            </Field>
          )}
        </FieldGroup>

        <FieldGroup>
          {renderCertificatesSection()}

          {(isEditMode || !hasFullAccess) && (
            <Field label="SP_METADATA">
              <DownloadFile
                variantType="iconWithText"
                label="DOWNLOAD_SP_METADATA"
                onDownloadClick={onDownloadSPMetadata}
              />
            </Field>
          )}
        </FieldGroup>

        {spDetailSection}
      </>
    );
  };

  return (
    <Card>
      <Field
        label="INPUT_METHOD"
        containerClass="radio-container"
        tooltip={!hasFullAccess ? {} : getFormTooltipDetail('inputMethod')}
      >
        <RadioButtons
          list={CONFIGURATION_LIST}
          isSelected={isRadioButtonSelected}
          onClick={onRadioButtonsClick}
          containerClass={!hasFullAccess ? 'disabled' : ''}
        />
      </Field>

      {renderCommonFormContent()}
    </Card>
  );
};

SAMLConfigurationForm.defaultProps = defaultProps;

SAMLConfigurationForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  isActionChoosen: PropTypes.bool,
  setIsActionChoosen: PropTypes.func,
  activeConfigurationMode: PropTypes.string,
  setActiveConfigurationMode: PropTypes.func,
  privileges: PropTypes.object,
};

export default SAMLConfigurationForm;
