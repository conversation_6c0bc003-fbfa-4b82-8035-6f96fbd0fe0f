import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import {
  <PERSON>ton,
  Card,
  DropDown,
  Field,
  FieldGroup,
  PasswordInput,
  ToggleButton,
  defaultFormPropTypes,
  defaultFormProps,
  useApiCall,
} from '@zscaler/zui-component-library';

import PropTypes from 'prop-types';

import { getBearerToken } from '../../ducks/external-identities';
import { selectAuthenticationMethodsList } from '../../ducks/external-identities/selectors';
import { showSuccessNotification } from '../../ducks/global';

import JITAttributeMappingForm from './JITAttributeMappingForm';
import SCIMAttributeMappingForm from './SCIMAttributeMappingForm';
import { getFormTooltipDetail } from './helper';

const defaultProps = {
  ...defaultFormProps,
  privileges: {
    hasFullAccess: false,
    hasViewAccess: false,
  },
};

const ProvisioningSettingsForm = ({
  mode,
  detail,
  onDetailChange,
  privileges,
  validationDetail,
}) => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();

  const { hasFullAccess, hasViewAccess } = privileges;

  const isEditMode = mode === 'edit';

  const { scimProvisionEnabled, endPointUrl, bearerToken } = detail?.scimConfigInfo || {};

  const { jitProvisionEnabled } = detail?.jitConfigInfo || {};

  const authenticationMethodsList = useSelector(selectAuthenticationMethodsList);

  const onJITProvisionToggle = () => {
    onDetailChange((prevState) => {
      const jitConfigInfo = {
        ...prevState.jitConfigInfo,
        jitProvisionEnabled: !prevState.jitConfigInfo?.jitProvisionEnabled,
      };

      return { ...prevState, jitConfigInfo };
    });
  };

  const onSCIMProvisionToggle = () => {
    onDetailChange((prevState) => {
      const scimConfigInfo = {
        ...prevState.scimConfigInfo,
        scimProvisionEnabled: !prevState.scimConfigInfo?.scimProvisionEnabled,
      };

      return { ...prevState, scimConfigInfo };
    });
  };

  const onGenerateTokenClick = () => {
    apiCall(getBearerToken()).then((bearerToken) => {
      if (bearerToken) {
        onDetailChange((prevState) => {
          const scimConfigInfo = {
            ...prevState.scimConfigInfo,
            bearerToken,
          };

          dispatch(
            showSuccessNotification({
              message: 'BEARER_TOKEN_SUCCESSFULLY_GENERATED',
            }),
          );

          return { ...prevState, scimConfigInfo };
        });
      }
    });
  };

  const renderJITSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('JIT_PROVISIONING')}</section>

        <Card>
          <Field
            label="JIT_PROVISIONING"
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('jitProvisionEnabled')}
          >
            <ToggleButton
              type="success"
              isOn={jitProvisionEnabled}
              onToggleClick={onJITProvisionToggle}
              showLabel={false}
              disabled={!hasFullAccess}
            />
          </Field>
        </Card>
      </>
    );
  };

  const renderSAMLAttributeMappingSection = () => {
    return (
      <JITAttributeMappingForm
        onDetailChange={onDetailChange}
        detail={detail}
        privileges={privileges}
      />
    );
  };

  const renderSCIMSection = () => {
    return (
      <>
        <section className="typography-paragraph1-uppercase">{t('SCIM_PROVISIONING')}</section>

        <Card>
          <FieldGroup containerClass="has-jc-s has-ai-c">
            <Field
              label="SCIM_PROVISIONING"
              containerStyle={{ width: '45%' }}
              tooltip={!hasFullAccess ? {} : getFormTooltipDetail('scimProvisionEnabled')}
            >
              <ToggleButton
                type="success"
                isOn={scimProvisionEnabled}
                onToggleClick={onSCIMProvisionToggle}
                showLabel={false}
                disabled={!hasFullAccess}
              />
            </Field>

            {isEditMode && scimProvisionEnabled && (
              <>
                <PasswordInput
                  type="text"
                  label="SCIM_ENDPOINT_URL"
                  value={endPointUrl}
                  showInputTypeToggle={false}
                  canCopy
                  showCopiedPassword={true}
                  disabled={!hasFullAccess}
                />
              </>
            )}
          </FieldGroup>

          {scimProvisionEnabled && (
            <>
              <FieldGroup containerClass="has-jc-sb has-ai-c">
                <Field
                  label="AUTHENTICATION_METHOD"
                  tooltip={!hasFullAccess ? {} : getFormTooltipDetail('authenticationMethod')}
                >
                  <DropDown
                    list={authenticationMethodsList}
                    selectedList={authenticationMethodsList}
                    disabled={!hasFullAccess}
                  />
                </Field>
              </FieldGroup>

              {hasViewAccess && (
                <FieldGroup containerClass="has-jc-sb has-ai-c">
                  <PasswordInput
                    label="TOKEN"
                    name="bearerToken"
                    value={bearerToken}
                    showInputTypeToggle={false}
                    canCopy
                    tooltip={!hasFullAccess ? {} : getFormTooltipDetail('bearerToken')}
                    showCopiedPassword={true}
                    disabled={!hasFullAccess}
                    info={validationDetail}
                  />

                  {hasFullAccess && (
                    <Button
                      type="secondary"
                      onClick={onGenerateTokenClick}
                      containerStyle={{ width: '200px', marginTop: '16px' }}
                    >
                      {t('GENERATE_TOKEN')}
                    </Button>
                  )}
                </FieldGroup>
              )}
            </>
          )}
        </Card>
      </>
    );
  };

  const renderSCIMAttributeMappingSection = () => {
    return (
      <SCIMAttributeMappingForm
        onDetailChange={onDetailChange}
        detail={detail}
        privileges={privileges}
      />
    );
  };

  return (
    <>
      {renderJITSection()}
      {renderSAMLAttributeMappingSection()}

      {renderSCIMSection()}
      {renderSCIMAttributeMappingSection()}
    </>
  );
};

ProvisioningSettingsForm.defaultProps = defaultProps;

ProvisioningSettingsForm.propTypes = { ...defaultFormPropTypes, privileges: PropTypes.object };

export default ProvisioningSettingsForm;
