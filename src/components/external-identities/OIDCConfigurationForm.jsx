import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Button,
  Card,
  Field,
  FieldGroup,
  Input,
  ListBuilder,
  PasswordInput,
  RadioButtons,
  TextWithTooltip,
  getOnChangeValue,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getMetaData } from '../../ducks/external-identities';
import { selectOIDCMetadata } from '../../ducks/external-identities/selectors';

import {
  CONFIGURATION_MODE,
  OIDC_INPUT_METHOD_LIST,
  TOKEN_ENDPOINT_AUTHENTICATION_METHOD_LIST,
  getFormTooltipDetail,
} from './helper';

const defaultProps = {
  isEditMode: false,
  formValues: {},
  setFormValues: noop,
  isActionChoosen: false,
  setIsActionChoosen: noop,
  activeConfigurationMode: '',
  setActiveConfigurationMode: noop,
  privileges: {
    hasFullAccess: false,
  },
};

// https://samltest.id/saml/idp
const OIDCConfigurationForm = ({
  formValues,
  setFormValues,
  isActionChoosen,
  setIsActionChoosen,
  activeConfigurationMode,
  setActiveConfigurationMode,
  validationDetail,
  privileges,
}) => {
  const { t } = useTranslation();

  const { apiCall } = useApiCall();

  const { hasFullAccess } = privileges;

  const {
    redirectURI,
    tokenEndpointAuthenticationMethod,
    clientId,
    clientSecret,
    scopes,
    defaultAuthenticationContext,
    issuer,
    authorizationEndpoint,
    tokenEndpoint,
    jwksEndpoint,
    userinfoEndpoint,
  } = formValues.oidcConfigInfo || {};

  const oidcMetadata = useSelector(selectOIDCMetadata);

  const [list, setList] = useState(scopes || []);

  const [selectedTokenAuthenticationMethod, setSelectedTokenAuthenticationMethod] = useState(
    tokenEndpointAuthenticationMethod,
  );

  const isManualMode = activeConfigurationMode === CONFIGURATION_MODE.MANUAL;

  const isInputMethodSelected = (detail = {}) => {
    const { value } = detail;

    return value === activeConfigurationMode;
  };

  const onInputMethodClick = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setActiveConfigurationMode(value);
    }
  };

  const isAuthenticationMethodSelected = (detail = {}) => {
    const { value } = detail;

    return value === selectedTokenAuthenticationMethod;
  };

  const onAuthenticationMethodSelected = (detail = {}) => {
    const { value } = detail;

    if (value) {
      setFormValues((prevState) => {
        const oidcConfigInfo = {
          ...prevState.oidcConfigInfo,
          tokenEndpointAuthenticationMethod: value,
        };

        return cloneDeep({ ...prevState, oidcConfigInfo });
      });

      setSelectedTokenAuthenticationMethod(value);
    }
  };

  const onFormFieldChange = (evt) => {
    const updatedValue = getOnChangeValue(evt.target);

    setFormValues((prevState) => {
      const oidcConfigInfo = {
        ...prevState.oidcConfigInfo,
        ...updatedValue,
      };

      return { ...prevState, oidcConfigInfo };
    });
  };

  useEffect(() => {
    const {
      authorizationEndpoint = '',
      issuer = '',
      jwksEndpoint = '',
      tokenEndpoint = '',
      userinfoEndpoint = '',
    } = oidcMetadata;

    if (authorizationEndpoint && issuer && jwksEndpoint && tokenEndpoint && userinfoEndpoint) {
      setFormValues((prevState) => {
        const oidcConfigInfo = {
          ...prevState.oidcConfigInfo,
          authorizationEndpoint,
          issuer,
          jwksEndpoint,
          tokenEndpoint,
          userinfoEndpoint,
        };

        return cloneDeep({ ...prevState, oidcConfigInfo });
      });
    }
  }, [oidcMetadata]);

  useEffect(() => {
    if (isManualMode) {
      setIsActionChoosen(true);
    }
  }, [isManualMode]);

  useEffect(() => {
    setFormValues((prevState) => {
      const oidcConfigInfo = {
        ...prevState.oidcConfigInfo,
        scopes: list.filter((scope) => !!scope),
      };

      return cloneDeep({ ...prevState, oidcConfigInfo });
    });
  }, [list]);

  const onFetchSAMLIdpMetaDataUrl = () => {
    const oidcIdpMetaDataUrl = formValues.oidcConfigInfo?.oidcIdpMetaDataUrl;

    if (oidcIdpMetaDataUrl) {
      apiCall(
        getMetaData({
          metadataurl: oidcIdpMetaDataUrl,
          idpProfileType: 'OIDC',
        }),
      )
        .then(() => {
          setIsActionChoosen(true);
        })
        .catch(noop);
    }
  };

  const renderURLModeContent = () => {
    if (activeConfigurationMode === CONFIGURATION_MODE.URL) {
      return (
        <Field
          label="METADATA_URL"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('oidcIdpMetaDataUrl')}
        >
          <div className="is-flex has-jc-sb has-ai-c full-width">
            <Input
              name="oidcIdpMetaDataUrl"
              value={formValues.oidcConfigInfo?.oidcIdpMetaDataUrl}
              onChange={onFormFieldChange}
              containerStyle={{ margin: '0' }}
              disabled={!hasFullAccess}
            />

            {hasFullAccess && (
              <Button
                onClick={onFetchSAMLIdpMetaDataUrl}
                containerStyle={{ marginLeft: '16px' }}
                disabled={!formValues.oidcConfigInfo?.oidcIdpMetaDataUrl}
              >
                {t('FETCH')}
              </Button>
            )}
          </div>
        </Field>
      );
    }

    return null;
  };

  const scopeValidators = ({ item }) => {
    const itemToValidate = item?.trim();

    if (!itemToValidate) {
      return false;
    }

    if (itemToValidate.length <= 1024) {
      return true;
    } else {
      return false;
    }
  };

  const getScopeValidationMessage = ({ invalidItems }) => {
    return `Scope can't be empty and must not exceed 1024 characters - ${invalidItems.join(', ')}`;
  };

  const renderCommonFormContent = () => {
    return (
      <>
        {(isActionChoosen || !hasFullAccess) && (
          <Input
            label="ISSUER"
            name="issuer"
            value={issuer}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('issuer')}
            readOnly={!isManualMode}
            info={validationDetail}
            disabled={!hasFullAccess}
          />
        )}

        {(isActionChoosen || !hasFullAccess) && (
          <Input
            label="AUTHORIZATION_ENDPOINT"
            name="authorizationEndpoint"
            value={authorizationEndpoint}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('authorizationEndpoint')}
            readOnly={!isManualMode}
            info={validationDetail}
            disabled={!hasFullAccess}
          />
        )}

        {(isActionChoosen || !hasFullAccess) && (
          <Input
            label="TOKEN_ENDPOINT"
            name="tokenEndpoint"
            value={tokenEndpoint}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('tokenEndpoint')}
            readOnly={!isManualMode}
            info={validationDetail}
            disabled={!hasFullAccess}
          />
        )}

        {(isActionChoosen || !hasFullAccess) && (
          <Input
            label="JWKS_ENDPOINT"
            name="jwksEndpoint"
            value={jwksEndpoint}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('jwksEndpoint')}
            readOnly={!isManualMode}
            info={validationDetail}
            disabled={!hasFullAccess}
          />
        )}

        {(isActionChoosen || !hasFullAccess) && (
          <Input
            label="USER_INFORMATION_ENDPOINT"
            name="userinfoEndpoint"
            value={userinfoEndpoint}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('userinfoEndpoint')}
            readOnly={!isManualMode}
            info={validationDetail}
            disabled={!hasFullAccess}
          />
        )}

        <FieldGroup>
          <Field
            label="REDIRECT_URI"
            htmlFor="redirectURI"
            info={validationDetail}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('redirectURI')}
          >
            <TextWithTooltip
              text={redirectURI}
              canCopy
              showCopy
              showtooltip={!hasFullAccess ? {} : false}
            >
              {redirectURI}
            </TextWithTooltip>
          </Field>
        </FieldGroup>

        <Field
          label="TOKEN_ENDPOINT_AUTHENTICATION_METHOD"
          containerClass="radio-container"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('tokenAuthenticationMethod')}
        >
          <RadioButtons
            list={TOKEN_ENDPOINT_AUTHENTICATION_METHOD_LIST}
            isSelected={isAuthenticationMethodSelected}
            onClick={onAuthenticationMethodSelected}
            containerClass={!hasFullAccess ? 'disabled' : ''}
          />
        </Field>

        <FieldGroup>
          <Input
            label="CLIENT_ID"
            name="clientId"
            value={clientId}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('clientId')}
            info={validationDetail}
            maxLength="128"
            disabled={!hasFullAccess}
          />

          <PasswordInput
            label="CLIENT_SECRET"
            name="clientSecret"
            value={clientSecret}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('clientSecret')}
            info={validationDetail}
            maxLength="128"
            disabled={!hasFullAccess}
          />
        </FieldGroup>

        <Field
          label="REQUESTED_SCOPES"
          containerClass="full-width"
          tooltip={!hasFullAccess ? {} : getFormTooltipDetail('requestedScope')}
          htmlFor="scopes"
          info={validationDetail}
        >
          <ListBuilder
            list={list}
            setList={setList}
            removeBlackList={['openid']}
            validator={scopeValidators}
            getValidationMessage={getScopeValidationMessage}
            separator={/[\r\n,]+/}
            disabled={!hasFullAccess}
          />
        </Field>

        <FieldGroup>
          <Input
            label="DEFAULT_REQUESTED_AUTHENTICATION_CONTEXT"
            name="defaultAuthenticationContext"
            value={defaultAuthenticationContext}
            onChange={onFormFieldChange}
            tooltip={!hasFullAccess ? {} : getFormTooltipDetail('defaultAuthenticationContext')}
            maxLength="256"
            disabled={!hasFullAccess}
          />
        </FieldGroup>
      </>
    );
  };

  return (
    <Card>
      <Field
        label="INPUT_METHOD"
        containerClass="radio-container"
        tooltip={!hasFullAccess ? {} : getFormTooltipDetail('oidcInputMethod')}
      >
        <RadioButtons
          list={OIDC_INPUT_METHOD_LIST}
          isSelected={isInputMethodSelected}
          onClick={onInputMethodClick}
          containerClass={!hasFullAccess ? 'disabled' : ''}
        />
      </Field>

      {renderURLModeContent()}

      {renderCommonFormContent()}
    </Card>
  );
};

OIDCConfigurationForm.defaultProps = defaultProps;

OIDCConfigurationForm.propTypes = {
  isEditMode: PropTypes.bool,
  formValues: PropTypes.object,
  onFormFieldChange: PropTypes.func,
  setFormValues: PropTypes.func,
  isActionChoosen: PropTypes.bool,
  setIsActionChoosen: PropTypes.func,
  activeConfigurationMode: PropTypes.string,
  setActiveConfigurationMode: PropTypes.func,
  validationDetail: PropTypes.object,
  privileges: PropTypes.object,
};

export default OIDCConfigurationForm;
