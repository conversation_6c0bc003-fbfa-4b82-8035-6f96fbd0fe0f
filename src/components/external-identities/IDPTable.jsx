import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faEye, faPencilAlt } from '@fortawesome/pro-solid-svg-icons';
import {
  Actions,
  Button,
  NoDataMessage,
  RowNumber,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { getList } from '../../ducks/external-identities';
import {
  selectPrimaryIdpTableConfig,
  selectPrimaryIdpTableDetail,
  selectSecondaryIdpTableConfig,
  selectSecondaryIdpTableDetail,
} from '../../ducks/external-identities/selectors';

import EIStatus from './EIStatus';

const defaultProps = {
  isIdpPrimary: false,
  onAddClick: noop,
  onEditClick: noop,
  onDeleteClick: noop,
  privileges: {},
};

const IDPTable = ({ isIdpPrimary, onAddClick, onEditClick, onDeleteClick, privileges }) => {
  const { t } = useTranslation();

  const { hasFullAccess } = privileges;

  const { apiCall } = useApiCall({});

  useEffect(() => {
    apiCall(getList({ defaultIdp: isIdpPrimary })).catch(noop);
  }, [isIdpPrimary]);

  const tableConfig = useSelector(
    isIdpPrimary ? selectPrimaryIdpTableConfig : selectSecondaryIdpTableConfig,
  );

  const tableDetail = useSelector(
    isIdpPrimary ? selectPrimaryIdpTableDetail : selectSecondaryIdpTableDetail,
  );

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'number') {
        columnDetail.cell = RowNumber;
      }
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            showDelete={hasFullAccess}
            editIcon={!hasFullAccess ? faEye : faPencilAlt}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      if (columnDetail.id === 'status') {
        columnDetail.cell = EIStatus;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, hasFullAccess]);

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(getList({ requireTotal: false, pageOffset: pageOffset + pageSize, pageSize })).catch(
      noop,
    );
  };

  const renderNoDataSection = (props) => {
    if (isIdpPrimary) {
      return (
        <NoDataMessage {...props}>
          <p className="is-flex has-jc-c has-ai-c" style={{ padding: '1rem' }}>
            <span className="typography-paragraph1">{t('PRIMARY_IDP_NOT_AVAILABLE')}</span>
            &nbsp;
            {hasFullAccess ? (
              <Button type="tertiary" containerClass="no-p-l" onClick={onAddClick}>
                {t('ADD_PRIMARY_IDP')}
              </Button>
            ) : null}
          </p>
        </NoDataMessage>
      );
    }

    return (
      <NoDataMessage containerClass="cell-container" {...props}>
        <p className="is-flex has-jc-c has-ai-c">
          <span className="typography-paragraph1">{t('SECONDARY_IDP_NOT_AVAILABLE')}</span>
        </p>
      </NoDataMessage>
    );
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
      hidePagination={isIdpPrimary}
      showFooter={!isIdpPrimary}
      renderNoDataSection={renderNoDataSection}
      containerClass={
        isIdpPrimary
          ? 'full-width primary-idp-table-container'
          : 'full-width secondary-idp-table-container'
      }
    />
  );
};

IDPTable.defaultProps = defaultProps;

IDPTable.propTypes = {
  isIdpPrimary: PropTypes.bool,
  onAddClick: PropTypes.func,
  onEditClick: PropTypes.func,
  onDeleteClick: PropTypes.func,
  privileges: PropTypes.object,
};

export default IDPTable;
