import { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  CHART_COLORS,
  CHART_CONFIG,
  CHART_MAX_DATA_POINTS,
  Checkbox,
  DashboardChart,
  DashboardChartContainer,
  Tab,
  Tabs,
  TooltipChartTime,
  getTimePayloadDetail,
  useApiCall,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { cloneDeep, filter, noop } from 'lodash-es';

import {
  getUserCreationEvents,
  getUserDeletionEvents,
  getUserModificationEvents,
} from '../../ducks/metrics';
import { METRIC_RESULT } from '../../ducks/metrics/constants';
import {
  selectUserCreationFailureEvents,
  selectUserDeletionFailureEvents,
  selectUserModificationFailureEvents,
} from '../../ducks/metrics/selectors';

import { DashboardContext } from './context/DashboardContextProvider';

const TABS = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
};

const UserManangementFailuresChartContainer = () => {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();

  const { timePayload, selectedTenant } = useContext(DashboardContext);

  const [selectedTab, setSelectedTab] = useState(TABS.CREATE);

  const userCreationFailureEvents = useSelector(selectUserCreationFailureEvents);
  const userModificationFailureEvents = useSelector(selectUserModificationFailureEvents);
  const userDeletionFailureEvents = useSelector(selectUserDeletionFailureEvents);

  const [chartData, setChartData] = useState(userCreationFailureEvents.formattedEntries);

  const [legends, setLegends] = useState(userCreationFailureEvents.legends);

  useEffect(() => {
    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.FIFTY,
    });

    delete timePayloadDetail.format;

    if (selectedTab === TABS.CREATE) {
      apiCall(
        getUserCreationEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.FAILURE,
        }),
      ).catch(noop);
    } else if (selectedTab === TABS.UPDATE) {
      apiCall(
        getUserModificationEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.FAILURE,
        }),
      ).catch(noop);
    } else if (selectedTab === TABS.DELETE) {
      apiCall(
        getUserDeletionEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.FAILURE,
        }),
      ).catch(noop);
    }
  }, [selectedTab, timePayload.startTime, timePayload.endTime, selectedTenant]);

  const chartConfig = useMemo(() => {
    let config = cloneDeep(CHART_CONFIG.LINE);

    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.FIFTY,
    });

    config.axisBottom.tickValues = 10;

    config.axisBottom.format = (value) => {
      return dayjs(value).format(timePayloadDetail.format);
    };

    config.sliceTooltip = (value) => (
      <TooltipChartTime legends={legends} xAxisSliceDetail={value} />
    );

    config.colors = filter(legends, { isSelected: true }).map((legend) => legend.color);

    return config;
  }, [timePayload.startTime, timePayload.endTime, legends, chartData]);

  useEffect(() => {
    if (selectedTab === TABS.CREATE) {
      setLegends(userCreationFailureEvents.legends);
    } else if (selectedTab === TABS.UPDATE) {
      setLegends(userModificationFailureEvents.legends);
    } else if (selectedTab === TABS.DELETE) {
      setLegends(userDeletionFailureEvents.legends);
    }
  }, [userCreationFailureEvents, userModificationFailureEvents, userDeletionFailureEvents]);

  useEffect(() => {
    const filteredEntries = [];
    let activeEntries = [];
    const legendsMapping = {};

    legends.forEach(({ id, isSelected }) => {
      legendsMapping[id] = isSelected;
    });

    if (selectedTab === TABS.CREATE) {
      activeEntries = userCreationFailureEvents.formattedEntries;
    } else if (selectedTab === TABS.UPDATE) {
      activeEntries = userModificationFailureEvents.formattedEntries;
    } else if (selectedTab === TABS.DELETE) {
      activeEntries = userDeletionFailureEvents.formattedEntries;
    }

    activeEntries.forEach((detail) => {
      if (legendsMapping[detail.id]) {
        filteredEntries.push(detail);
      }
    });

    setChartData(filteredEntries);
  }, [legends]);

  const renderTabsSection = () => {
    return (
      <Tabs containerStyle={{ marginBottom: '0' }}>
        <Tab
          label={t(TABS.CREATE)}
          isActive={selectedTab === TABS.CREATE}
          onClick={() => {
            setSelectedTab(TABS.CREATE);
          }}
        />

        <Tab
          label={t(TABS.UPDATE)}
          isActive={selectedTab === TABS.UPDATE}
          onClick={() => {
            setSelectedTab(TABS.UPDATE);
          }}
        />

        <Tab
          label={t(TABS.DELETE)}
          isActive={selectedTab === TABS.DELETE}
          onClick={() => {
            setSelectedTab(TABS.DELETE);
          }}
        />
      </Tabs>
    );
  };

  const onLegendClick = (detail) => {
    setLegends((prevState) => {
      return prevState.map((prevDetail) => {
        let newDetail = { ...prevDetail };

        if (newDetail.id === detail.id) {
          newDetail.isSelected = !newDetail.isSelected;
        }

        return newDetail;
      });
    });
  };

  const renderLegendsSection = () => {
    return legends.map((detail, idx) => {
      const { id, name, isSelected } = detail;

      return (
        <Checkbox
          key={id}
          name={`user-management-failure-${name}`}
          checked={isSelected}
          checkedColor={CHART_COLORS[idx]}
          text={t(name)}
          isLarge={false}
          onChange={() => onLegendClick(detail)}
        />
      );
    });
  };

  return (
    <DashboardChartContainer
      title={t('USER_MANAGEMENT_FAILURE')}
      renderTabsSection={renderTabsSection}
      renderLegendsSection={renderLegendsSection}
      containerStyle={{ maxWidth: '50%' }}
    >
      <DashboardChart chartType="LINE" {...chartConfig} data={chartData} />
    </DashboardChartContainer>
  );
};

export default UserManangementFailuresChartContainer;
