import { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  CHART_COLORS,
  CHART_CONFIG,
  CHART_MAX_DATA_POINTS,
  Checkbox,
  DashboardChart,
  DashboardChartContainer,
  Tab,
  Tabs,
  TooltipChartTime,
  getTimePayloadDetail,
  useApiCall,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { cloneDeep, filter, noop } from 'lodash-es';

import {
  getUserCreationEvents,
  getUserDeletionEvents,
  getUserModificationEvents,
} from '../../ducks/metrics';
import { METRIC_RESULT } from '../../ducks/metrics/constants';
import {
  selectUserCreationSuccessEvents,
  selectUserDeletionSuccessEvents,
  selectUserModificationSuccessEvents,
} from '../../ducks/metrics/selectors';

import { DashboardContext } from './context/DashboardContextProvider';

const TABS = {
  CREATED: 'CREATED',
  UPDATED: 'UPDATED',
  DELETED: 'DELETED',
};

const UserManangementChartContainer = () => {
  const { apiCall } = useApiCall();
  const { t } = useTranslation();

  const { timePayload, selectedTenant } = useContext(DashboardContext);

  const [selectedTab, setSelectedTab] = useState(TABS.CREATED);

  const userCreationSuccessEvents = useSelector(selectUserCreationSuccessEvents);
  const userModificationSuccessEvents = useSelector(selectUserModificationSuccessEvents);
  const userDeletionSuccessEvents = useSelector(selectUserDeletionSuccessEvents);

  const [chartData, setChartData] = useState(userCreationSuccessEvents.formattedEntries);
  const [legends, setLegends] = useState(userCreationSuccessEvents.legends);

  useEffect(() => {
    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.FIFTY,
    });

    delete timePayloadDetail.format;

    if (selectedTab === TABS.CREATED) {
      apiCall(
        getUserCreationEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.SUCCESS,
        }),
      ).catch(noop);
    } else if (selectedTab === TABS.UPDATED) {
      apiCall(
        getUserModificationEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.SUCCESS,
        }),
      ).catch(noop);
    } else if (selectedTab === TABS.DELETED) {
      apiCall(
        getUserDeletionEvents({
          tenantPseudoDomain: selectedTenant,
          ...timePayloadDetail,
          result: METRIC_RESULT.SUCCESS,
        }),
      ).catch(noop);
    }
  }, [selectedTab, timePayload.startTime, timePayload.endTime, selectedTenant]);

  const chartConfig = useMemo(() => {
    let config = cloneDeep(CHART_CONFIG.LINE);

    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.FIFTY,
    });

    config.axisBottom.tickValues = 10;

    config.axisBottom.format = (value) => {
      return dayjs(value).format(timePayloadDetail.format);
    };

    config.sliceTooltip = (value) => (
      <TooltipChartTime legends={legends} xAxisSliceDetail={value} />
    );

    config.colors = filter(legends, { isSelected: true }).map((legend) => legend.color);

    return config;
  }, [timePayload.startTime, timePayload.endTime, legends, chartData]);

  useEffect(() => {
    if (selectedTab === TABS.CREATED) {
      setLegends(userCreationSuccessEvents.legends);
    } else if (selectedTab === TABS.UPDATED) {
      setLegends(userModificationSuccessEvents.legends);
    } else if (selectedTab === TABS.DELETED) {
      setLegends(userDeletionSuccessEvents.legends);
    }
  }, [userCreationSuccessEvents, userModificationSuccessEvents, userDeletionSuccessEvents]);

  useEffect(() => {
    const filteredEntries = [];
    let activeEntries = [];
    const legendsMapping = {};

    legends.forEach(({ id, isSelected }) => {
      legendsMapping[id] = isSelected;
    });

    if (selectedTab === TABS.CREATED) {
      activeEntries = userCreationSuccessEvents.formattedEntries;
    } else if (selectedTab === TABS.UPDATED) {
      activeEntries = userModificationSuccessEvents.formattedEntries;
    } else if (selectedTab === TABS.DELETED) {
      activeEntries = userDeletionSuccessEvents.formattedEntries;
    }

    activeEntries.forEach((detail) => {
      if (legendsMapping[detail.id]) {
        filteredEntries.push(detail);
      }
    });

    setChartData(filteredEntries);
  }, [legends]);

  const renderTabsSection = () => {
    return (
      <Tabs containerStyle={{ marginBottom: '0' }}>
        <Tab
          label={t(TABS.CREATED)}
          isActive={selectedTab === TABS.CREATED}
          onClick={() => {
            setSelectedTab(TABS.CREATED);
          }}
        />

        <Tab
          label={t(TABS.UPDATED)}
          isActive={selectedTab === TABS.UPDATED}
          onClick={() => {
            setSelectedTab(TABS.UPDATED);
          }}
        />

        <Tab
          label={t(TABS.DELETED)}
          isActive={selectedTab === TABS.DELETED}
          onClick={() => {
            setSelectedTab(TABS.DELETED);
          }}
        />
      </Tabs>
    );
  };

  const onLegendClick = (detail) => {
    setLegends((prevState) => {
      return prevState.map((prevDetail) => {
        let newDetail = cloneDeep(prevDetail);

        if (newDetail.id === detail.id) {
          newDetail.isSelected = !newDetail.isSelected;
        }

        return newDetail;
      });
    });
  };

  const renderLegendsSection = () => {
    return legends.map((detail, idx) => {
      const { id, name, isSelected } = detail;

      return (
        <Checkbox
          key={id}
          name={`user-management-success-${name}`}
          checked={isSelected}
          checkedColor={CHART_COLORS[idx]}
          text={t(name)}
          isLarge={false}
          onChange={() => onLegendClick(detail)}
        />
      );
    });
  };

  return (
    <DashboardChartContainer
      title={t('USER_MANAGEMENT')}
      renderTabsSection={renderTabsSection}
      renderLegendsSection={renderLegendsSection}
      containerStyle={{ maxWidth: '50%' }}
    >
      <DashboardChart chartType="LINE" {...chartConfig} data={chartData} />
    </DashboardChartContainer>
  );
};

export default UserManangementChartContainer;
