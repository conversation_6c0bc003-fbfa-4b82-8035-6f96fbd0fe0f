import { useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  CHART_CONFIG,
  DashboardChart,
  DashboardChartContainer,
  TooltipChartUser,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import { getDeviceRegistrationEvents } from '../../ducks/metrics';
import { selectDeviceRegistrationEvents } from '../../ducks/metrics/selectors';

import { DashboardContext } from './context/DashboardContextProvider';

const DeviceRegistrationChartContainer = () => {
  const { apiCall } = useApiCall();

  const { timePayload, selectedTenant } = useContext(DashboardContext);

  const deviceRegistrationEvents = useSelector(selectDeviceRegistrationEvents);

  const [chartData, setChartData] = useState(deviceRegistrationEvents.formattedEntries);

  useEffect(() => {
    apiCall(
      getDeviceRegistrationEvents({ tenantPseudoDomain: selectedTenant, ...timePayload }),
    ).catch(noop);
  }, [timePayload.startTime, timePayload.endTime, selectedTenant]);

  useEffect(() => {
    setChartData(deviceRegistrationEvents.formattedEntries);
  }, [deviceRegistrationEvents]);

  const chartConfig = useMemo(() => {
    let config = cloneDeep(CHART_CONFIG.BAR);

    config.tooltip = (value) => <TooltipChartUser xAxisSliceDetail={value} />;

    return config;
  }, [timePayload.startTime, timePayload.endTime]);

  return (
    <DashboardChartContainer
      title="Service Authentications by Client Type"
      containerStyle={{ maxWidth: '50%' }}
    >
      <DashboardChart chartType="BAR" {...chartConfig} data={chartData} />
    </DashboardChartContainer>
  );
};

export default DeviceRegistrationChartContainer;
