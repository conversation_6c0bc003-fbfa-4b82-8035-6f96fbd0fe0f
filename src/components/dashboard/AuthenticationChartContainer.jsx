import { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  CHART_COLORS,
  CHART_CONFIG,
  CHART_MAX_DATA_POINTS,
  Checkbox,
  DashboardChart,
  DashboardChartContainer,
  Tab,
  Tabs,
  TooltipChartTime,
  getTimePayloadDetail,
  useApiCall,
} from '@zscaler/zui-component-library';

import dayjs from 'dayjs';
import { cloneDeep, filter, noop } from 'lodash-es';

import { getAuthenticationEvents } from '../../ducks/metrics';
import { METRIC_RESULT } from '../../ducks/metrics/constants';
import { selectAuthenticationEvents } from '../../ducks/metrics/selectors';

import { DashboardContext } from './context/DashboardContextProvider';

const TABS = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const LEGENDS_TABS = {
  EXTERNAL_IDP: 'EXTERNAL_IDP',
  HOSTED: 'HOSTED',
};

const AuthenticationChartContainer = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { timePayload, selectedTenant } = useContext(DashboardContext);

  const [selectedTab, setSelectedTab] = useState(TABS.SUCCESS);
  const [selectedLegendTab, setSelectedLegendTab] = useState(LEGENDS_TABS.EXTERNAL_IDP);

  const authenticationEvents = useSelector(selectAuthenticationEvents);

  const [chartData, setChartData] = useState(authenticationEvents.formattedEntries);
  const [legends, setLegends] = useState(authenticationEvents.legends);

  useEffect(() => {
    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.HUNDRED,
    });

    delete timePayloadDetail.format;

    if (selectedTab === TABS.SUCCESS) {
      apiCall(
        getAuthenticationEvents(
          {
            tenantPseudoDomain: selectedTenant,
            ...timePayloadDetail,
            result: METRIC_RESULT.SUCCESS,
          },
          selectedLegendTab,
        ),
      ).catch(noop);
    } else if (selectedTab === TABS.FAILURE) {
      apiCall(
        getAuthenticationEvents(
          {
            tenantPseudoDomain: selectedTenant,
            ...timePayloadDetail,
            result: METRIC_RESULT.FAILURE,
          },
          selectedLegendTab,
        ),
      ).catch(noop);
    }
  }, [selectedTab, timePayload.startTime, timePayload.endTime, selectedTenant, selectedLegendTab]);

  const chartConfig = useMemo(() => {
    let config = cloneDeep(CHART_CONFIG.LINE);

    const timePayloadDetail = getTimePayloadDetail({
      selectedTimeRange: timePayload,
      maxEntries: CHART_MAX_DATA_POINTS.HUNDRED,
    });

    config.axisBottom.format = (value) => {
      return dayjs(value).format(timePayloadDetail.format);
    };

    config.sliceTooltip = (value) => (
      <TooltipChartTime legends={legends} xAxisSliceDetail={value} />
    );

    config.colors = filter(legends, { isSelected: true }).map((legend) => legend.color);

    return config;
  }, [timePayload.startTime, timePayload.endTime, legends, chartData]);

  useEffect(() => {
    setLegends(authenticationEvents.legends);
  }, [authenticationEvents]);

  useEffect(() => {
    const filteredEntries = [];
    let activeEntries = [];
    const legendsMapping = {};

    legends.forEach(({ id, isSelected }) => {
      legendsMapping[id] = isSelected;
    });

    activeEntries = authenticationEvents.formattedEntries;

    activeEntries.forEach((detail) => {
      if (legendsMapping[detail.id]) {
        filteredEntries.push(detail);
      }
    });

    setChartData(filteredEntries);
  }, [legends]);

  const renderTabsSection = () => {
    return (
      <Tabs containerStyle={{ marginBottom: '0' }}>
        <Tab
          label={t(TABS.SUCCESS)}
          isActive={selectedTab === TABS.SUCCESS}
          onClick={() => {
            setSelectedTab(TABS.SUCCESS);
          }}
        />

        <Tab
          label={t(TABS.FAILURE)}
          isActive={selectedTab === TABS.FAILURE}
          onClick={() => {
            setSelectedTab(TABS.FAILURE);
          }}
        />
      </Tabs>
    );
  };

  const onLegendClick = (detail) => {
    setLegends((prevState) => {
      return prevState.map((prevDetail) => {
        let newDetail = cloneDeep(prevDetail);

        if (newDetail.id === detail.id) {
          newDetail.isSelected = !newDetail.isSelected;
        }

        return newDetail;
      });
    });
  };

  const renderLegendsSection = () => {
    return legends.map((detail, idx) => {
      const { id, name, isSelected } = detail;

      return (
        <Checkbox
          key={id}
          name={`authentication-${name}`}
          checked={isSelected}
          checkedColor={CHART_COLORS[idx]}
          text={t(name)}
          isLarge={false}
          onChange={() => onLegendClick(detail)}
        />
      );
    });
  };

  const renderLegendsTabSection = () => {
    return (
      <>
        <Tabs containerStyle={{ marginBottom: '0' }}>
          <Tab
            label={t(LEGENDS_TABS.EXTERNAL_IDP)}
            isActive={selectedLegendTab === LEGENDS_TABS.EXTERNAL_IDP}
            onClick={() => {
              setSelectedLegendTab(LEGENDS_TABS.EXTERNAL_IDP);
            }}
          />

          <Tab
            label={t(LEGENDS_TABS.HOSTED)}
            isActive={selectedLegendTab === LEGENDS_TABS.HOSTED}
            onClick={() => {
              setSelectedLegendTab(LEGENDS_TABS.HOSTED);
            }}
          />
        </Tabs>

        <div style={{ marginTop: '2rem' }}>{renderLegendsSection()}</div>
      </>
    );
  };

  return (
    <DashboardChartContainer
      title={t('AUTHENTICATION')}
      renderTabsSection={renderTabsSection}
      renderLegendsSection={renderLegendsTabSection}
    >
      <DashboardChart chartType="LINE" {...chartConfig} data={chartData} />
    </DashboardChartContainer>
  );
};

export default AuthenticationChartContainer;
