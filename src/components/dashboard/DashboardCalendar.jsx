import { useContext } from 'react';

import { CalendarDropDown } from '@zscaler/zui-component-library';

import { CALENDAR_DD_LEVEL, DashboardContext } from './context/DashboardContextProvider';

const DashboardCalendar = () => {
  const { selectedTimeRange, setSelectedTimeRange, calendarConfig, formatPattern } =
    useContext(DashboardContext);

  return (
    <CalendarDropDown
      level={CALENDAR_DD_LEVEL}
      selectedList={selectedTimeRange}
      setSelectedList={setSelectedTimeRange}
      kind="secondary"
      config={calendarConfig}
      formatPattern={formatPattern}
      containerStyle={{ maxWidth: '500px' }}
    />
  );
};

export default DashboardCalendar;
