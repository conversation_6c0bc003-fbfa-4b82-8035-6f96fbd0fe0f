import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { faUser } from '@fortawesome/pro-regular-svg-icons';
import { DashboardInfoCard, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getDashboardCards } from '../../ducks/metrics';
import { selectDashboardCards } from '../../ducks/metrics/selectors';

import GroupIcon from '../../images/Group_Icon.svg';
import SyncIcon from '../../images/Sync.svg';

const DASHBOARD_CARDS_IDS = {
  REGISTERED_USERS: 'REGISTERED_USERS',
  IDP_USERS: 'IDP_USERS',
  HOSTED_USERS: 'HOSTED_USERS',
  SERVICES: 'SERVICES',
};

const getIconDetail = (id) => {
  if (id === DASHBOARD_CARDS_IDS.REGISTERED_USERS) {
    return { icon: GroupIcon, iconIsImage: true };
  } else if (id === DASHBOARD_CARDS_IDS.SERVICES) {
    return { icon: SyncIcon, iconIsImage: true };
  }

  return {
    icon: faUser,
    iconIsImage: false,
    iconProps: {
      size: '1x',
      className: 'icon-container',
      border: true,
      style: { padding: '0.6rem 0.8rem', borderRadius: '50%' },
    },
  };
};

const InfoCardContainer = () => {
  const { apiCall } = useApiCall();

  const dashboardCards = useSelector(selectDashboardCards);

  useEffect(() => {
    apiCall(getDashboardCards()).catch(noop);
  }, []);

  return (
    <div className="info-card-container is-flex" style={{ gap: '12px' }}>
      {dashboardCards.map(({ id, label, data } = {}) => {
        return <DashboardInfoCard {...getIconDetail(id)} key={id} label={label} list={data} />;
      })}
    </div>
  );
};

export default InfoCardContainer;
