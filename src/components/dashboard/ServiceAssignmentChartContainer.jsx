import { useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  CHART_CONFIG,
  DashboardChart,
  DashboardChartContainer,
  TooltipChartUser,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, noop } from 'lodash-es';

import { getServiceAssignmentEvents } from '../../ducks/metrics';
import { selectServiceAssignmentEvents } from '../../ducks/metrics/selectors';

import { DashboardContext } from './context/DashboardContextProvider';

const ServiceAssignmentChartContainer = () => {
  const { apiCall } = useApiCall();

  const { timePayload, selectedTenant } = useContext(DashboardContext);

  const serviceAssignmentEvents = useSelector(selectServiceAssignmentEvents);

  const [chartData, setChartData] = useState(serviceAssignmentEvents.formattedEntries);

  useEffect(() => {
    apiCall(
      getServiceAssignmentEvents({ tenantPseudoDomain: selectedTenant, ...timePayload }),
    ).catch(noop);
  }, [timePayload.startTime, timePayload.endTime, selectedTenant]);

  useEffect(() => {
    setChartData(serviceAssignmentEvents.formattedEntries);
  }, [serviceAssignmentEvents]);

  const chartConfig = useMemo(() => {
    let config = cloneDeep(CHART_CONFIG.BAR);

    config.tooltip = (value) => <TooltipChartUser xAxisSliceDetail={value} />;

    return config;
  }, [timePayload.startTime, timePayload.endTime]);

  return (
    <DashboardChartContainer title="Users Enrolled via ZCC" containerStyle={{ maxWidth: '50%' }}>
      <DashboardChart chartType="BAR" {...chartConfig} data={chartData} />
    </DashboardChartContainer>
  );
};

export default ServiceAssignmentChartContainer;
