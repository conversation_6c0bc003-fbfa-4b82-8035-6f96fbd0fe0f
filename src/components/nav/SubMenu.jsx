import { useLayoutEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import PropTypes from 'prop-types';

import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';

import MenuLink from './MenuLink';

const defaultProps = {
  label: '',
  links: [],
};

const LINKS_CONTAINER_CLASS = 'links-container';

const SubMenu = ({ label, links }) => {
  const ref = useRef(null);
  const { t } = useTranslation();

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  useLayoutEffect(() => {
    const childCount = ref.current.querySelector(`.${LINKS_CONTAINER_CLASS}`).children.length;

    if (childCount == 0) {
      ref.current.style.display = 'none';
    } else {
      ref.current.style.display = 'flex';
    }
  }, [isUserSSOEnabled]);

  return (
    <div className="sub-menu-container" ref={ref}>
      <div className="label-section">{t(label)}</div>

      <div className={`${LINKS_CONTAINER_CLASS}`}>
        {links?.map((linkObj) => (
          <MenuLink key={linkObj.linkTo} {...linkObj} />
        ))}
      </div>
    </div>
  );
};

SubMenu.defaultProps = defaultProps;

SubMenu.propTypes = {
  label: PropTypes.string,
  links: PropTypes.arrayOf(Object),
};

export default SubMenu;
