import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';

import PropTypes from 'prop-types';

import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY } from '../../config';

const defaultProps = {
  label: '',
  links: [],
  skipPermissionCheck: false,
};

const MenuLink = ({ linkTo, label, page, skipPermissionCheck }) => {
  const { t } = useTranslation();
  const location = useLocation();

  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (linkTo && linkTo !== '/') {
      const pathnameIndex = location.pathname.indexOf(linkTo);

      if (pathnameIndex === 0) {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  }, [location]);

  const { noAccess } = useSelector(selectPermissionsByKey(page));

  const { noAccess: ipLocationNoAccess } = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.IP_LOCATION_POLICY),
  );

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  //exceptions
  if (ipLocationNoAccess && linkTo.includes('policy/signon')) {
    return null;
  }

  if (!isUserSSOEnabled && page === PERMISSIONS_KEY.SERVICE_ENTITLEMENTS_POLICY) {
    return null;
  }

  if (noAccess && !skipPermissionCheck) {
    return null;
  }

  return (
    <Link key={linkTo} to={linkTo} className={`link ${isActive ? 'active' : ''}`}>
      {t(label)}
    </Link>
  );
};

MenuLink.defaultProps = defaultProps;

MenuLink.propTypes = {
  linkTo: PropTypes.string,
  label: PropTypes.string,
  page: PropTypes.string,
  skipPermissionCheck: PropTypes.bool,
};

export default MenuLink;
