import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY } from '../../config';
import SubMenu from './SubMenu';

const defaultProps = {
  linkTo: null,
  onClick: noop,
  icon: null,
  isIconImage: false,
  hasSmallIcon: false,
  iconClass: '',
  iconStyle: {},
  label: '',
  subMenu: [],
  containerClass: '',
  containerStyle: {},
};

const Menu = ({
  linkTo,
  onClick,
  icon,
  isIconImage,
  hasSmallIcon,
  iconClass,
  iconStyle,
  label,
  subMenu,
  containerClass,
  containerStyle,
}) => {
  const { t } = useTranslation();
  const location = useLocation();

  const { noAccess: passwordPageNoAccess } = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_METHODS),
  );

  const { noAccess: signOnPolicyPageNoAccess } = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.SIGN_ON_POLICY),
  );

  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (linkTo && linkTo !== '/') {
      const pathnameIndex = location.pathname.indexOf(linkTo.split('/')[1]);

      if (pathnameIndex !== -1) {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  }, [location]);

  const hasSubMenu = subMenu?.length > 0;

  if (signOnPolicyPageNoAccess && passwordPageNoAccess && label == 'POLICY') {
    return null;
  }

  return (
    <div
      className={`menu-container ${containerClass} ${isActive ? 'active' : ''}`}
      style={containerStyle}
    >
      <Link to={linkTo} onClick={onClick} className={`menu ${containerClass}`}>
        {isIconImage ? (
          <img
            src={icon}
            className={`icon ${hasSmallIcon ? 'small' : ''} ${iconClass}`}
            alt="nav image"
            style={iconStyle}
          />
        ) : (
          <FontAwesomeIcon
            icon={icon}
            className={`icon ${hasSmallIcon ? 'small' : ''} ${iconClass}`}
            style={iconStyle}
          />
        )}
        {label ? <span className="typography-paragraph1">{t(label)}</span> : null}
      </Link>

      {hasSubMenu && (
        <div className="sub-menu-list-container">
          {subMenu?.map((menuDetail) => (
            <SubMenu key={menuDetail.label} {...menuDetail} />
          ))}
        </div>
      )}
    </div>
  );
};

Menu.defaultProps = defaultProps;

Menu.propTypes = {
  linkTo: PropTypes.string,
  onClick: PropTypes.func,
  icon: PropTypes.any,
  isIconImage: PropTypes.bool,
  hasSmallIcon: PropTypes.bool,
  iconClass: PropTypes.string,
  iconStyle: PropTypes.object,
  label: PropTypes.string,
  subMenu: PropTypes.arrayOf(Object),
  containerClass: PropTypes.string,
  containerStyle: PropTypes.object,
};

export default Menu;
