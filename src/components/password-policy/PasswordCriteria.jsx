import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { faChevronDown, faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Card,
  Field,
  FieldGroup,
  Input,
  ToggleButton,
  defaultFormPropTypes,
  defaultFormProps,
  mergeFormValues,
  replaceNonNumericDigit,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';
import PropTypes from 'prop-types';

const defaultProps = {
  ...defaultFormProps,
  formValues: { expiryAge: 15 },
  setFormValues: noop,
  setIsPolicyUpdated: noop,
  isRecommended: true,
  hasFullAccess: false,
};

const PasswordCriteria = ({
  formValues,
  setFormValues,
  validationDetail,
  setIsPolicyUpdated,
  isRecommended,
  hasFullAccess,
}) => {
  const { t } = useTranslation();

  const [showCriteriaDetail, setShowCriteriaDetail] = useState(true);

  const onToggleClick = (formName) => {
    setFormValues((prevState) => ({ ...prevState, [formName]: !prevState[formName] }));

    setIsPolicyUpdated(true);
  };

  const onFormFieldChange = (evt) => {
    const { name, value } = evt?.target || {};

    if (name == 'expiryAge') {
      const inputNum = replaceNonNumericDigit({ value, allowDecimal: false });

      setFormValues((prevState) => {
        const newState = { ...prevState, [name]: inputNum };

        return newState;
      });
    } else {
      setFormValues(mergeFormValues(evt));
    }

    setIsPolicyUpdated(true);
  };

  return (
    <>
      <section
        className="typography-paragraph1-uppercase collapsable"
        onKeyDown={noop}
        onClick={() => {
          setShowCriteriaDetail((prevState) => !prevState);
        }}
      >
        <FontAwesomeIcon
          className="icon left"
          icon={showCriteriaDetail ? faChevronDown : faChevronUp}
        />

        <span>{t('PASSWORD_CRITERIA')}</span>
      </section>

      {showCriteriaDetail && (
        <Card>
          <FieldGroup>
            <Field
              label="PASSWORD_DOES_NOT_INCLUDE"
              containerClass="field-stacked"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Turn on to restrict the user from including their company name, username, first name, or last name in their password',
                    }
              }
            >
              <ToggleButton
                isOn={formValues.excludeNames}
                onToggleClick={() => {
                  onToggleClick('excludeNames');
                }}
                showLabel={false}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field
              label="REJECT_REUSE"
              containerClass="field-stacked"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content: 'Turn on to restrict users from reusing their last 5 passwords',
                    }
              }
            >
              <ToggleButton
                isOn={formValues.disallowRecentPassword}
                onToggleClick={() => {
                  onToggleClick('disallowRecentPassword');
                }}
                showLabel={false}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field
              label="DEACTIVATE_USER"
              containerClass="field-stacked"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Turn on to deactivate the user account after 10 unsuccessful login attempts',
                    }
              }
            >
              <ToggleButton
                isOn={formValues.deactivateAfterUnsuccessfulAttempts}
                onToggleClick={() => {
                  onToggleClick('deactivateAfterUnsuccessfulAttempts');
                }}
                showLabel={false}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field
              label="ALLOW_ADMIN_SET_PASSWORD"
              containerClass="field-stacked"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        "Turn on to allow the administrator to create or change user's password",
                    }
              }
            >
              <ToggleButton
                isOn={formValues.allowAdminSetPasswords}
                onToggleClick={() => {
                  onToggleClick('allowAdminSetPasswords');
                }}
                showLabel={false}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field
              label="FORCE_PASSWORD_CHANGE"
              containerClass="field-stacked"
              tooltip={
                !hasFullAccess
                  ? {}
                  : {
                      content:
                        'Turn on to prompt users to change their password after the initial login',
                    }
              }
            >
              <ToggleButton
                isOn={formValues.forcePasswordChange}
                onToggleClick={() => {
                  onToggleClick('forcePasswordChange');
                }}
                showLabel={false}
                disabled={isRecommended || !hasFullAccess}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            {isRecommended ? (
              <Field
                label="PASSWORD_EXPIRY"
                containerClass="field-stacked expiry-age"
                tooltip={
                  !hasFullAccess
                    ? {}
                    : {
                        content:
                          'Set the password expiration period after which users can reset their passwords. You can set the expiration period from 15 to 365 days.',
                      }
                }
              >
                {formValues.expiryAge}
              </Field>
            ) : (
              <Input
                label="PASSWORD_EXPIRY"
                name="expiryAge"
                value={formValues.expiryAge}
                onChange={onFormFieldChange}
                info={validationDetail}
                containerClass="field-stacked expiry-age"
                tooltip={
                  !hasFullAccess
                    ? {}
                    : {
                        content:
                          'Set the password expiration period after which users can reset their passwords. You can set the expiration period from 15 to 365 days.',
                      }
                }
                disabled={!hasFullAccess}
              />
            )}
          </FieldGroup>
        </Card>
      )}
    </>
  );
};

PasswordCriteria.defaultProps = defaultProps;

PasswordCriteria.propTypes = {
  ...defaultFormPropTypes,
  formValues: PropTypes.object,
  setFormValues: PropTypes.func,
  setShowUpdateSection: PropTypes.func,
  isRecommended: PropTypes.bool,
  hasFullAccess: PropTypes.bool,
};

export default PasswordCriteria;
