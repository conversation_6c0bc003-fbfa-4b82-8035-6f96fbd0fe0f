import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const searchOptions = [
  { label: 'NAME', value: 'NAME' },
  { label: 'GROUP', value: 'GROUP' },
];

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'API_RESOURCE',
  },
  add: {
    headerText: 'ADD_API_RESOURCE',
  },
  edit: {
    headerText: 'EDIT_API_RESOURCE',
  },
  delete: {
    headerText: 'DELETE_API_RESOURCE',
    confirmationMessage: 'DELETE_API_RESOURCE_MESSAGE',
  },
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'NAME_REQUIRED_MESSAGE';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the API resource`;
  }

  if (name === 'primaryAud') {
    tooltipDetail.content = `Enter audience for the API resource`;
  }

  return tooltipDetail;
};
