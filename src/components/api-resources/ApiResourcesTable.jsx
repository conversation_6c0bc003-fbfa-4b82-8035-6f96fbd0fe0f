import { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { faEye } from '@fortawesome/pro-solid-svg-icons';
import { Actions, TableContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/api-resources';
import { selectTableConfig, selectTableDetail } from '../../ducks/api-resources/selectors';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiResourcesTable = () => {
  const { apiCall } = useApiCall();

  const { setModalMode, setDetail, searchTerm, isFormReadOnly } = useContext(CRUDPageContext);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'actions') {
        const ActionsCellComponent = (props) => (
          <Actions
            {...props}
            onEditClick={onEditClick}
            onDeleteClick={onDeleteClick}
            editIcon={faEye}
            showDelete={false}
          />
        );

        columnDetail.cell = ActionsCellComponent;
      }

      return columnDetail;
    });

    if (isFormReadOnly) {
      return [...(tableConfig?.columns || [])];
    }

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, isFormReadOnly]);

  const onEditClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('view');
    }
  };

  const onDeleteClick = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchTerm) {
      payload.name = searchTerm;
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <TableContainer
      {...tableConfig}
      columns={tableColumnConfig}
      data={tableDetail.data}
      pagination={{ ...tableDetail, onLoadMoreClick }}
    />
  );
};

export default ApiResourcesTable;
