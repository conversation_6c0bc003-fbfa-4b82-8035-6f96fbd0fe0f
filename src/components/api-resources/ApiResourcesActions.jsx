import { useContext } from 'react';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Search, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getList } from '../../ducks/api-resources';

import { CRUDPageContext } from '../../contexts/CRUDPageContextProvider';

const ApiResourcesAction = () => {
  const { apiCall } = useApiCall();

  const { searchTerm, setSearchTerm } = useContext(CRUDPageContext);

  const onRefreshClick = () => {
    apiCall(getList()).catch(noop);
    setSearchTerm('');
  };

  const onSearchEnter = (term) => {
    apiCall(getList({ name: term })).catch(noop);

    setSearchTerm(term);
  };

  return (
    <div className="is-flex full-width has-jc-e">
      <div className="buttons">
        <Button
          type="tertiary"
          onClick={onRefreshClick}
          style={{ minWidth: '0px', paddingRight: '0px' }}
        >
          <FontAwesomeIcon icon={faSync} />
        </Button>

        <Search onSearch={onSearchEnter} term={searchTerm} containerStyle={{ width: '260px' }} />
      </div>
    </div>
  );
};

export default ApiResourcesAction;
