import '@testing-library/jest-dom/extend-expect';
import { fireEvent, render } from '@testing-library/react';

import StepsWizard from './StepsWizard';

describe('StepsWizard Component', () => {
  const defaultProps = {
    alignment: 'vertical',
    list: [
      { value: 'step1', label: 'Step 1' },
      { value: 'step2', label: 'Step 2' },
    ],
    selectedItem: { value: 'step1', label: 'Step 1' },
    onSelection: jest.fn(),
    stateConfig: {
      disabled: { icon: {}, color: '#8590A6' },
      active: { icon: {}, color: '#2160E1' },
      unselected: { icon: {}, color: '#8590A6' },
      success: { icon: {}, color: 'green' },
      warning: { icon: {}, color: 'orange' },
      error: { icon: {}, color: 'red' },
    },
    containerClass: '',
    containerStyle: {},
    children: <div>Content</div>,
  };

  it('renders without crashing', () => {
    const { container } = render(<StepsWizard {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });

  it('renders the correct number of steps', () => {
    const { getAllByText } = render(<StepsWizard {...defaultProps} />);
    expect(getAllByText(/Step/).length).toBe(2);
  });

  it('applies the selected class to the selected step', () => {
    const { getByText } = render(<StepsWizard {...defaultProps} />);
    expect(getByText('Step 1')).toHaveClass('selected');
  });

  it('calls onSelection when a step is clicked', () => {
    const { getByText } = render(<StepsWizard {...defaultProps} />);
    fireEvent.click(getByText('Step 2'));
    expect(defaultProps.onSelection).toHaveBeenCalledWith({ value: 'step2', label: 'Step 2' });
  });

  it('renders children content', () => {
    const { getByText } = render(<StepsWizard {...defaultProps} />);
    expect(getByText('Content')).toBeInTheDocument();
  });
});
