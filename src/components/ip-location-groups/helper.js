import { defaultValidationDetail } from '@zscaler/zui-component-library';

export const modalModeDetail = {
  '': {},
  view: {
    headerText: 'LOCATION_GROUP',
  },
  add: {
    headerText: 'ADD_LOCATION_GROUP',
  },
  edit: {
    headerText: 'EDIT_LOCATION_GROUP',
  },
  delete: {
    headerText: 'DELETE_LOCATION_GROUP',
    confirmationMessage: 'DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE',
  },
  bulkDelete: {
    headerText: 'BULK_DELETE',
    confirmationMessage: 'BULK_DELETE_LOCATION_GROUP_CONFIRMATION_MESSAGE',
  },
  importFile: {
    headerText: 'IMPORT_LOCATION_GROUP',
  },
};

export const bulkActionOptions = [{ label: 'DELETE', value: 'DELETE' }];

export const defaultBulkActionOption = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { name, locations } = formValues || {};

  if (!name) {
    validationDetail.isValid = false;
    validationDetail.context = 'name';
    validationDetail.type = 'error';
    validationDetail.message = 'NAME_REQUIRED_MESSAGE';

    return validationDetail;
  }

  if (locations?.length === 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'locations';
    validationDetail.type = 'error';
    validationDetail.message = 'LOCATIONS_REQUIRED_MESSAGE';

    return validationDetail;
  }

  return validationDetail;
};

export const getFormTooltipDetail = (name) => {
  const tooltipDetail = {
    content: '',
  };

  if (name === 'name') {
    tooltipDetail.content = `Enter a name for the location group`;
  }

  if (name === 'locations') {
    tooltipDetail.content = (
      <p>
        Select the locations that you want to assign to this group. Click the checkbox against the
        location to add them to the group. Click <strong className="tooltip-bold">X</strong> to
        remove a location or <strong className="tooltip-bold">Clear All</strong> to remove all
        selections
      </p>
    );
  }

  if (name === 'description') {
    tooltipDetail.content = `(Optional) Enter additional notes or information. Comments cannot exceed 512 characters.`;
  }

  if (name === 'overrideExistingEntries') {
    tooltipDetail.content = (
      <p>
        Enable this option if you want to update your existing locations, delete existing locations,
        or add new locations. If you only want to add new location groups, Zscaler doesn&apos;t
        recommend selecting this option. To learn more, see{' '}
        <a href="https://help.zscaler.com/zslogin/importing-ip-location-groups-csv-file">
          Importing IP Location Groups from a CSV File
        </a>
        .
      </p>
    );
  }

  if (name === 'csvFile') {
    tooltipDetail.content = (
      <p>
        Click <strong className="tooltip-bold">Browse File</strong> and select the CSV file you want
        to import.
      </p>
    );
  }

  return tooltipDetail;
};
