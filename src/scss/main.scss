@charset "UTF-8";

// 1. Configuration and helpers
// helpers from vendor or common page

// 2. Vendors
@import './vendors/zui-component-library';

// 3. Base stuff
// @import 'base/fonts';
// './base/typography';

// 4. Layout-related sections
@import './layout/banner';
@import './layout/footer';
@import './layout/header';
@import './layout/nav';

// 5. Components
@import './components/logo';
@import './components/small-card';
@import './components/user-form';

// 6. Page-specific styles
@import './pages/common-page';

@import './pages/access_policy';
@import './pages/admin-advanced-settings';
@import './pages/admin-domains';
@import './pages/admin-authentication-methods';
@import './pages/admin-my-profile';
@import './pages/admin-services';
@import './pages/admin-api-clients';
@import './pages/admin-tokens';
@import './pages/audit-logs';
@import './pages/authentication-levels';
@import './pages/branding';
@import './pages/device_token';
@import './pages/entitlements';
@import './pages/external-identities';
@import './pages/password-policy';
@import './pages/remote-assistance';
@import './pages/signon-policy';
@import './pages/signup';
@import './pages/roles';
@import './pages/users';
@import './pages/token-validators';

// 7. Themes
