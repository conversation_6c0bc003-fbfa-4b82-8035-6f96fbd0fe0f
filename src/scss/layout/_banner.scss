#banner-layout {
  position: fixed;
  left: 0;
  width: 100%;

  z-index: 1;

  .ui-banner-container {
    height: 26px;

    .description {
      color: var(--semantic-color-content-status-danger-primary, #8e201c);
      font-weight: bold;
      font-size: 1.6rem;

      margin-left: 100px;
    }
  }

  .migration-banner-container {
    width: 100%;
    height: 26px;

    padding: 2px 12px;

    background-color: var(--semantic-color-surface-severity-medium-active, #fedec0);

    .link {
      color: var(--semantic-color-content-interactive-primary-default, #2160e1);

      cursor: pointer;
    }
  }
}

.migrate-admin-modal-container {
  .modal-content-container {
    width: 624px;

    .field {
      margin-top: 0;
      margin-bottom: 16px;
    }

    .admin-migration-warning {
      display: flex;
      padding: 10px 16px;
      gap: 8px;
      margin-bottom: 15px;

      border-radius: 8px;
      border: 1px solid var(--semantic-color-border-status-warning-default, #fbad7f);
      background: var(--semantic-color-surface-base-primary, #fff);

      color: var(--semantic-color-content-base-secondary, #4a5468);

      @extend .typography-paragraph1;
    }
  }
}
