#password-policy {
  padding-bottom: 152px;

  .typography-paragraph1-uppercase {
    margin-top: 24px;
    margin-bottom: 8px;

    &:first-of-type {
      margin-top: 0px;
    }
  }
  .card {
    padding-top: 0px;

    // border: 1px solid $color-light-gray-border;
    border-radius: 5px;

    @extend .shadow-xs;

    &.configuration-type {
      .field {
        flex-grow: 0;
        min-width: auto;
      }

      .toast {
        margin-top: 20px;
        margin-bottom: 0px;
        margin-left: 24px;
        height: 64px;
      }
    }

    &.label-tooltip-container {
      padding-top: 12px;
    }
  }

  .collapsable {
    cursor: pointer;
  }

  .input {
    width: 260px;
  }

  .field {
    margin-bottom: 0;

    &.field-stacked {
      align-items: center;

      .label {
        margin-top: 8px;
        width: 600px;
      }

      .selected-items {
        min-width: 60px;

        &.disabled {
          // background-color: $color-lighter-gray-background;
        }
      }
    }
  }

  .buttons {
    padding: 12px 20px;
    margin-bottom: 0;
    justify-content: flex-start;
  }

  .selected-items {
    &.disabled {
      cursor: default;
      // color: $color-darker-gray-text;
      background-color: transparent;
      border-color: transparent;

      .right-content {
        display: none;
      }
    }
  }

  .field.expiry-age {
    .label {
      flex-shrink: 0;
    }

    .input {
      width: 80px;
    }
  }
}
.info-detail-container {
  white-space: pre-wrap;
  word-break: break-word;
}
.config-buttons {
  .button {
    width: 162px;

    &:not(.primary) {
      // background-color: $color-lighter-gray-background;
    }
  }
}
