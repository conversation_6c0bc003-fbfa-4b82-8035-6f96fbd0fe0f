#admin-services {
  .table-container {
    height: calc(100vh - 166px);
  }

  .services-users-groups-container {
    // background-color: $color-lightest-blue-background;
    padding: 2rem 2rem 4rem 4rem;

    .search-section {
      padding-bottom: 2rem;

      .search-container {
        margin-right: 1rem;
      }
    }

    .table-container {
      margin: 0rem -2rem 0 -4rem;

      height: auto;

      &.has-fixed-height {
        height: 350px;
      }

      &,
      * {
        border: none;
        // background-color: $color-lightest-blue-background;
      }
    }

    .body-container {
      .row-container:not(:last-of-type) {
        border-bottom: 1px solid white;
      }
    }

    .table-footer-container {
      padding-left: 4rem;
    }
  }
}

.roles-section {
  margin-top: -1rem;
  width: 100%;

  .table-container {
    max-height: 400px;
    height: 300px;

    .body-container {
      height: calc(100% - 45px);
    }
  }
}

.roles-detail {
  display: flex;
  flex-flow: column wrap;

  .detail {
    display: flex;
    flex-flow: column wrap;
  }

  .heading {
    .resource-name,
    .role-name {
      // font-weight: $font-medium-heading-font-weight;
    }
  }

  .resource-name,
  .role-name {
    width: 50%;
    padding-bottom: 14px;
    padding-right: 14px;
  }
}

.service-modal-container {
  .typography-paragraph1-uppercase {
    margin-top: 24px;
    margin-bottom: 8px;

    &:first-of-type {
      margin-top: 0px;
    }
  }

  .field {
    margin-top: 0px;
  }

  &.add {
    .field {
      &:last-of-type {
        margin-right: 0;
      }
    }

    .modal-body-container {
      .stepper-container {
        margin: -20px;
        margin-bottom: 1px;
      }
    }
  }
}

.stepper-container {
  display: flex;

  .step {
    display: flex;
    justify-content: center;
    align-items: center;

    position: relative;

    width: 100%;
    height: 44px;

    // background-color: $color-light-gray-border;
    color: var(--semantic-color-content-base-secondary, #4a5468);

    &::after {
      content: '';
      position: absolute;
      right: 0;
      width: 0;
      height: 0;
      margin-right: -35px;
      border: 25px solid transparent;
      // border-left: 10px solid $color-light-gray-border;
      z-index: 1;
    }

    .step-no {
      display: flex;
      justify-content: center;
      align-items: center;

      height: 20px;
      width: 20px;
      margin-right: 12px;

      // color: $color-white-background;
      // background-color: $color-dark-gray-light-text;

      border-radius: 50%;
    }

    &.active {
      // color: $color-darkest-gray-text;
      // background-color: $color-body-background;

      &::after {
        // border-left-color: $color-body-background;
      }

      .step-no {
        // background-color: $color-darker-gray-text;
      }
    }

    &.done {
      // color: $color-standard-green-text;
      // background-color: $color-lightest-green-label;

      &::after {
        // border-left-color: $color-lightest-green-label;
      }

      .step-no {
        // background-color: $color-standard-green-text;
      }
    }
  }
}

.scope-role-container {
  // background-color: $color-lightest-blue-background;
  padding: 2rem;
  padding-left: 3rem;
}
