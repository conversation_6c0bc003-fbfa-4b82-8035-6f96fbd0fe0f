.modal-container-roles {
  .modal-body-container {
    .info-icon {
      // color: $color-standard-blue-text;
    }

    .radio-buttons-container {
      .is-hidden {
        pointer-events: none;
        cursor: not-allowed;
        opacity: 0.4;
      }
      &.disabled {
        cursor: unset;
        pointer-events: none;
        .button.primary {
          // background-color: $color-darker-gray-text;
        }
      }

      &.view {
        .button:nth-of-type(1) {
          @extend .is-hidden;
        }
      }
      &.restricted-view {
        .button:nth-of-type(1) {
          @extend .is-hidden;
        }
        .button:nth-of-type(2) {
          @extend .is-hidden;
        }
        .button:nth-of-type(3) {
          @extend .is-hidden;
        }
      }
      &.none {
        .button:nth-of-type(1) {
          @extend .is-hidden;
        }
        .button:nth-of-type(2) {
          @extend .is-hidden;
        }
        .button:nth-of-type(3) {
          @extend .is-hidden;
        }
        .button:nth-of-type(4) {
          @extend .is-hidden;
        }
      }
      .button {
        border-radius: 0px;
        &:first-child {
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
        }
        &:last-child {
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
        }
      }
      .button.primary {
        border-radius: 5px;
      }
    }
  }
}
