import { createSlice } from '@reduxjs/toolkit';

import { findIndex, includes, isArray, map } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_ASSIGN_ENTITIES_TABLE_DETAIL,
  DATA_ENTITY_TABLE_DETAIL,
  DATA_GROUP_USERS_TABLE_DETAIL,
  DATA_SCOPE_ROLES_DETAIL,
  DATA_SELECTED_ENTITIES_TABLE_DETAIL,
  DATA_SERVICES_ROLE_DETAIL,
  DATA_SERVICES_SCOPE_DETAIL,
  DATA_SERVICE_CONSTRAINTS,
  DATA_TABLE_DETAIL,
  DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  DEFAULT_ENTITY_TABLE_DETAIL,
  DEFAULT_GROUP_USERS_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  ENDPOINT,
  RED<PERSON><PERSON>_KEY,
  ROLES_API_ENDPOINT,
} from './constants';

const adminEntitlementsSlice = createSlice({
  name: R<PERSON><PERSON><PERSON>_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },

    updateEntityTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_ENTITY_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_ENTITY_TABLE_DETAIL] = tableDetail;
      }
    },

    updateAssignEntitiesTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
      }
    },

    updateSelectedEntitiesDetail(
      state,
      { payload: { selectedEntities, pageOffset, resetEntitiesList = false } },
    ) {
      const tableDetail = state[DATA_SELECTED_ENTITIES_TABLE_DETAIL];
      const pageSize = pageOffset + 100;
      const totalRecord = selectedEntities?.length;

      let records = [];
      if (selectedEntities?.length > 0) {
        records = selectedEntities.slice(pageOffset, pageSize);
      }

      if (isArray(selectedEntities)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        tableDetail.totalRecord = totalRecord;
        tableDetail.data = [...tableDetail.data, ...records];
        if (resetEntitiesList) {
          tableDetail.data = [];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_SELECTED_ENTITIES_TABLE_DETAIL] = tableDetail;
      }
    },

    updateRoles(state, { payload: { id, data } }) {
      const rolesDetail = state[DATA_SERVICES_ROLE_DETAIL];

      if (!rolesDetail[id]) {
        rolesDetail[id] = [];
      }

      if (isArray(data)) {
        rolesDetail[id] = data;
      }

      state[DATA_SERVICES_ROLE_DETAIL] = rolesDetail;
    },

    updateScopeRoles(state, { payload: { id, data } }) {
      const scopeRoleDetail = state[DATA_SCOPE_ROLES_DETAIL];

      if (!scopeRoleDetail[id]) {
        scopeRoleDetail[id] = [];
      }

      if (isArray(data)) {
        scopeRoleDetail[id] = data;
      }

      state[DATA_SCOPE_ROLES_DETAIL] = scopeRoleDetail;
    },

    updateScopes(state, { payload: { id, data } }) {
      const scopesDetail = state[DATA_SERVICES_SCOPE_DETAIL];

      if (!scopesDetail[id]) {
        scopesDetail[id] = [];
      }

      if (isArray(data)) {
        scopesDetail[id] = data;
      }

      state[DATA_SERVICES_SCOPE_DETAIL] = scopesDetail;
    },

    updateAllEntitiesRoles(state, { payload }) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];
      if (payload?.selectedRole.length > 0) {
        const selectedRowIds = map(payload?.selectedRows, 'id');
        tableDetail.data.forEach((item) => {
          if (includes(selectedRowIds, item.id)) {
            item.selectedRole = payload?.selectedRole;
          }
        });
      } else {
        tableDetail.data.forEach((item) => {
          item.selectedRole = [];
        });
      }
      state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
    },

    updateAllEntitiesScopes(state, { payload }) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];
      if (payload?.selectedScope.length > 0) {
        const selectedRowIds = map(payload?.selectedRows, 'id');
        tableDetail.data.forEach((item) => {
          if (includes(selectedRowIds, item.id)) {
            item.selectedScope = payload?.selectedScope;
          }
        });
      }
      state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
    },

    updateEntityRole(state, { payload }) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];
      const index = findIndex(tableDetail.data, ['id', payload.id]);
      tableDetail.data[index] = {
        ...tableDetail.data[index],
        ...{ selectedRole: payload.selectedValue },
      };
      state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
    },

    updateEntityScope(state, { payload }) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];
      const index = findIndex(tableDetail.data, ['id', payload.id]);
      tableDetail.data[index] = {
        ...tableDetail.data[index],
        ...{ selectedScope: payload.selectedValue },
      };
      state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
    },

    updateConstraints(state, { payload }) {
      state[DATA_SERVICE_CONSTRAINTS] = payload;
    },

    updateGroupUsersTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_GROUP_USERS_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_GROUP_USERS_TABLE_DETAIL] = tableDetail;
      }
    },

    resetTableDetail(state) {
      const tableDetail = DEFAULT_ENTITY_TABLE_DETAIL;
      state[DATA_ENTITY_TABLE_DETAIL] = tableDetail;
    },
  },
});

const {
  updateTableDetail,
  updateEntityTableDetail,
  updateAssignEntitiesTableDetail,
  updateScopeRoles,
  updateRoles,
  updateScopes,
  updateSelectedEntitiesDetail,
  updateAllEntitiesRoles,
  updateAllEntitiesScopes,
  updateEntityRole,
  updateEntityScope,
  updateConstraints,
  updateGroupUsersTableDetail,
  resetTableDetail,
} = adminEntitlementsSlice.actions;

export const assignEntities =
  ({ id, payload, isZiamService, auditorOverride = false }) =>
  async () => {
    let requestUrl = API_ENDPOINT + `/${id}/admin/assignments?auditorOverride=${auditorOverride}`;

    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT + '/assignments';
    }

    await http.post(requestUrl, payload);
  };

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    groupname = '',
    linkedServicesOnly = true,
    includeDefaultServices = true,
    includeDepSvces = true,
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT +
      `?limit=${pageSize}&offset=${pageOffset}&linkedServicesOnly=${linkedServicesOnly}&includeDefaultServices=${includeDefaultServices}&includeDepSvces=${includeDepSvces}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name.trim()}`;
    }

    if (groupname) {
      requestUrl += `&groupname=${groupname.trim()}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const getEntityList =
  ({
    pageSize = DEFAULT_ENTITY_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_ENTITY_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    isZiamService = false,
    id = '',
    name = '',
    type = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT + `/${id}/admin/assignments?limit=${pageSize}&offset=${pageOffset}`;

    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT + `/assignments?limit=${pageSize}&offset=${pageOffset}`;
    }
    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    if (type) {
      if (isZiamService) {
        requestUrl += `&resourceType=${type}`;
      } else {
        requestUrl += `&type=${type}`;
      }
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateEntityTableDetail(response.data || []));
      return response?.data?.records;
    }
  };

export const getAllEntitiesForAssignment =
  ({
    pageSize = DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    type = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      ENDPOINT +
      `${type === 'GROUP' ? 'groups' : 'users'}` +
      `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    if (type === 'GROUP') {
      requestUrl += `&excludeGroups=ADMIN_ENTITLEMENT_DISABLED`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateAssignEntitiesTableDetail(response.data || []));
    }
  };

export const update =
  ({ id, assignedId, payload, isZiamService, type, auditorOverride = false }) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT + `/${id}/admin/assignments/${assignedId}?auditorOverride=${auditorOverride}`;

    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT + `/assignments/${assignedId}`;
    }

    await http.put(requestUrl, payload);

    if (isZiamService) {
      dispatch(getEntityList({ id: '', isZiamService, type }));
    } else {
      dispatch(getEntityList({ id, type }));
    }
  };

export const remove =
  ({ id, tservice, isZiamService, type }) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/${tservice?.id}/admin/assignments/${id}`;
    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT + `/assignments/${id}`;
    }

    await http.delete(requestUrl);

    if (isZiamService) {
      dispatch(getEntityList({ id: '', isZiamService, type }));
    } else {
      dispatch(getEntityList({ id: tservice.id, type }));
    }
  };

export const bulkDelete =
  ({ ids, tservice, isZiamService, type }) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/${tservice.id}/admin/assignments/bulk-delete`;
    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT + '/assignments/bulk-delete';
    }

    const payload = [...ids];

    await http.put(requestUrl, payload);

    if (isZiamService) {
      dispatch(getEntityList({ id: '', isZiamService, type }));
    } else {
      dispatch(getEntityList({ id: tservice.id, type }));
    }
  };

export const setSelectedEntities =
  ({ selectedEntities, pageOffset, resetEntitiesList }) =>
  (dispatch) => {
    dispatch(updateSelectedEntitiesDetail({ selectedEntities, pageOffset, resetEntitiesList }));
  };

export const setSelectedRole =
  (data = []) =>
  (dispatch) => {
    dispatch(updateEntityRole(data));
  };

export const setSelectedScope =
  (data = []) =>
  (dispatch) => {
    dispatch(updateEntityScope(data));
  };

export const setSameRoleForAllEntities =
  (selectedRole = [], selectedRows = []) =>
  (dispatch) => {
    dispatch(updateAllEntitiesRoles({ selectedRole, selectedRows }));
  };

export const setSameScopeForAllEntities =
  (selectedScope = [], selectedRows = []) =>
  (dispatch) => {
    dispatch(updateAllEntitiesScopes({ selectedScope, selectedRows }));
  };

export const getRoles =
  ({ id, scopeId, isZiamService }) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/${id}/roles${scopeId ? `?tsScopeId=${scopeId}` : ''}`;

    if (isZiamService) {
      requestUrl = ROLES_API_ENDPOINT;
    }
    const response = await http.get(requestUrl);

    if (response?.data) {
      if (scopeId) {
        dispatch(updateScopeRoles({ id: scopeId, data: response.data }));
      } else if (isZiamService) {
        dispatch(updateRoles({ id, data: response?.data?.records }));
      } else {
        dispatch(updateRoles({ id, data: response.data }));
      }
    }
  };

export const getScopes =
  ({ id }) =>
  async (dispatch) => {
    let requestUrl = ROLES_API_ENDPOINT;
    if (id) {
      requestUrl = API_ENDPOINT + `/${id}/scopes`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateScopes({ id, data: response.data }));
    }
  };

export const getServiceConstraints = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/service-constraints';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateConstraints(response.data));
  }
};

export const getGroupUsers =
  ({
    pageSize = DEFAULT_GROUP_USERS_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_GROUP_USERS_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    id = '',
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = ENDPOINT + `groups/${id}/users?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateGroupUsersTableDetail(response.data));
    }
  };

export const resetTableData = () => async (dispatch) => {
  dispatch(resetTableDetail());
};

export const syncRoles =
  ({ id }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/sync-roles?tServiceId=${id}`;

    await http.post(requestUrl);
  };

export default adminEntitlementsSlice;
