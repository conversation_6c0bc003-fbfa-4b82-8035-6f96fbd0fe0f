import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import { find } from 'lodash-es';

import {
  DATA_ASSIGN_ENTITIES_TABLE_DETAIL,
  DATA_ENTITY_TABLE_DETAIL,
  DATA_GROUP_USERS_TABLE_DETAIL,
  DATA_ROLES_DETAIL,
  DATA_SCOPES_DETAIL,
  DATA_SCOPE_ROLES_DETAIL,
  DATA_SELECTED_ENTITIES_TABLE_DETAIL,
  DATA_SERVICES_ROLE_DETAIL,
  DATA_SERVICES_SCOPE_DETAIL,
  DATA_SERVICE_CONSTRAINTS,
  DATA_TABLE_DETAIL,
  DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG,
  DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  DEFAULT_ENTITY_TABLE_CONFIG,
  DEFAULT_ENTITY_TABLE_DETAIL,
  DEFAULT_GROUP_USERS_TABLE_CONFIG,
  DEFAULT_GROUP_USERS_TABLE_DETAIL,
  DEFAULT_REVIEW_ASSIGN_ENTITIES_TABLE_CONFIG,
  DEFAULT_ROLE_TABLE_CONFIG,
  DEFAULT_SCOPE_ROLES_DETAIL,
  DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
  DEFAULT_SERVICES_ROLE_DETAIL,
  DEFAULT_SERVICES_SCOPE_DETAIL,
  DEFAULT_SERVICE_CONSTRAINTS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const EMPTY_ARRAY = [];
const EMPTY_OBJECT = {};

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectEntityTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_ENTITY_TABLE_DETAIL] || DEFAULT_ENTITY_TABLE_DETAIL,
);

export const selectEntityTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_ENTITY_TABLE_CONFIG,
);

export const selectGroupUsersTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_GROUP_USERS_TABLE_CONFIG,
);

export const selectAssignEntitiesTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG,
);

export const selectReviewAssignEntitiesTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_REVIEW_ASSIGN_ENTITIES_TABLE_CONFIG,
);

export const selectRoleDetail = createSelector(baseSelector, (state) => state[DATA_ROLES_DETAIL]);

export const selectScopeDetail = createSelector(baseSelector, (state) => state[DATA_SCOPES_DETAIL]);

export const selectScopesList = createSelector(selectScopeDetail, (data) =>
  getDropDownList({ list: data, lite: false }),
);

export const selectAssignEntitiesTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] || DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
);

export const selectedEntitiesTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SELECTED_ENTITIES_TABLE_DETAIL] || DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
);

export const selectGroupUsersTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_GROUP_USERS_TABLE_DETAIL] || DEFAULT_GROUP_USERS_TABLE_DETAIL,
);

export const selectServiceConstraints = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICE_CONSTRAINTS] || DEFAULT_SERVICE_CONSTRAINTS,
);

export const selectServiceScopeDetail = (servicesScopeDetail, id) =>
  servicesScopeDetail[id] || EMPTY_ARRAY;

export const selectServiceScopeList = (list) => getDropDownList({ list: list });

export const selectServicesScopeDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_SCOPE_DETAIL] || DEFAULT_SERVICES_SCOPE_DETAIL,
);

export const selectServiceRoleDetail = (servicesRoleDetail, id) =>
  servicesRoleDetail[id] || EMPTY_ARRAY;

export const selectServicesRoleDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICES_ROLE_DETAIL] || DEFAULT_SERVICES_ROLE_DETAIL,
);

export const selectServiceRoleList = (list) => getDropDownList({ list: list, lite: false });

export const selectScopesRoleDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SCOPE_ROLES_DETAIL] || DEFAULT_SCOPE_ROLES_DETAIL,
);

export const selectScopeRoleDetail = (selectScopesRoleDetail, id) =>
  selectScopesRoleDetail[id] || EMPTY_ARRAY;

export const selectScopeRoleList = (list) => getDropDownList({ list: list });

export const selectRoleTableConfig = createSelector(baseSelector, () => DEFAULT_ROLE_TABLE_CONFIG);

export const selectServiceConstraintDetail = (serviceConstraints, serviceName) =>
  find(serviceConstraints, { serviceName }) || EMPTY_OBJECT;
