import accessPoliciesSlice from './access-policies';
import adminEntitlementsSlice from './admin-entitlements';
import advancedSettingsSlice from './advanced-settings';
import apiClientsSlice from './api-clients';
import apiResourcesSlice from './api-resources';
import auditLogsSlice from './audit-logs';
import authenticationLevelsSlice from './authentication-levels';
import authenticationMethodsSlice from './authentication-methods';
import brandingSlice from './branding';
import countryCodesSlice from './countries';
import departmentsSlice from './departments';
import deviceTokensSlice from './device-tokens';
import eusaSlice from './eusa';
import externalIdentitiesSlice from './external-identities';
import featuresSlice from './features';
import globalSlice from './global';
import groupsSlice from './groups';
import locationGroupsSlice from './ip-location-groups';
import locationsSlice from './ip-locations';
import loginSlice from './login';
import metricsSlice from './metrics';
import migrationSlice from './migration';
import passwordPolicySlice from './password-policy';
import permissionsSlice from './permissions';
import profileSlice from './profile';
import remoteAssistanceSlice from './remote-assistance';
import roleslice from './roles';
import serviceEntitlementsSlice from './service-entitlements';
import servicesSlice from './services';
import sessionAttributesSlice from './session-attributes';
import signonPoliciesSlice from './signon-policies';
import tenantDomainsSlice from './tenant-domains';
import tokenValidators from './token-validators';
import tokensSlice from './tokens';
import userSlice from './user';
import userAttributesSlice from './user-attributes';
import usersSlice from './users';

export const reducer = {
  [accessPoliciesSlice.name]: accessPoliciesSlice.reducer,
  [adminEntitlementsSlice.name]: adminEntitlementsSlice.reducer,
  [advancedSettingsSlice.name]: advancedSettingsSlice.reducer,
  [auditLogsSlice.name]: auditLogsSlice.reducer,
  [apiClientsSlice.name]: apiClientsSlice.reducer,
  [apiResourcesSlice.name]: apiResourcesSlice.reducer,
  [authenticationLevelsSlice.name]: authenticationLevelsSlice.reducer,
  [brandingSlice.name]: brandingSlice.reducer,
  [countryCodesSlice.name]: countryCodesSlice.reducer,
  [departmentsSlice.name]: departmentsSlice.reducer,
  [deviceTokensSlice.name]: deviceTokensSlice.reducer,
  [eusaSlice.name]: eusaSlice.reducer,
  [externalIdentitiesSlice.name]: externalIdentitiesSlice.reducer,
  [featuresSlice.name]: featuresSlice.reducer,
  [globalSlice.name]: globalSlice.reducer,
  [groupsSlice.name]: groupsSlice.reducer,
  [locationGroupsSlice.name]: locationGroupsSlice.reducer,
  [locationsSlice.name]: locationsSlice.reducer,
  [loginSlice.name]: loginSlice.reducer,
  [metricsSlice.name]: metricsSlice.reducer,
  [migrationSlice.name]: migrationSlice.reducer,
  [authenticationMethodsSlice.name]: authenticationMethodsSlice.reducer,
  [passwordPolicySlice.name]: passwordPolicySlice.reducer,
  [permissionsSlice.name]: permissionsSlice.reducer,
  [profileSlice.name]: profileSlice.reducer,
  [remoteAssistanceSlice.name]: remoteAssistanceSlice.reducer,
  [roleslice.name]: roleslice.reducer,
  [serviceEntitlementsSlice.name]: serviceEntitlementsSlice.reducer,
  [servicesSlice.name]: servicesSlice.reducer,
  [sessionAttributesSlice.name]: sessionAttributesSlice.reducer,
  [signonPoliciesSlice.name]: signonPoliciesSlice.reducer,
  [tenantDomainsSlice.name]: tenantDomainsSlice.reducer,
  [tokensSlice.name]: tokensSlice.reducer,
  [userSlice.name]: userSlice.reducer,
  [userAttributesSlice.name]: userAttributesSlice.reducer,
  [usersSlice.name]: usersSlice.reducer,
  [tokenValidators.name]: tokenValidators.reducer,
};
