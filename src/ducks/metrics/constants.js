export const REDUCER_KEY = 'metrics';

export const API_ENDPOINT = '/admin/internal-api/v1/metrics';

export const DATA_EVENTS = 'events';
export const DATA_DASHBOARD_CARDS = 'cards';
export const DATA_GRAPH_FLAGS = 'graphFlags';
export const DATA_AUTHENTICATION = 'authentication';
export const DATA_SERVICE_ASSIGNMENT = 'enrolledViaZCC';
export const DATA_DEVICE_REGISTRATION = 'deviceRegistration';
export const DATA_SSO = 'sso';

export const DATA_USER_CREATION_SUCCESS = 'userCreationSuccess';
export const DATA_USER_MODIFICATION_SUCCESS = 'userModificationSuccess';
export const DATA_USER_DELETION_SUCCESS = 'userDeletionSuccess';

export const DATA_USER_CREATION_FAILURE = 'userCreationFailure';
export const DATA_USER_MODIFICATION_FAILURE = 'userModificationFailure';
export const DATA_USER_DELETION_FAILURE = 'userDeletionFailure';

export const METRIC_RESULT = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

export const DEFAULT_EVENTS = [
  'INVALID_CSV_OPERATION',
  'SSO_EVENT',
  'USER_DELETION_EVENT',
  'AUTHENTICATION_EVENT',
  'DEVICE_REGISTRATION_EVENT',
  'USER_MODIFICATION_EVENT',
  'USER_CREATION_EVENT',
  'SERVICE_ASSIGNMENT_EVENT',
];

export const DEFAULT_DASHBOARD_CARDS = [];

export const DEFAULT_GRAPH_FLAGS = {
  authentication_event: false,
  user_creation_event_success: false,
  user_creation_event_failure: false,
  device_registration_event: false,
  service_assignment_event: false,
  sso_event: false,
};

export const DEFAULT_AUTHENTICATION = { formattedEntries: [], legends: [] };
export const DEFAULT_SERVICE_ASSIGNMENT = { formattedEntries: [], legends: [] };
export const DEFAULT_DEVICE_REGISTRATION = { formattedEntries: [], legends: [] };
export const DEFAULT_SSO = { formattedEntries: [], legends: [] };

export const DEFAULT_USER_CREATION_SUCCESS = { formattedEntries: [], legends: [] };
export const DEFAULT_USER_MODIFICATION_SUCCESS = { formattedEntries: [], legends: [] };
export const DEFAULT_USER_DELETION_SUCCESS = { formattedEntries: [], legends: [] };

export const DEFAULT_USER_CREATION_FAILURE = { formattedEntries: [], legends: [] };
export const DEFAULT_USER_MODIFICATION_FAILURE = { formattedEntries: [], legends: [] };
export const DEFAULT_USER_DELETION_FAILURE = { formattedEntries: [], legends: [] };

export const DEFAULT_STATE = {
  [DATA_EVENTS]: DEFAULT_EVENTS,
  [DATA_DASHBOARD_CARDS]: DEFAULT_DASHBOARD_CARDS,
  [DATA_GRAPH_FLAGS]: DEFAULT_GRAPH_FLAGS,
  [DATA_AUTHENTICATION]: DEFAULT_AUTHENTICATION,
  [DATA_SERVICE_ASSIGNMENT]: DEFAULT_SERVICE_ASSIGNMENT,
  [DATA_DEVICE_REGISTRATION]: DEFAULT_DEVICE_REGISTRATION,
  [DATA_SSO]: DEFAULT_SSO,
  [DATA_USER_CREATION_SUCCESS]: DEFAULT_USER_CREATION_SUCCESS,
  [DATA_USER_MODIFICATION_SUCCESS]: DEFAULT_USER_MODIFICATION_SUCCESS,
  [DATA_USER_DELETION_SUCCESS]: DEFAULT_USER_DELETION_SUCCESS,
  [DATA_USER_CREATION_FAILURE]: DEFAULT_USER_CREATION_FAILURE,
  [DATA_USER_MODIFICATION_FAILURE]: DEFAULT_USER_MODIFICATION_FAILURE,
  [DATA_USER_DELETION_FAILURE]: DEFAULT_USER_DELETION_FAILURE,
};
