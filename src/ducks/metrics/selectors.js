import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_AUTHENTICATION,
  DATA_DASHBOARD_CARDS,
  DATA_DEVICE_REGISTRATION,
  DATA_EVENTS,
  DATA_GRAPH_FLAGS,
  DATA_SERVICE_ASSIGNMENT,
  DATA_SSO,
  DATA_USER_CREATION_FAILURE,
  DATA_USER_CREATION_SUCCESS,
  DATA_USER_DELETION_FAILURE,
  DATA_USER_DELETION_SUCCESS,
  DATA_USER_MODIFICATION_FAILURE,
  DATA_USER_MODIFICATION_SUCCESS,
  DEFAULT_AUTHENTICATION,
  DEFAULT_DASHBOARD_CARDS,
  DEFAULT_DEVICE_REGISTRATION,
  DEFAULT_EVENTS,
  DEFAULT_GRAPH_FLAGS,
  DEFAULT_SERVICE_ASSIGNMENT,
  DEFAULT_SSO,
  DEFAULT_USER_CREATION_FAILURE,
  DEFAULT_USER_CREATION_SUCCESS,
  DEFAULT_USER_DELETION_FAILURE,
  DEFAULT_USER_DELETION_SUCCESS,
  DEFAULT_USER_MODIFICATION_FAILURE,
  DEFAULT_USER_MODIFICATION_SUCCESS,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectEvents = createSelector(
  baseSelector,
  (state) => state[DATA_EVENTS] || DEFAULT_EVENTS,
);

export const selectDashboardCards = createSelector(
  baseSelector,
  (state) => state[DATA_DASHBOARD_CARDS] || DEFAULT_DASHBOARD_CARDS,
);

export const selectGraphFlags = createSelector(
  baseSelector,
  (state) => state[DATA_GRAPH_FLAGS] || DEFAULT_GRAPH_FLAGS,
);

export const selectAuthenticationEvents = createSelector(
  baseSelector,
  (state) => state[DATA_AUTHENTICATION] || DEFAULT_AUTHENTICATION,
);

export const selectServiceAssignmentEvents = createSelector(
  baseSelector,
  (state) => state[DATA_SERVICE_ASSIGNMENT] || DEFAULT_SERVICE_ASSIGNMENT,
);

export const selectDeviceRegistrationEvents = createSelector(
  baseSelector,
  (state) => state[DATA_DEVICE_REGISTRATION] || DEFAULT_DEVICE_REGISTRATION,
);

export const selectSSOEvents = createSelector(
  baseSelector,
  (state) => state[DATA_SSO] || DEFAULT_SSO,
);

export const selectUserCreationSuccessEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_CREATION_SUCCESS] || DEFAULT_USER_CREATION_SUCCESS,
);

export const selectUserModificationSuccessEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_MODIFICATION_SUCCESS] || DEFAULT_USER_MODIFICATION_SUCCESS,
);

export const selectUserDeletionSuccessEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_DELETION_SUCCESS] || DEFAULT_USER_DELETION_SUCCESS,
);

export const selectUserCreationFailureEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_CREATION_FAILURE] || DEFAULT_USER_CREATION_FAILURE,
);

export const selectUserModificationFailureEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_MODIFICATION_FAILURE] || DEFAULT_USER_MODIFICATION_FAILURE,
);

export const selectUserDeletionFailureEvents = createSelector(
  baseSelector,
  (state) => state[DATA_USER_DELETION_FAILURE] || DEFAULT_USER_DELETION_FAILURE,
);
