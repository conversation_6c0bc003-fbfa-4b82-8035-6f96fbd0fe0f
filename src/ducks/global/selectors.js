import { createSelector } from '@reduxjs/toolkit';

import {
  APP_STATE,
  DATA_APP_SETUP_DETAIL,
  DATA_APP_STATE,
  DATA_BANNER,
  DATA_BUILD_VERSION,
  DATA_LOADING,
  DATA_NAV_STATE,
  DATA_NOTIFICATIONS,
  DEFAULT_APP_SETUP_DETAIL,
  DEFAULT_APP_STATE,
  DEFAULT_BANNER,
  DEFAULT_BUILD_VERSION,
  DEFAULT_LOADING,
  DEFAULT_NOTIFICATIONS,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectAppState = createSelector(
  baseSelector,
  (state) => state[DATA_APP_STATE] || DEFAULT_APP_STATE,
);

export const selectAppSetupPending = createSelector(
  selectAppState,
  (appState) => appState === APP_STATE.UNSET,
);

export const selectIsAppSetupDone = createSelector(
  selectAppState,
  (appState) => appState === APP_STATE.SETUP_DONE,
);

export const selectAppSetupHasError = createSelector(
  selectAppState,
  (appState) => appState === APP_STATE.SETUP_ERROR,
);

export const selectAppSetupDetail = createSelector(
  baseSelector,
  (state) => state[DATA_APP_SETUP_DETAIL] || DEFAULT_APP_SETUP_DETAIL,
);

export const selectIsLoading = createSelector(
  baseSelector,
  (state) => state[DATA_LOADING] ?? DEFAULT_LOADING,
);

export const selectNotificationsList = createSelector(baseSelector, (state) =>
  Object.values(state[DATA_NOTIFICATIONS] || DEFAULT_NOTIFICATIONS),
);

export const selectBuildVersion = createSelector(
  baseSelector,
  (state) => state[DATA_BUILD_VERSION] || DEFAULT_BUILD_VERSION,
);

export const selectBanner = createSelector(
  baseSelector,
  (state) => state[DATA_BANNER] || DEFAULT_BANNER,
);

export const selectIsNavMinimized = createSelector(
  baseSelector,
  (state) => state[DATA_NAV_STATE]?.minimize || false,
);
