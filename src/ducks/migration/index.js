import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_STATUS, DEFAULT_STATE, REDUCER_KEY } from './constants';

const migrationSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateStatus(state, { payload }) {
      state[DATA_STATUS] = payload || DEFAULT_STATE;
    },
  },
});

const { updateStatus } = migrationSlice.actions;

export const getMigrationStatus = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT);

  if (response?.data) {
    dispatch(updateStatus(response?.data || {}));
  }
};

export const updateMigrationStatus =
  (newState, auditorOverride = false) =>
  async (dispatch) => {
    const response = await http.put(API_ENDPOINT + `?auditorOverride=${auditorOverride}`, {
      newState,
    });

    if (response?.data) {
      dispatch(updateStatus(response?.data || {}));
    }
  };

export default migrationSlice;
