import { createSelector } from '@reduxjs/toolkit';

import { find } from 'lodash-es';

import {
  DATA_SYSTEM_FEATURES,
  DATA_TENANT_FEATURES,
  DEFAULT_ACCESS_POLICY_DETAIL,
  DEFAULT_ADMINS_MFA_DETAIL,
  DEFAULT_ONE_API_DETAIL,
  DEFAULT_STEPUP_SUPPORT_DETAIL,
  DEFAULT_SYSTEM_FEATURES,
  DEFAULT_TENANT_FEATURES,
  DEFAULT_TOKEN_VALIDATOR_DETAIL,
  DEFAULT_TOKEN_VALIDATOR_SUPPORT_DETAIL,
  DEFAULT_USER_SSO_DETAIL,
  DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTenantFeatures = createSelector(
  baseSelector,
  (state) => state[DATA_TENANT_FEATURES] || DEFAULT_TENANT_FEATURES,
);

export const selectSystemFeatures = createSelector(
  baseSelector,
  (state) => state[DATA_SYSTEM_FEATURES] || DEFAULT_SYSTEM_FEATURES,
);

export const selectUserSSOFeature = createSelector(selectTenantFeatures, ({ features = {} }) => {
  return find(features, { name: DEFAULT_USER_SSO_DETAIL.name }) || DEFAULT_USER_SSO_DETAIL;
});

export const selectIsUserSSOEnabled = createSelector(selectUserSSOFeature, ({ status }) => status);

export const selectAdminsMFAFeature = createSelector(selectTenantFeatures, ({ features = {} }) => {
  return find(features, { name: DEFAULT_ADMINS_MFA_DETAIL.name }) || DEFAULT_ADMINS_MFA_DETAIL;
});

export const selectIsAdminsMFAEnabled = createSelector(
  selectAdminsMFAFeature,
  ({ status }) => status,
);

export const selectOneApiFeature = createSelector(selectSystemFeatures, ({ features = {} }) => {
  return find(features, { name: DEFAULT_ONE_API_DETAIL.name }) || DEFAULT_ONE_API_DETAIL;
});

export const selectIsOneApiEnabled = createSelector(selectOneApiFeature, ({ status }) => status);

export const selectAccessPolicyFeature = createSelector(
  selectSystemFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_ACCESS_POLICY_DETAIL.name }) || DEFAULT_ACCESS_POLICY_DETAIL
    );
  },
);

export const selectIsAccessPolicyEnabled = createSelector(
  selectAccessPolicyFeature,
  ({ status }) => status,
);

export const selectStepupSupportSystemFeature = createSelector(
  selectSystemFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_STEPUP_SUPPORT_DETAIL.name }) || DEFAULT_STEPUP_SUPPORT_DETAIL
    );
  },
);

export const selectIsStepupSupportSystemEnabled = createSelector(
  selectStepupSupportSystemFeature,
  ({ status }) => status,
);

export const selectStepupSupportTenantFeature = createSelector(
  selectTenantFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_STEPUP_SUPPORT_DETAIL.name }) || DEFAULT_STEPUP_SUPPORT_DETAIL
    );
  },
);

export const selectIsStepupSupportTenantEnabled = createSelector(
  selectStepupSupportTenantFeature,
  ({ status }) => status,
);

export const selectZiaStepupSupportSystemFeature = createSelector(
  selectSystemFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL.name }) ||
      DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL
    );
  },
);

export const selectZiaStepupSupportTenantFeature = createSelector(
  selectTenantFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL.name }) ||
      DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL
    );
  },
);

export const selectIsZiaStepupSupportSystemEnabled = createSelector(
  selectZiaStepupSupportSystemFeature,
  ({ status }) => status,
);

export const selectIsZiaStepupSupportTenantEnabled = createSelector(
  selectZiaStepupSupportTenantFeature,
  ({ status }) => status,
);

export const selectTokenValidatorTenantFeature = createSelector(
  selectTenantFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_TOKEN_VALIDATOR_DETAIL.name }) ||
      DEFAULT_TOKEN_VALIDATOR_DETAIL
    );
  },
);

export const selectTokenValidatorSystemFeature = createSelector(
  selectSystemFeatures,
  ({ features = {} }) => {
    return (
      find(features, { name: DEFAULT_TOKEN_VALIDATOR_SUPPORT_DETAIL.name }) ||
      DEFAULT_TOKEN_VALIDATOR_SUPPORT_DETAIL
    );
  },
);

export const selectIsTokenValidatorTenantEnabled = createSelector(
  selectTokenValidatorTenantFeature,
  ({ status }) => status,
);

export const selectIsTokenValidatorSystemEnabled = createSelector(
  selectTokenValidatorSystemFeature,
  ({ status }) => status,
);
