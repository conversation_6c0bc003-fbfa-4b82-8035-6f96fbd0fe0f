export const REDUCER_KEY = 'features';

export const API_ENDPOINT = '/admin/internal-api/v1/features';

export const DATA_TENANT_FEATURES = 'tenantFeatures';

export const DATA_SYSTEM_FEATURES = 'systemFeatures';

export const DEFAULT_USER_SSO_DETAIL = {
  name: 'USER_SSO',
  status: false,
  displayName: 'User SSO support',
};

export const DEFAULT_ADMINS_MFA_DETAIL = {
  name: 'ADMINS_MFA_CONTROL',
  status: false,
  displayName: 'ADMINS_MFA CONTROL',
};

export const DEFAULT_ONE_API_DETAIL = {
  name: 'ONEAPI_SUPPORT',
  status: false,
  displayName: 'One API Support',
};

export const DEFAULT_STEPUP_SUPPORT_DETAIL = {
  name: 'STEPUP_SUPPORT',
  status: false,
  displayName: 'STEPUP_SUPPORT',
};

export const DEFAULT_ZIA_STEPUP_SUPPORT_DETAIL = {
  name: 'ZIA_STEPUP_SUPPORT',
  status: false,
  displayName: 'ZIA_STEPUP_SUPPORT',
};

export const DEFAULT_ACCESS_POLICY_DETAIL = {
  name: 'ONEAPI_ACCESS_POLICY_SUPPORT',
  status: false,
  displayName: 'One API Access Policy Support',
};

export const DEFAULT_TOKEN_VALIDATOR_DETAIL = {
  name: 'TOKEN_VALIDATOR',
  status: false,
  displayName: 'Token Validator',
};

export const DEFAULT_TOKEN_VALIDATOR_SUPPORT_DETAIL = {
  name: 'TOKEN_VALIDATOR_SUPPORT',
  status: false,
  displayName: 'Token Validator Support',
};

export const DEFAULT_TENANT_FEATURES = {
  features: [
    DEFAULT_ADMINS_MFA_DETAIL,
    DEFAULT_USER_SSO_DETAIL,
    DEFAULT_STEPUP_SUPPORT_DETAIL,
    DEFAULT_TOKEN_VALIDATOR_DETAIL,
  ],
};

export const DEFAULT_SYSTEM_FEATURES = {
  features: [DEFAULT_ONE_API_DETAIL, DEFAULT_STEPUP_SUPPORT_DETAIL],
};

export const DEFAULT_STATE = {
  [DATA_TENANT_FEATURES]: DEFAULT_TENANT_FEATURES,
  [DATA_SYSTEM_FEATURES]: DEFAULT_SYSTEM_FEATURES,
};
