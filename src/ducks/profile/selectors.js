import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  DATA_LANGUAGES,
  DATA_MY_PROFILE,
  DEFAULT_LANGUAGES,
  DEFAULT_MY_PROFILE_DETAILS,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectMyProfileDetail = createSelector(
  baseSelector,
  (state) => state[DATA_MY_PROFILE] || DEFAULT_MY_PROFILE_DETAILS,
);

export const selectLanguagesOptions = createSelector(
  baseSelector,
  (state) => state[DATA_LANGUAGES] || DEFAULT_LANGUAGES,
);

export const selectLanguagesList = createSelector(selectLanguagesOptions, (data) =>
  getDropDownList({ list: data }),
);
