import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_REMOTE_ASSISTANCE_SETTING,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';

const remoteAssistanceSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateRemoteAssistanceSetting(state, { payload }) {
      state[DATA_REMOTE_ASSISTANCE_SETTING] = payload;
    },
  },
});

const { updateRemoteAssistanceSetting } = remoteAssistanceSlice.actions;

export const getRemoteAssistance = () => async (dispatch) => {
  const response = await http.get(API_ENDPOINT);

  if (response?.data) {
    dispatch(updateRemoteAssistanceSetting(response.data));
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(updateRemoteAssistanceSetting(response.data));
  }
};

export default remoteAssistanceSlice;
