import { createSelector } from '@reduxjs/toolkit';

import { DATA_SETTINGS, DEFAULT_SETTINGS, REDUCER_KEY } from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectSettings = createSelector(
  baseSelector,
  (state) => state[DATA_SETTINGS] || DEFAULT_SETTINGS,
);

export const selectDomainList = createSelector(selectSettings, ({ domains }) =>
  domains.map(({ id, name }) => ({ label: name, value: id })),
);
