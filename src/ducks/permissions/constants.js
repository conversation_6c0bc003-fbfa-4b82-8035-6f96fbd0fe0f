import { PERMISSIONS_KEY } from '../../config';

export const REDUCER_KEY = 'permissions';

export const API_ENDPOINT = '/admin/internal-api/v1/';

export const ALL_PERMISSIONS_API_ENDPOINT = '/admin/internal-api/v1/permissions';

export const MY_PERMISSIONS_API_ENDPOINT = '/admin/internal-api/v1/my-profile/permissions';

export const DATA_ALL_PERMISSIONS = 'allPermissions';
export const DATA_ALL_PERMISSION_LEVELS = 'allPermissionLevels';
export const DATA_MY_PERMISSIONS = 'myPermissions';
export const DATA_MY_PERMISSIONS_LEVEL = 'myPermissionsLevel';
export const DATA_ADMIN_ENTITLEMENT_PERMISSIONS = 'adminEntitlementPermissions';

export const DEFAULT_ALL_PERMISSIONS = [];
export const DEFAULT_ALL_PERMISSION_LEVELS = {};
export const DEFAULT_MY_PERMISSIONS = [];
export const DEFAULT_MY_PERMISSIONS_LEVEL = {};
export const DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS = [];

export const NONE_PERMISSION_BLACK_LIST = [
  PERMISSIONS_KEY.AUTHENTICATION_METHODS,
  PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY,
  PERMISSIONS_KEY.CXO_INSIGHT,
  PERMISSIONS_KEY.BRANDING_POLICY,
  PERMISSIONS_KEY.GUEST_DOMAIN_POLICY,
];

export const DEFAULT_PERMISSION_LEVEL = {
  hasFullAccess: false,
  hasRestrictedFullAccess: false,
  hasViewAccess: false,
  hasRestrictedViewAccess: false,
  noAccess: true,
};

export const DEFAULT_STATE = {
  [DATA_ALL_PERMISSIONS]: DEFAULT_ALL_PERMISSIONS,
  [DATA_ALL_PERMISSION_LEVELS]: DEFAULT_ALL_PERMISSION_LEVELS,
  [DATA_MY_PERMISSIONS]: DEFAULT_MY_PERMISSIONS,
  [DATA_MY_PERMISSIONS_LEVEL]: DEFAULT_MY_PERMISSIONS_LEVEL,
  [DATA_ADMIN_ENTITLEMENT_PERMISSIONS]: DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS,
};
