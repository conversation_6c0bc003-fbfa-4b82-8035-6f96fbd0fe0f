import { HttpResponse, delay, http } from 'msw';

import { MY_PERMISSIONS_API_ENDPOINT } from './constants';

export const MOCK_PERMISSIONS = [
  'ip-location.full',
  'linked-tenant.view',
  'external-identity.full',
  'guest-domain.full',
  'role.full',
  'sign-on-policy.full',
  'authentication-session.full',
  'administrative-entitlement.full',
  'authentication-methods.full',
  'user-and-group.full',
  'service-entitlement.full',
  'user-credential.full',
  'branding.full',
  'authentication-event-log.view',
  'audit-Log.view',
  'remote-assistance-management.full',
  'oauth2-clients.full',
  'resource-servers.full',
];

export const permissionsMockHandler = [
  http.get(MY_PERMISSIONS_API_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(MOCK_PERMISSIONS);
  }),
];
