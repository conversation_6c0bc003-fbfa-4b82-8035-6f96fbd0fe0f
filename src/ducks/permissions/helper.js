import { PERMISSION_LEVEL } from '../../config';

export const getHasPermissionDetail = (level) => {
  let detail = {
    hasFullAccess: false,
    hasRestrictedFullAccess: false,
    hasViewAccess: false,
    hasRestrictedViewAccess: false,
    noAccess: true,
  };

  switch (level) {
    case PERMISSION_LEVEL.FULL: {
      detail.hasFullAccess = true;
      detail.hasRestrictedFullAccess = true;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;

      break;
    }

    case PERMISSION_LEVEL.RESTRICTED_FULL: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = true;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;

      break;
    }

    case PERMISSION_LEVEL.VIEW: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = true;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;

      break;
    }

    case PERMISSION_LEVEL.RESTRICTED_VIEW: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = false;
      detail.hasRestrictedViewAccess = true;
      detail.noAccess = false;

      break;
    }

    case PERMISSION_LEVEL.NONE: {
      detail.hasFullAccess = false;
      detail.hasRestrictedFullAccess = false;
      detail.hasViewAccess = false;
      detail.hasRestrictedViewAccess = false;
      detail.noAccess = true;

      break;
    }
  }

  return detail;
};
