import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { PERMISSION_LEVEL } from '../../config';
import {
  ALL_PERMISSIONS_API_ENDPOINT,
  API_ENDPOINT,
  DATA_ADMIN_ENTITLEMENT_PERMISSIONS,
  DATA_ALL_PERMISSIONS,
  DATA_ALL_PERMISSION_LEVELS,
  DATA_MY_PERMISSIONS,
  DATA_MY_PERMISSIONS_LEVEL,
  DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS,
  DEFAULT_ALL_PERMISSIONS,
  DEFAULT_ALL_PERMISSION_LEVELS,
  DEFAULT_MY_PERMISSIONS,
  DEFAULT_MY_PERMISSIONS_LEVEL,
  DEFAULT_STATE,
  MY_PERMISSIONS_API_ENDPOINT,
  NONE_PERMISSION_BLACK_LIST,
  REDUCER_KEY,
} from './constants';
import { getHasPermissionDetail } from './helper';

const permissionsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateAllPermissions(state, { payload }) {
      const newPermissionLevels = {};

      payload?.forEach?.(({ name }) => {
        const [permissionKey, level] = name?.split?.('.') || [];

        let levelList = newPermissionLevels[permissionKey];

        if (!levelList) {
          levelList = [];

          if (!NONE_PERMISSION_BLACK_LIST.includes(permissionKey)) {
            levelList.push(PERMISSION_LEVEL.NONE);
          }
        }

        levelList.push(level);

        newPermissionLevels[permissionKey] = levelList;
      });

      state[DATA_ALL_PERMISSION_LEVELS] = newPermissionLevels || DEFAULT_ALL_PERMISSION_LEVELS;
      state[DATA_ALL_PERMISSIONS] = payload || DEFAULT_ALL_PERMISSIONS;
    },
    updateMyPermissions(state, { payload }) {
      const newPermissions = {};

      payload?.forEach?.((permission) => {
        const [permissionKey, level] = permission?.split?.('.') || [];

        const hasPermissionDetail = getHasPermissionDetail(level);

        newPermissions[permissionKey] = hasPermissionDetail;
      });

      state[DATA_MY_PERMISSIONS_LEVEL] = newPermissions || DEFAULT_MY_PERMISSIONS_LEVEL;
      state[DATA_MY_PERMISSIONS] = payload || DEFAULT_MY_PERMISSIONS;
    },
    updateAdminEntitlementServicePermissions(state, { payload }) {
      state[DATA_ADMIN_ENTITLEMENT_PERMISSIONS] = payload || DEFAULT_ADMIN_ENTITLEMENT_PERMISSIONS;
    },
  },
});

const { updateAllPermissions, updateMyPermissions, updateAdminEntitlementServicePermissions } =
  permissionsSlice.actions;

export const getAllPermissions = () => async (dispatch) => {
  const requestUrl = ALL_PERMISSIONS_API_ENDPOINT;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateAllPermissions(response.data || []));
  }
};

export const getMyPermissions = () => async (dispatch) => {
  const requestUrl = MY_PERMISSIONS_API_ENDPOINT;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateMyPermissions(response.data || []));

    // Note: please keep mockData response as the freuency of reuse is really high for mocking various
    // permission combination/scenarios. I bet it would be handly :)

    // Still don't beleive me uncomment and try yourself :lol

    // eslint-disable-next-line no-unused-vars
    const mockData = [
      'ip-location.full',
      'linked-tenant.view',
      'external-identity.full',
      'guest-domain.full',
      'role.full',
      'sign-on-policy.full',
      'authentication-session.full',
      'administrative-entitlement.full',
      'authentication-methods.full',
      'user-and-group.full',
      'service-entitlement.full',
      'user-credential.full',
      'branding.full',
      'authentication-event-log.view',
      'audit-Log.view',
      'remote-assistance-management.full',
      'oauth2-clients.full',
      'resource-servers.full',
    ];

    // Please comment before comitting your changes otherwise a P0 for sure :rofl, cheers :mug
    // dispatch(updateMyPermissions(mockData));
  }
};

export const getAdminEntitlementServicePermissions = (userId) => async (dispatch) => {
  const response = await http.get(API_ENDPOINT + `users/${userId}/admin/entitlements`);

  if (response?.data) {
    dispatch(updateAdminEntitlementServicePermissions(response.data));
  }
};

export default permissionsSlice;
