import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_LOCATION_GROUPS,
  DATA_TABLE_DETAIL,
  DEFAULT_LOCATION_GROUP,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectLocationGroupsDetail = createSelector(
  baseSelector,
  (state) => state[DATA_LOCATION_GROUPS] || DEFAULT_LOCATION_GROUP,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);
