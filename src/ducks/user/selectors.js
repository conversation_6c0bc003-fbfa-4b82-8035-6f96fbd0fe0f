import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectMfaTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectMfaTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);
