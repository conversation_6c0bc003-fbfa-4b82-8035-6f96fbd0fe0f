import { TOKEN_TYPE_NAME } from '../../components/token-validators/helper';

export const REDUCER_KEY = 'token-validators';

export const ZDK_API_ENDPOINT = '/admin/internal-api/v1/zdk-token-config';

export const TOKENS_API_ENDPOINT = '/admin/internal-api/v1/token-validators';

export const API_ENDPOINT_ZDK_FEATURE_FLAG =
  '/admin/internal-api/v1/zdk-token-config/zdk-cluster-info';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const ZDK_IDS_DETAIL = 'zdkIds';

export const ZDK_FEATURE_FLAG = 'zdkfeatureFlag';

export const ZDK_MATCHING_TOKEN_VALIDATOR = 'matchingTokenValidator';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'tokenType',
    accessorFn: (row) => TOKEN_TYPE_NAME[row.tokenType] || null,
    Header: 'TYPE',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ZDK_FEATURE_FLAG = {
  isZdkCluster: false,
};

export const DEFAULT_MATCHING_TOKEN_VALIDATOR = {};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [ZDK_IDS_DETAIL]: DEFAULT_TABLE_DETAIL,
  [ZDK_FEATURE_FLAG]: DEFAULT_ZDK_FEATURE_FLAG,
  [ZDK_MATCHING_TOKEN_VALIDATOR]: DEFAULT_MATCHING_TOKEN_VALIDATOR,
};
