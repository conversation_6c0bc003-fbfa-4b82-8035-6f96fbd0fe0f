export const REDUCER_KEY = 'api-clients';

export const API_ENDPOINT = '/admin/internal-api/v1/oauth2-clients';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_ACCESS_TOKEN_LIFETIME = 'accessTokenLifetime';

export const DEFAULT_ACCESS_TOKEN_LIFETIME = [];

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.clientName || '---',
    Header: 'NAME',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'clientId',
    accessorFn: (row) => row.clientId || '---',
    Header: 'CLIENT_ID',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => row.status || false,
    Header: 'STATUS',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: 'basic',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'name' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_ACCESS_TOKEN_LIFETIME]: DEFAULT_ACCESS_TOKEN_LIFETIME,
};
