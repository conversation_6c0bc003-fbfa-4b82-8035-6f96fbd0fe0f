import { createSelector } from '@reduxjs/toolkit';

import { isUndefined } from 'lodash-es';

import {
  CONFIGURATION_TYPES,
  DATA_ACTIVE_PASSWORD_POLICY,
  DATA_PASSWORD_POLICIES,
  DEFAULT_ACTIVE_CONFIGURATION_TYPE,
  DEFAULT_ACTIVE_CONFIG_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectActiveConfigurationType = createSelector(
  baseSelector,
  (data) =>
    data[DATA_ACTIVE_PASSWORD_POLICY].configurationType || DEFAULT_ACTIVE_CONFIGURATION_TYPE,
);

export const selectActiveConfigDetail = createSelector(
  baseSelector,
  (data) => data[DATA_ACTIVE_PASSWORD_POLICY].config || DEFAULT_ACTIVE_CONFIG_DETAIL,
);

export const selectRecommendedPasswordPolicyDetail = createSelector(
  baseSelector,
  (data) => data[DATA_PASSWORD_POLICIES][CONFIGURATION_TYPES.RECOMMENDED]?.config || {},
);

export const selectCustomPasswordPolicyDetail = createSelector(
  baseSelector,
  (data) => data[DATA_PASSWORD_POLICIES][CONFIGURATION_TYPES.CUSTOM]?.config || {},
);

export const selectIsPasswordPolicyWeak = ({ formValues, recommendedPasswordPolicy }) => {
  const {
    minLength,
    minLowerCase,
    minUpperCase,
    minNumeric,
    minSpecialChar,
    excludeNames,
    disallowRecentPassword,
    deactivateAfterUnsuccessfulAttempts,
    allowAdminSetPasswords,
    forcePasswordChange,
    expiryAge,
  } = recommendedPasswordPolicy;

  if (formValues.minLength < minLength) {
    return 'MIN_LENGTH';
  }

  if (formValues.minLowerCase < minLowerCase) {
    return 'MIN_LOWER_CASE';
  }

  if (formValues.minUpperCase < minUpperCase) {
    return 'MIN_UPPER_CASE';
  }

  if (formValues.minNumeric < minNumeric) {
    return 'MIN_NUMERIC';
  }

  if (formValues.minSpecialChar < minSpecialChar) {
    return 'MIN_SPECIAL_CHARACTER';
  }

  if (
    !isUndefined(formValues.excludeNames) &&
    !isUndefined(excludeNames) &&
    formValues.excludeNames != excludeNames
  ) {
    return 'EXCLUDE_NAME';
  }

  if (
    !isUndefined(formValues.disallowRecentPassword) &&
    !isUndefined(disallowRecentPassword) &&
    formValues.disallowRecentPassword != disallowRecentPassword
  ) {
    return 'DISALLOW_RECENT_PASSWORD';
  }

  if (
    !isUndefined(formValues.deactivateAfterUnsuccessfulAttempts) &&
    !isUndefined(deactivateAfterUnsuccessfulAttempts) &&
    formValues.deactivateAfterUnsuccessfulAttempts != deactivateAfterUnsuccessfulAttempts
  ) {
    return 'DEACTIVATE_AFTER_UNSUCCESSFUL_ATTEMPTS';
  }

  if (
    !isUndefined(formValues.allowAdminSetPasswords) &&
    !isUndefined(allowAdminSetPasswords) &&
    formValues.allowAdminSetPasswords != allowAdminSetPasswords
  ) {
    return 'ALLOW_ADMIN_SET_PASSWORD';
  }

  if (
    !isUndefined(formValues.forcePasswordChange) &&
    !isUndefined(forcePasswordChange) &&
    formValues.forcePasswordChange != forcePasswordChange
  ) {
    return 'FORCE_PASSWORD_CHANGE';
  }

  if (formValues.expiryAge < expiryAge) {
    return 'EXPIRY_AGE';
  }
};
