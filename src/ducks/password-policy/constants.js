export const REDUCER_KEY = 'passwordPolicy';

export const API_ENDPOINT = '/admin/internal-api/v1/password-policy';

export const DATA_ACTIVE_PASSWORD_POLICY = 'activePasswordPolicy';
export const DATA_PASSWORD_POLICIES = 'passwordPolicies';

export const CONFIGURATION_TYPES = {
  RECOMMENDED: 'RECOMMENDED',
  CUSTOM: 'CUSTOM',
};

export const DEFAULT_ACTIVE_CONFIGURATION_TYPE = 'RECOMMENDED';

export const RECOMMENDED_PASSWORD_POLICY = {
  minLength: 8,
  minLowerCase: 1,
  minUpperCase: 1,
  minNumeric: 1,
  minSpecialChar: 1,
  excludeNames: false,
  disallowRecentPassword: true,
  deactivateAfterUnsuccessfulAttempts: false,
  allowAdminSetPasswords: true,
  forcePasswordChange: true,
  expiryAge: 30,
};

export const DEFAULT_ACTIVE_CONFIG_DETAIL = RECOMMENDED_PASSWORD_POLICY;

export const DEFAULT_ACTIVE_PASSWORD_POLICY_DETAIL = {
  configurationType: DEFAULT_ACTIVE_CONFIGURATION_TYPE,
  config: DEFAULT_ACTIVE_CONFIG_DETAIL,
};

export const DEFAULT_PASSWORD_POLICIES = {};

export const DEFAULT_STATE = {
  [DATA_ACTIVE_PASSWORD_POLICY]: DEFAULT_ACTIVE_PASSWORD_POLICY_DETAIL,
  [DATA_PASSWORD_POLICIES]: DEFAULT_PASSWORD_POLICIES,
};
