import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_AUTHENTICATION_METHODS, DEFAULT_STATE, REDUCER_KEY } from './constants';

const authenticationMethodsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateSettings(state, { payload }) {
      state[DATA_AUTHENTICATION_METHODS] = payload;
    },
  },
});

const { updateSettings } = authenticationMethodsSlice.actions;

export const getMultifactorSettings = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/multi-factor';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSettings(response.data));
  }
};

export const updateMultifactorSettings = (payload) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/multi-factor';

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(updateSettings(response.data));
  }
};

export default authenticationMethodsSlice;
