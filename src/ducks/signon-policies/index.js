import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_ACTIONS,
  DATA_FIELDS,
  DATA_OPERATIONS,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const signonPoliciesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateActions(state, { payload }) {
      state[DATA_ACTIONS] = payload;
    },
    updateFields(state, { payload }) {
      state[DATA_FIELDS] = payload;
    },
    updateOperations(state, { payload }) {
      state[DATA_OPERATIONS] = payload;
    },
  },
});

const { updateActions, updateFields, updateOperations, updateTableDetail } =
  signonPoliciesSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.id}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const getActions = () => async (dispatch) => {
  const url = API_ENDPOINT + `/enums/actions`;

  const response = await http.get(url);

  if (response?.data) {
    dispatch(updateActions(response.data));
  }
};

export const getFields = () => async (dispatch) => {
  const url = API_ENDPOINT + `/enums/condition-fields`;

  const response = await http.get(url);

  if (response?.data) {
    dispatch(updateFields(response.data));
  }
};

export const getOperations = () => async (dispatch) => {
  const url = API_ENDPOINT + `/enums/condition-operations`;

  const response = await http.get(url);

  if (response?.data) {
    dispatch(updateOperations(response.data));
  }
};

export default signonPoliciesSlice;
