import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_ACTIONS,
  DATA_FIELDS,
  DATA_OPERATIONS,
  DATA_TABLE_DETAIL,
  DEFAULT_ACTIONS,
  DEFAULT_FIELDS,
  DEFAULT_OPERATIONS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectFieldList = createSelector(baseSelector, (state) => {
  const enums = state[DATA_FIELDS] || DEFAULT_FIELDS;

  return enums.map((name) => ({ label: name, value: name }));
});

export const selectOperationList = createSelector(baseSelector, (state) => {
  const enums = state[DATA_OPERATIONS] || DEFAULT_OPERATIONS;

  return Object.keys(enums).map((key) => ({ label: enums[key], value: key }));
});

export const selectActionList = createSelector(baseSelector, (state) => {
  const enums = state[DATA_ACTIONS] || DEFAULT_ACTIONS;

  return enums.map((name) => ({ label: name, value: name }));
});
