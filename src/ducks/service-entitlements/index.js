import { createSlice } from '@reduxjs/toolkit';

import { findIndex, isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_ASSIGN_ENTITIES_TABLE_DETAIL,
  DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
  DATA_DEVICE_GROUPS_TABLE_DETAIL,
  DATA_ENTITY_TABLE_DETAIL,
  DATA_GROUP_USERS_TABLE_DETAIL,
  DATA_SELECTED_ENTITIES_TABLE_DETAIL,
  DATA_SERVICE_CONSTRAINTS,
  DATA_TABLE_DETAIL,
  DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
  DEFAULT_DEVICE_GROUPS_TABLE_DETAIL,
  DEFAULT_ENTITY_TABLE_DETAIL,
  DEFAULT_GROUP_USERS_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  ENDPOINT,
  REDUCER_KEY,
} from './constants';

const serviceEntitlementsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },

    updateEntityTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_ENTITY_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_ENTITY_TABLE_DETAIL] = tableDetail;
      }
    },

    updateAssignEntitiesTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] = tableDetail;
      }
    },

    updateSelectedEntitiesDetail(
      state,
      { payload: { selectedEntities, pageOffset, resetEntitiesList = false } },
    ) {
      const tableDetail = state[DATA_SELECTED_ENTITIES_TABLE_DETAIL];
      const pageSize = pageOffset + 100;
      const totalRecord = selectedEntities?.length;

      let records = [];
      if (selectedEntities?.length > 0) {
        records = selectedEntities.slice(pageOffset, pageSize);
      }

      if (isArray(selectedEntities)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        tableDetail.totalRecord = totalRecord;
        tableDetail.data = [...tableDetail.data, ...records];
        if (resetEntitiesList) {
          tableDetail.data = [];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_SELECTED_ENTITIES_TABLE_DETAIL] = tableDetail;
      }
    },

    updateConstraints(state, { payload }) {
      state[DATA_SERVICE_CONSTRAINTS] = payload;
    },

    updateGroupUsersTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_GROUP_USERS_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_GROUP_USERS_TABLE_DETAIL] = tableDetail;
      }
    },

    resetTableDetail(state) {
      const tableDetail = DEFAULT_ENTITY_TABLE_DETAIL;
      state[DATA_ENTITY_TABLE_DETAIL] = tableDetail;
    },

    updateDeviceGroupsTableDetail(state, { payload }) {
      const tableDetail = state[DATA_DEVICE_GROUPS_TABLE_DETAIL];

      if (payload && isArray(payload)) {
        payload = payload.map((group) => {
          return { ...group, isSelected: false };
        });
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.totalRecord = payload.length;
        tableDetail.data = [...payload];
        tableDetail.hasFetchedAllRecords = true;

        state[DATA_DEVICE_GROUPS_TABLE_DETAIL] = tableDetail;
      }
    },

    updateDeviceGroupAssignments(state, { payload }) {
      const tableDetail = state[DATA_DEVICE_GROUPS_TABLE_DETAIL];
      let selectedDeviceGroups = state[DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL];

      if (isArray(payload)) {
        if (tableDetail?.data) {
          payload.forEach((device) => {
            const deviceIndex = findIndex(tableDetail.data, ['id', device.id]);
            if (deviceIndex !== -1) {
              tableDetail.data[deviceIndex] = {
                ...tableDetail.data[deviceIndex],
                isSelected: true,
              };
            }
          });
          selectedDeviceGroups = [...payload];
        }
        state[DATA_DEVICE_GROUPS_TABLE_DETAIL] = tableDetail;
        state[DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL] = selectedDeviceGroups;
      }
    },

    updateDeviceGroupAssignmentSelection(state, { payload }) {
      const tableDetail = state[DATA_DEVICE_GROUPS_TABLE_DETAIL];
      let selectedDeviceGroups = state[DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL];

      if (payload) {
        if (tableDetail?.data) {
          const deviceIndex = findIndex(tableDetail.data, ['id', payload.id]);
          if (deviceIndex !== -1) {
            const isSelected = tableDetail.data[deviceIndex].isSelected;
            tableDetail.data[deviceIndex] = {
              ...tableDetail.data[deviceIndex],
              isSelected: !isSelected,
            };
            if (tableDetail.data[deviceIndex].isSelected) {
              selectedDeviceGroups = [
                ...selectedDeviceGroups,
                { id: payload.id, name: payload.name },
              ];
            } else {
              const selectedDeviceGroupIndex = findIndex(selectedDeviceGroups, ['id', payload.id]);
              selectedDeviceGroups.splice(selectedDeviceGroupIndex, 1);
            }
          }
        }
      } else {
        selectedDeviceGroups = state[DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL];
      }
      state[DATA_DEVICE_GROUPS_TABLE_DETAIL] = tableDetail;
      state[DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL] = selectedDeviceGroups;
    },
  },
});

const {
  updateTableDetail,
  updateEntityTableDetail,
  updateAssignEntitiesTableDetail,
  updateSelectedEntitiesDetail,
  updateConstraints,
  updateGroupUsersTableDetail,
  resetTableDetail,
  updateDeviceGroupsTableDetail,
  updateDeviceGroupAssignments,
  updateDeviceGroupAssignmentSelection,
} = serviceEntitlementsSlice.actions;

export const assignEntities =
  ({ id, payload }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/${id}/runtime/assignments`;

    await http.post(requestUrl, payload);
  };

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    groupname = '',
    linkedServicesOnly = true,
    includeDefaultServices = true,
    includeDepSvces = true,
    runtime = true,
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT +
      `?limit=${pageSize}&offset=${pageOffset}&linkedServicesOnly=${linkedServicesOnly}&includeDefaultServices=${includeDefaultServices}&includeDepSvces=${includeDepSvces}&runtime=${runtime}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name.trim()}`;
    }

    if (groupname) {
      requestUrl += `&groupname=${groupname.trim()}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const getEntityList =
  ({
    pageSize = DEFAULT_ENTITY_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_ENTITY_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    id = '',
    name = '',
    entityType = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT + `/${id}/runtime/assignments?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    if (entityType) {
      requestUrl += `&type=${entityType}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateEntityTableDetail(response.data || []));
      return response?.data?.records;
    }
  };

export const getAllEntitiesForAssignment =
  ({
    pageSize = DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    type = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      ENDPOINT +
      `${type === 'GROUP' ? 'groups' : 'users'}` +
      `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    if (type === 'GROUP') {
      requestUrl += `&excludeGroups=SERVICE_ENTITLEMENT_DISABLED`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateAssignEntitiesTableDetail(response.data || []));
    }
  };

export const remove =
  ({ id, tservice, entityType }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${tservice.id}/runtime/assignments/${id}`;

    await http.delete(requestUrl);

    dispatch(getEntityList({ id: tservice.id, entityType }));
  };

export const bulkDelete =
  ({ ids, tservice, entityType }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${tservice.id}/runtime/assignments/bulk-delete`;
    const payload = [...ids];

    await http.put(requestUrl, payload);

    dispatch(getEntityList({ id: tservice.id, entityType }));
  };

export const setSelectedEntities =
  ({ selectedEntities, pageOffset, resetEntitiesList }) =>
  (dispatch) => {
    dispatch(updateSelectedEntitiesDetail({ selectedEntities, pageOffset, resetEntitiesList }));
  };

export const getServiceConstraints = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/service-constraints';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateConstraints(response.data));
  }
};

export const getGroupUsers =
  ({
    pageSize = DEFAULT_GROUP_USERS_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_GROUP_USERS_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    id = '',
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = ENDPOINT + `groups/${id}/users?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateGroupUsersTableDetail(response.data));
    }
  };

export const getDeviceGroups =
  ({
    pageSize = DEFAULT_DEVICE_GROUPS_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_DEVICE_GROUPS_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    id = '',
    name = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      ENDPOINT + `services/${id}/device-groups?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${name}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateDeviceGroupsTableDetail(response?.data));
    }
  };

export const getDeviceGroupAssignments =
  ({ id = '' } = {}) =>
  async (dispatch) => {
    let requestUrl = ENDPOINT + `services/${id}/device-groups/assignments`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateDeviceGroupAssignments(response?.data));
    }
  };

export const resetTableData = () => async (dispatch) => {
  dispatch(resetTableDetail());
};

export const setDeviceGroups =
  ({ id, groups }) =>
  async () => {
    let requestUrl = ENDPOINT + `services/${id}/device-groups/assignments`;

    const payload = [...groups];

    await http.put(requestUrl, payload);
  };

export const updateSelection = (data) => async (dispatch) => {
  dispatch(updateDeviceGroupAssignmentSelection(data));
};

export default serviceEntitlementsSlice;
