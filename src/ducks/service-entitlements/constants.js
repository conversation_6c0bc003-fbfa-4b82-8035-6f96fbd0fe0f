export const ENDPOINT = '/admin/internal-api/v1/';

export const API_ENDPOINT = '/admin/internal-api/v1/services';

export const USERS_API_ENDPOINT = '/admin/internal-api/v1/users';

export const REDUCER_KEY = 'serviceEntitlements';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_ENTITY_TABLE_DETAIL = 'entityTableDetail';

export const DATA_GROUP_USERS_TABLE_DETAIL = 'groupUsersTableDetail';

export const DATA_DEVICE_GROUPS_TABLE_DETAIL = 'deviceGroupsTableDetail';

export const DATA_ASSIGN_ENTITIES_TABLE_DETAIL = 'assignEntitiesTableDetail';

export const DATA_SELECTED_ENTITIES_TABLE_DETAIL = 'selectedEntitiesTableDetail';

export const DATA_SERVICE_CONSTRAINTS = 'serviceConstraints';

export const DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL = 'deviceGroupAssignments';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row.displayName || '---',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'cloudName',
    accessorFn: (row) => row.cloudDomainName || '---',
    Header: 'CLOUD_NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'orgName',
    accessorFn: (row) => row.orgName || '---',
    Header: 'ORG_NAME',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
  },
];

const DEFAULT_ENTITY_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row?.resource?.name || '---',
    Header: 'NAME',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'userId',
    accessorFn: (row) => row?.resource?.loginName || '---',
    Header: 'USER_ID',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
  },
  {
    id: 'groupUsers',
    Header: 'USERS',
    minSize: 100,
    size: 100,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortType: 'alphanumeric',
  },
];

const DEFAULT_GROUP_USERS_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row?.displayName || row?.name || '-',
    Header: 'NAME',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'userId',
    accessorFn: (row) => row?.loginName || row?.primaryEmail || 'NONE',
    Header: 'USER_ID',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
];

const DEFAULT_DEVICE_GROUPS_TABLE_COLUMNS_DETAILS = [
  {
    id: 'groupSelector',
    Header: '',
    accessorFn: (row) => row?.id || '--',
    disableSortBy: true,
    defaultCanSort: false,
    minSize: 20,
    size: 20,
  },
  {
    id: 'id',
    accessorFn: (row) => row?.id || '--',
    Header: 'ID',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'name',
    accessorFn: (row) => row?.name || '--',
    Header: 'NAME',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
];

const DEFAULT_ASSIGN_ENTITIES_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.displayName || row.name || 'NONE',
    Header: 'NAME',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'loginName',
    accessorFn: (row) => row.loginName || 'NONE',
    Header: 'USER_ID',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'groupUsers',
    Header: 'USERS',
    minSize: 200,
    size: 200,
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_GROUP_USERS_TABLE_CONFIG = {
  columns: DEFAULT_GROUP_USERS_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_DEVICE_GROUPS_TABLE_CONFIG = {
  columns: DEFAULT_DEVICE_GROUPS_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_ENTITY_TABLE_CONFIG = {
  columns: DEFAULT_ENTITY_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG = {
  columns: DEFAULT_ASSIGN_ENTITIES_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ENTITY_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_GROUP_USERS_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_DEVICE_GROUPS_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL = [];

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_ENTITY_TABLE_DETAIL]: DEFAULT_ENTITY_TABLE_DETAIL,
  [DATA_GROUP_USERS_TABLE_DETAIL]: DEFAULT_GROUP_USERS_TABLE_DETAIL,
  [DATA_DEVICE_GROUPS_TABLE_DETAIL]: DEFAULT_DEVICE_GROUPS_TABLE_DETAIL,
  [DATA_ASSIGN_ENTITIES_TABLE_DETAIL]: DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  [DATA_SELECTED_ENTITIES_TABLE_DETAIL]: DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
  [DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL]: DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
};
