import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_ASSIGN_ENTITIES_TABLE_DETAIL,
  DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
  DATA_DEVICE_GROUPS_TABLE_DETAIL,
  DATA_ENTITY_TABLE_DETAIL,
  DATA_GROUP_USERS_TABLE_DETAIL,
  DATA_SELECTED_ENTITIES_TABLE_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG,
  DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
  DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
  DEFAULT_DEVICE_GROUPS_TABLE_CONFIG,
  DEFAULT_DEVICE_GROUPS_TABLE_DETAIL,
  DEFAULT_ENTITY_TABLE_CONFIG,
  DEFAULT_ENTITY_TABLE_DETAIL,
  DEFAULT_GROUP_USERS_TABLE_CONFIG,
  DEFAULT_GROUP_USERS_TABLE_DETAIL,
  DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectEntityTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_ENTITY_TABLE_DETAIL] || DEFAULT_ENTITY_TABLE_DETAIL,
);

export const selectEntityTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_ENTITY_TABLE_CONFIG,
);

export const selectGroupUsersTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_GROUP_USERS_TABLE_CONFIG,
);

export const selectDeviceGroupsTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_DEVICE_GROUPS_TABLE_CONFIG,
);

export const selectAssignEntitiesTableConfig = createSelector(
  baseSelector,
  () => DEFAULT_ASSIGN_ENTITIES_TABLE_CONFIG,
);

export const selectAssignEntitiesTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_ASSIGN_ENTITIES_TABLE_DETAIL] || DEFAULT_ASSIGN_ENTITIES_TABLE_DETAIL,
);

export const selectedEntitiesTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_SELECTED_ENTITIES_TABLE_DETAIL] || DEFAULT_SELECTED_ENTITIES_TABLE_DETAIL,
);

export const selectGroupUsersTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_GROUP_USERS_TABLE_DETAIL] || DEFAULT_GROUP_USERS_TABLE_DETAIL,
);

export const selectDeviceGroupsTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_DEVICE_GROUPS_TABLE_DETAIL] || DEFAULT_DEVICE_GROUPS_TABLE_DETAIL,
);

export const selectDeviceGroupsAssignmentsDetail = createSelector(
  baseSelector,
  (state) =>
    state[DATA_DEVICE_GROUPS_ASSIGNMENTS_DETAIL] || DEFAULT_DEVICE_GROUPS_ASSIGNMENTS_DETAIL,
);
