import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { getAttachmentDetail } from '../helper';
import {
  API_ENDPOINT,
  DATA_IDPS_DETAIL,
  DATA_IDP_CERTIFICATE,
  DATA_METADATA,
  DATA_NEW_IDP_DETAIL,
  DATA_PRIMARY_IDP_TABLE_DETAIL,
  DATA_SCIM_AUTH_METHODS,
  DATA_SECONDARY_IDP_TABLE_DETAIL,
  DATA_SIGNING_ALGORITHMS,
  DATA_SP_CERTIFICATES,
  DATA_SP_CERTIFICATE_TYPES,
  DATA_VENDOR_NAMES,
  DEFAULT_PRIMARY_IDP_TABLE_DETAIL,
  DEFAULT_STATE,
  REDUCER_KEY,
} from './constants';

const externalIdentitiesSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updatePrimaryIdpTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_PRIMARY_IDP_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_PRIMARY_IDP_TABLE_DETAIL] = tableDetail;
      }
    },
    updateSecondaryIdpTableDetail(
      state,
      { payload: { records, pageSize, pageOffset, totalRecord } },
    ) {
      const tableDetail = state[DATA_SECONDARY_IDP_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_SECONDARY_IDP_TABLE_DETAIL] = tableDetail;
      }
    },
    updateIdpsDetail(state, { payload }) {
      state[DATA_IDPS_DETAIL] = payload;
    },
    updateNewIdpDetail(state, { payload }) {
      state[DATA_NEW_IDP_DETAIL] = payload;
    },
    updateSCIMAuthMethods(state, { payload }) {
      state[DATA_SCIM_AUTH_METHODS] = payload;
    },
    updateSigningAlgorithms(state, { payload }) {
      state[DATA_SIGNING_ALGORITHMS] = payload;
    },
    updateSPCertificateTypes(state, { payload }) {
      state[DATA_SP_CERTIFICATE_TYPES] = payload;
    },
    updateVendorNames(state, { payload }) {
      state[DATA_VENDOR_NAMES] = payload;
    },
    updateMetadata(state, { payload }) {
      state[DATA_METADATA] = payload;
    },
    updateIdpCertificate(state, { payload }) {
      state[DATA_IDP_CERTIFICATE] = payload;
    },
    updateSPCertificates(state, { payload: { certUse, data } }) {
      const spCertificates = { ...state[DATA_SP_CERTIFICATES] };

      spCertificates[certUse] = data;

      state[DATA_SP_CERTIFICATES] = spCertificates;
    },
  },
});

const {
  updatePrimaryIdpTableDetail,
  updateSecondaryIdpTableDetail,
  updateIdpsDetail,
  updateNewIdpDetail,
  updateSCIMAuthMethods,
  updateSigningAlgorithms,
  updateSPCertificateTypes,
  updateVendorNames,
  updateMetadata,
  updateIdpCertificate,
  updateSPCertificates,
} = externalIdentitiesSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_PRIMARY_IDP_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_PRIMARY_IDP_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    defaultIdp = false,
  } = {}) =>
  async (dispatch) => {
    let requestUrl =
      API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}&defaultIdp=${defaultIdp}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      if (defaultIdp) {
        dispatch(updatePrimaryIdpTableDetail(response.data || []));
      } else {
        dispatch(updateSecondaryIdpTableDetail(response.data || []));
      }
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  await http.post(requestUrl, payload);

  dispatch(getList({ defaultIdp: detail.default }));
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.id}`;
  const payload = { ...detail };

  if (payload.alertMessage) {
    delete payload.alertMessage;
  }

  await http.put(requestUrl, payload);

  dispatch(getList({ defaultIdp: detail.default }));
};

export const remove =
  ({ id, default: defaultIdp }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList({ defaultIdp }));
  };

export const getIdpDetail = (id) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${id}`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateIdpsDetail(response.data));
  }
};

export const getNewIdpDetail =
  ({ defaultIdp }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/new?defaultIdp=${defaultIdp}`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateNewIdpDetail(response.data));
    }
  };

export const clearNewIdpDetail = () => async (dispatch) => {
  dispatch(updateNewIdpDetail({}));
};

export const removeIdpCert =
  ({ certHash }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/clear-idp-cert?certHash=${certHash}`;

    await http.delete(requestUrl);
  };

export const downloadSPCertificate =
  ({ id }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/download-sp-certificate?certificateId=${id}`;

    const response = await http.get(requestUrl);

    if (response?.data) {
      return getAttachmentDetail(response);
    }
  };

export const getSCIMAuthMethods = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/scim-auth-methods';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSCIMAuthMethods(response.data));
  }
};

export const getSigningAlgorithms = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/signing-algorithms';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSigningAlgorithms(response.data));
  }
};

export const getSPCertificateTypes = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/sp-certificate-types';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSPCertificateTypes(response.data));
  }
};

export const getVendorList = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/vendor-names';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateVendorNames(response.data));
  }
};

export const getMetaData =
  ({ metadataurl, idpProfileType = 'SAML' }) =>
  async (dispatch) => {
    const requestUrl =
      API_ENDPOINT + `/fetch-metadata?metadataurl=${metadataurl}&idpProfileType=${idpProfileType}`;

    const response = await http.post(requestUrl);

    if (response?.data) {
      dispatch(updateMetadata({ ...response.data, idpProfileType }));
    }
  };

export const getSPCertificates = (certUse) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/sp-certificates?certUse=${certUse}`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateSPCertificates({ certUse, data: response.data }));
  }
};

export const uploadIDPCert = (filesDetail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/upload-idp-cert`;

  const files = new FormData();
  files.append('uploadfile', filesDetail[0]);

  const response = await http.post(requestUrl, files, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  if (response?.data) {
    dispatch(updateIdpCertificate(response.data));
  }
};

export const uploadMetadata = (filesDetail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/upload-metadata`;

  const files = new FormData();
  files.append('uploadfile', filesDetail[0]);

  const response = await http.post(requestUrl, files, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  if (response?.data) {
    dispatch(updateMetadata({ idpProfileType: 'SAML', ...response.data }));
  }
};

export const downloadMetadata = (id) => async () => {
  const requestUrl = API_ENDPOINT + `/${id}/download-metadata`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    return getAttachmentDetail(response);
  }
};

export const resetMetadata = () => (dispatch) => {
  dispatch(updateMetadata({}));
};

export const getBearerToken = () => async () => {
  const requestUrl = API_ENDPOINT + `/bearer-token`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    return response.data;
  }
};

export default externalIdentitiesSlice;
