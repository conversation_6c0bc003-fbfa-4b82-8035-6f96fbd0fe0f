export const REDUCER_KEY = 'external-identities';

export const API_ENDPOINT = '/admin/internal-api/v1/idps';

export const DATA_PRIMARY_IDP_TABLE_DETAIL = 'primaryIDPTableDetail';
export const DATA_SECONDARY_IDP_TABLE_DETAIL = 'secondaryIDPTableDetail';
export const DATA_IDPS_DETAIL = 'idpsDetail';
export const DATA_NEW_IDP_DETAIL = 'newIdpDetail';
export const DATA_SCIM_AUTH_METHODS = 'scimAuthMethods';
export const DATA_SIGNING_ALGORITHMS = 'signingAlgorithms';
export const DATA_SP_CERTIFICATE_TYPES = 'spCertificateTypes';
export const DATA_VENDOR_NAMES = 'vendorNames';
export const DATA_METADATA = 'metadata';
export const DATA_IDP_CERTIFICATE = 'idpCertificate';
export const DATA_SP_CERTIFICATES = 'spCertificates';
export const DATA_AUTHENTICATION_METHODS = 'authenticationMethod';

const DEFAULT_PRIMARY_IDP_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    size: 250,
    minSize: 250,
    enableSorting: false,
  },
  {
    id: 'type',
    accessorFn: (row) => row.type || 'SAML',
    Header: 'TYPE',
    size: 100,
    minSize: 100,
    enableResizing: false,
    enableSorting: false,
  },
  {
    id: 'status',
    accessorFn: (row) => (row.status ? 'Enabled' : 'Disabled'),
    Header: 'STATUS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    enableSorting: false,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    enableSorting: false,
  },
];

export const DEFAULT_PRIMARY_IDP_TABLE_CONFIG = {
  columns: DEFAULT_PRIMARY_IDP_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'ruleOrder' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_PRIMARY_IDP_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

const DEFAULT_SECONDARY_IDP_TABLE_COLUMNS_DETAILS = [
  {
    id: 'number',
    Header: 'TABLE_NUMBER',
    minSize: 50,
    size: 50,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'NAME',
    size: 200,
    minSize: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'type',
    accessorFn: (row) => row.type || 'SAML',
    Header: 'TYPE',
    size: 100,
    minSize: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'domain',
    accessorFn: (row) => row.domains.map(({ name }) => name).join(', ') || 'SAML',
    Header: 'DOMAIN',
    size: 200,
    minSize: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => (row.status ? 'Enabled' : 'Disabled'),
    Header: 'STATUS',
    size: 100,
    minSize: 100,
    defaultanSort: true,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    enableSorting: false,
  },
];

export const DEFAULT_SECONDARY_IDP_TABLE_CONFIG = {
  columns: DEFAULT_SECONDARY_IDP_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_SECONDARY_IDP_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_IDPS_DETAIL = {};

export const DEFAULT_NEW_IDP_DETAIL = {
  id: '',
  oidcConfigInfo: {
    redirectURI: '',
  },
  samlConfigInfo: {
    samlAssertionEncEnabled: false,
    samlRequestSignEnabled: false,
    samlSpAcsUrl: '',
    samlSpEntityId: '',
  },
};

export const DEFAULT_SCIM_AUTH_METHODS = [];

export const DEFAULT_SIGNING_ALGORITHMS = [];

export const DEFAULT_SP_CERTIFICATE_TYPES = {
  SIGNING: 'SIGNING',
  ENCRYPTION: 'ENCRYPTION',
  BOTH_ENCRYPTION_SIGNING: 'BOTH_ENCRYPTION_SIGNING',
};

export const DEFAULT_VENDOR_NAMES = [];

export const DEFAULT_METADATA = {};

export const DEFAULT_IDP_CERTIFICATE = {};

export const DEFAULT_SP_CERTIFICATE = {};

export const DEFAULT_AUTHENTICATION_METHODS = ['BEARER_TOKEN'];

export const DEFAULT_STATE = {
  [DATA_PRIMARY_IDP_TABLE_DETAIL]: DEFAULT_PRIMARY_IDP_TABLE_DETAIL,
  [DATA_SECONDARY_IDP_TABLE_DETAIL]: DEFAULT_SECONDARY_IDP_TABLE_DETAIL,
  [DATA_IDPS_DETAIL]: DEFAULT_IDPS_DETAIL,
  [DATA_NEW_IDP_DETAIL]: DEFAULT_NEW_IDP_DETAIL,
  [DATA_SCIM_AUTH_METHODS]: DEFAULT_SCIM_AUTH_METHODS,
  [DATA_SIGNING_ALGORITHMS]: DEFAULT_SIGNING_ALGORITHMS,
  [DATA_SP_CERTIFICATE_TYPES]: DEFAULT_SP_CERTIFICATE_TYPES,
  [DATA_VENDOR_NAMES]: DEFAULT_VENDOR_NAMES,
  [DATA_METADATA]: DEFAULT_METADATA,
  [DATA_IDP_CERTIFICATE]: DEFAULT_IDP_CERTIFICATE,
  [DATA_SP_CERTIFICATES]: DEFAULT_SP_CERTIFICATE,
  [DATA_AUTHENTICATION_METHODS]: DEFAULT_AUTHENTICATION_METHODS,
};
