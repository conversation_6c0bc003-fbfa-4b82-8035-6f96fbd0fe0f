export const REDUCER_KEY = 'accessPolicies';

export const API_ENDPOINT = '/admin/internal-api/v1/policies';

export const DATA_TABLE_DETAIL = 'tableDetail';
export const DATA_API_CLIENTS_TABLE_DETAIL = 'apiClientsTableDetail';
export const DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL = 'resourceServTableDetail';

export const DATA_CRITERIA_VALUES = 'criteriaValues';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || 'NONE',
    Header: 'RULE_NAME',
    size: 100,
    minSize: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'apiClients',
    accessorFn: (row) => row.apiClients?.map?.((clients) => clients.name)?.join?.(', ') || 'NONE',
    Header: 'API_CLIENTS',
    size: 100,
    minSize: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'criteria',
    Header: 'CRITERIA',
    size: 300,
    minSize: 300,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'action',
    accessorFn: (row) => row.ruleAction || '',
    Header: 'RULE_ACTION',
    size: 100,
    minSize: 100,
    maxSize: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => row.ruleStatus || '',
    Header: 'RULE_STATUS',
    size: 100,
    minSize: 100,
    maxSize: 100,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'basic',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'ruleOrder' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_API_CLIENTS_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_CRITERIA_VALUES = [];

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_API_CLIENTS_TABLE_DETAIL]: DEFAULT_API_CLIENTS_TABLE_DETAIL,
  [DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL]: DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  [DATA_CRITERIA_VALUES]: DEFAULT_CRITERIA_VALUES,
};
