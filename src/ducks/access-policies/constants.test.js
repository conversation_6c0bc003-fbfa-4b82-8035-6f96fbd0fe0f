import {
  DATA_API_CLIENTS_TABLE_DETAIL,
  DATA_CRITERIA_VALUES,
  DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_API_CLIENTS_TABLE_DETAIL,
  DEFAULT_CRITERIA_VALUES,
  DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_COLUMNS_DETAILS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
} from './constants';

describe('Access Policies Constants', () => {
  it('should have correct default table columns details', () => {
    expect(DEFAULT_TABLE_COLUMNS_DETAILS).toHaveLength(6);
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[0]).toHaveProperty('id', 'name');
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[1]).toHaveProperty('id', 'apiClients');
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[2]).toHaveProperty('id', 'criteria');
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[3]).toHaveProperty('id', 'action');
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[4]).toHaveProperty('id', 'status');
    expect(DEFAULT_TABLE_COLUMNS_DETAILS[5]).toHaveProperty('id', 'actions');
  });

  it('should have correct default table config', () => {
    expect(DEFAULT_TABLE_CONFIG).toHaveProperty('columns', DEFAULT_TABLE_COLUMNS_DETAILS);
    expect(DEFAULT_TABLE_CONFIG.initialState).toHaveProperty('sortBy', [{ id: 'ruleOrder' }]);
    expect(DEFAULT_TABLE_CONFIG.initialState).toHaveProperty('hiddenColumns', []);
    expect(DEFAULT_TABLE_CONFIG).toHaveProperty('onFiltersApply', expect.any(Function));
    expect(DEFAULT_TABLE_CONFIG).toHaveProperty('showColumnLayoutConfigurer', false);
    expect(DEFAULT_TABLE_CONFIG).toHaveProperty('showRowTooltip', false);
  });

  it('should have correct default table detail', () => {
    expect(DEFAULT_TABLE_DETAIL).toEqual({
      totalRecord: -1,
      pageSize: 100,
      pageOffset: 0,
      hasFetchedAllRecords: false,
      data: [],
      hasData: false,
      hasError: false,
      error: {},
    });
  });

  it('should have correct default API clients table detail', () => {
    expect(DEFAULT_API_CLIENTS_TABLE_DETAIL).toEqual({
      totalRecord: -1,
      pageSize: 100,
      pageOffset: 0,
      hasFetchedAllRecords: false,
      data: [],
      hasData: false,
      hasError: false,
      error: {},
    });
  });

  it('should have correct default resource server scopes table detail', () => {
    expect(DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL).toEqual({
      totalRecord: -1,
      pageSize: 100,
      pageOffset: 0,
      hasFetchedAllRecords: false,
      data: [],
      hasData: false,
      hasError: false,
      error: {},
    });
  });

  it('should have correct default criteria values', () => {
    expect(DEFAULT_CRITERIA_VALUES).toEqual([]);
  });

  it('should have correct default state', () => {
    expect(DEFAULT_STATE).toEqual({
      [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
      [DATA_API_CLIENTS_TABLE_DETAIL]: DEFAULT_API_CLIENTS_TABLE_DETAIL,
      [DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL]: DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
      [DATA_CRITERIA_VALUES]: DEFAULT_CRITERIA_VALUES,
    });
  });
});
