import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  DATA_API_CLIENTS_TABLE_DETAIL,
  DATA_CRITERIA_VALUES,
  DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DATA_TABLE_DETAIL,
  DEFAULT_API_CLIENTS_TABLE_DETAIL,
  DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectApiClientsTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_API_CLIENTS_TABLE_DETAIL] || DEFAULT_API_CLIENTS_TABLE_DETAIL,
);

export const selectApiClientsList = createSelector(selectApiClientsTableDetail, ({ data }) => {
  return getDropDownList({ list: data, labelKey: 'clientName', valueKey: 'clientId' });
});

export const selectIsApiClientsTotalRecordsFetched = createSelector(
  selectApiClientsTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectResourceServerScopesTableDetail = createSelector(
  baseSelector,
  (state) =>
    state[DATA_RESOURCE_SERVER_SCOPES_TABLE_DETAIL] || DEFAULT_RESOURCE_SERVER_SCOPES_TABLE_DETAIL,
);

export const selectResourceServerScopesList = createSelector(
  selectResourceServerScopesTableDetail,
  ({ data }) => {
    return getDropDownList({ list: data, lite: false });
  },
);

export const selectIsResourceServerScopesTotalRecordsFetched = createSelector(
  selectResourceServerScopesTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectCriteriaValues = createSelector(
  baseSelector,
  (state) => state[DATA_CRITERIA_VALUES],
);

export const selectDaysOfWeekEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.daysOfWeek || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});

export const selectFrequenciesEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.frequencies || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});

export const selectOperationsEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.operation || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});

export const selectTimeZonesEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.timezones || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});

export const selectWeeksOfMonthEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.weeksOfMonth || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});

export const selectRuleActionsEnums = createSelector(selectCriteriaValues, (state) => {
  const enums = state.ruleActions || [];

  return enums.map(({ displayName, name }) => ({ label: displayName, value: name }));
});
