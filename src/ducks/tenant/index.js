import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, API_EUSA_ENDPOINT, DEFAULT_STATE, REDUCER_KEY } from './constants';

const tenantSlice = createSlice({ name: REDUCER_KEY, initialState: DEFAULT_STATE, reducers: {} });

export const onboardVerify = (payload) => async () => {
  await http.post(`${API_ENDPOINT}/onboard-verify`, payload);
};

export const onboard = (payload) => async () => {
  await http.post(`${API_ENDPOINT}/onboard`, payload);
};

export const getEUSAContent = () => async () => {
  const response = await http.get(API_EUSA_ENDPOINT);

  if (response?.data) {
    return response.data || '';
  }
};

export const getOnboardStatusPolling =
  ({ timeout = 3 } = {}) =>
  async (dispatch) => {
    const requestUrl = `${API_ENDPOINT}/onboard-status`;

    let response = '';

    await new Promise((resolve, reject) => {
      setTimeout(() => {
        http
          .get(requestUrl)
          .then((apiResponse) => {
            response = apiResponse.data;

            resolve(response);
          })
          .catch((error) => {
            reject(error);
          });
      }, timeout * 1000);
    });

    if (response?.status) {
      return response;
    } else {
      return await dispatch(getOnboardStatusPolling());
    }
  };

export const sendEmailOTP = (email) => async () => {
  const response = await http.post(`${API_ENDPOINT}/email/challenge`, {
    email: email,
  });
  // TO DO once email service is working
  if (response) {
    return response;
  }
};

export const verifyEmailOTP = (otp) => async () => {
  const response = await http.post(`${API_ENDPOINT}/email/verify`, {
    code: otp,
  });
  if (response) {
    return response;
  }
};

export const updatePseudoDomain =
  ({ pseudoDomainPrefix }) =>
  async () => {
    const response = await http.put(`${API_ENDPOINT}/pseudo-domain`, {
      pseudoDomainPrefix,
    });

    if (response) {
      return response;
    }
  };

export default tenantSlice;
