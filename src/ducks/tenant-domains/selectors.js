import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_IDP_DOMAINS,
  DATA_TABLE_DETAIL,
  DEFAULT_IDP_DOMAINS,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectDomainNameList = createSelector(selectTableDetail, (tableDetail) => {
  const list = tableDetail?.data?.map?.(({ id, name, ...rest }) => ({
    label: name,
    value: id,
    ...rest,
  }));

  return list;
});

export const selectIDPDomains = createSelector(
  baseSelector,
  (state) => state[DATA_IDP_DOMAINS] || DEFAULT_IDP_DOMAINS,
);

export const selectDomainsByType = createSelector(selectTableDetail, (tableDetail) => {
  const { data } = tableDetail;
  const tenantDomains = {
    primaryDomains: [],
    guestDomains: [],
    arbitraryDomains: [],
  };
  data?.forEach((domain) => {
    if (domain?.guest) {
      tenantDomains.guestDomains.push(domain?.name);
    } else if (domain?.arbitrary) {
      tenantDomains.arbitraryDomains.push(domain);
    } else {
      tenantDomains.primaryDomains.push(domain);
    }
  });
  return tenantDomains;
});
