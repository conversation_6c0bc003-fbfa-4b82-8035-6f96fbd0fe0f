import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_IDP_DOMAINS,
  DATA_TABLE_DETAIL,
  DEFAULT_IDP_DOMAINS,
  DEFAULT_IDP_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';
import { selectTableDetail } from './selectors';

const tenantDomainsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        } else {
          tableDetail.hasFetchedAllRecords = false;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateIDPDomains(state, { payload }) {
      let idpDomains = payload;
      if (payload?.records) {
        idpDomains = payload?.records;
      }
      state[DATA_IDP_DOMAINS] = idpDomains || DEFAULT_IDP_DOMAINS;
    },
  },
});

const { updateTableDetail, updateIDPDomains } = tenantDomainsSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    domainType = 'all',
    requirePseudoDomain = false,
    requireInternalDomain = false,
    all = false,
  } = {}) =>
  async (dispatch, getState) => {
    let requestUrl =
      API_ENDPOINT +
      `?limit=${pageSize}&offset=${pageOffset}&requirePseudoDomain=${requirePseudoDomain}&domainType=${domainType}&requireInternalDomain=${requireInternalDomain}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));

      const tableDetail = selectTableDetail(getState());

      if (all && !tableDetail.hasFetchedAllRecords) {
        return dispatch(
          getList({
            pageSize,
            pageOffset: pageOffset + pageSize,
            requireTotal: false,
            name,
            domainType,
            requirePseudoDomain,
            requireInternalDomain,
            all,
          }),
        );
      }
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.idpid}`;
  const payload = { ...detail };

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ idpid }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${idpid}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const getIdpDomains =
  ({
    pageSize = DEFAULT_IDP_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_IDP_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    all = false,
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `/idp-domains` + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateIDPDomains(response.data));

      if (all) {
        // return dispatch(
        //   getList({
        //     pageSize,
        //     pageOffset: pageOffset + pageSize,
        //     requireTotal: false,
        //     name,
        //     requirePseudoDomain,
        //     all,
        //   }),
        // );
      }
    }
  };

export const updateGuestDomains = (payload) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/guest-domains`;
  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList({ requireTotal: true, all: true }));
  }
};

export default tenantDomainsSlice;
