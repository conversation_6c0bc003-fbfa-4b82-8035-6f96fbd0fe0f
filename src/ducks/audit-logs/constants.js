import { getTimeRange } from '@zscaler/zui-component-library';

import dayjs from 'dayjs';

export const REDUCER_KEY = 'auditLogs';

export const API_ENDPOINT = '/admin/internal-api/v1/audit-logs';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'number',
    Header: 'TABLE_NUMBER',
    minSize: 70,
    size: 70,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'timestamp',
    accessorFn: (row) => dayjs(row.timestamp).format('MMMM DD, YYYY - hh:mm A') || 0,
    Header: 'TIME_STAMP',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actionType',
    accessorFn: (row) => row.actionType || '',
    Header: 'ACTION',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'resourceName',
    accessorFn: (row) => row.objectName || '',
    Header: 'RESOURCE',
    minSize: 250,
    size: 250,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'userName',
    accessorFn: (row) => row.userName || '',
    Header: 'ADMIN_ID',
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'clientIP',
    accessorFn: (row) => row.clientIP || '',
    Header: 'CLIENT_IP',
    minSize: 150,
    size: 150,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'actionResult',
    accessorFn: (row) => row.actionResult || '',
    Header: 'RESULT',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
  },
  {
    id: 'category',
    accessorFn: (row) => row.category || '',
    Header: 'CATEGORY',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
  },
  {
    id: 'dataBeforeAfter',
    accessorFn: (row) => JSON.stringify(row.dataBefore) || '',
    Header: 'VIEW_CHANGES',
    minSize: 150,
    size: 150,
    defaultanSort: false,
  },
  {
    id: 'actionInterface',
    accessorFn: (row) => row.actionInterface || '',
    Header: 'INTERFACE',
    minSize: 100,
    size: 100,
    disableSortBy: true,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'subcategory',
    accessorFn: (row) => row.subcategory || '',
    Header: 'SUB_CATEGORY',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'number' }],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DATA_TIME_RANGE_OPTIONS = 'timeRangeOptions';

export const DEFAULT_TIME_RANGE_OPTION_DETAIL = getTimeRange('REPORT').map((timeRange) => ({
  label: timeRange,
  value: timeRange,
}));

export const DATA_INTERFACES = 'interfaces';

export const DEFAULT_INTERFACES = [];

export const DATA_RESULTS = 'results';

export const DEFAULT_RESULTS = [];

export const DATA_ACTION_TYPES = 'actionTypes';

export const DEFAULT_ACTION_TYPES = [];

export const DATA_CATEGORIES_MAPPING = 'categoriesMapping';

export const DEFAULT_CATEGORIES_MAPPING = {};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_INTERFACES]: DEFAULT_INTERFACES,
  [DATA_RESULTS]: DEFAULT_RESULTS,
  [DATA_ACTION_TYPES]: DEFAULT_ACTION_TYPES,
  [DATA_TIME_RANGE_OPTIONS]: DEFAULT_TIME_RANGE_OPTION_DETAIL,
  [DATA_CATEGORIES_MAPPING]: DEFAULT_CATEGORIES_MAPPING,
};
