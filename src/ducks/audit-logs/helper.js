export const getCategoriesAndSubCategory = ({ categoriesMapping = {}, activeCategory = 'ALL' }) => {
  const mappings = {
    categories: [],
    subCategories: [],
    activeSubCategory: [],
  };

  Object.keys(categoriesMapping).forEach((category) => {
    mappings.categories.push(category);

    mappings.subCategories = [...mappings.subCategories, ...categoriesMapping[category]];
  });

  if (activeCategory === 'ALL') {
    mappings.activeSubCategory = [...mappings.subCategories];
  } else {
    mappings.activeSubCategory = [...(categoriesMapping[activeCategory] || [])];
  }

  return mappings;
};
