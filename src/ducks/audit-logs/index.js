import { createSlice } from '@reduxjs/toolkit';

import dayjs from 'dayjs';
import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { getAttachmentDetail } from '../helper';
import {
  API_ENDPOINT,
  DATA_ACTION_TYPES,
  DATA_CATEGORIES_MAPPING,
  DATA_INTERFACES,
  DATA_RESULTS,
  DATA_TABLE_DETAIL,
  DEFAULT_ACTION_TYPES,
  DEFAULT_CATEGORIES_MAPPING,
  DEFAULT_INTERFACES,
  DEFAULT_RESULTS,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const auditLogsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateInterfaces(state, { payload }) {
      state[DATA_INTERFACES] = payload || DEFAULT_INTERFACES;
    },
    updateResults(state, { payload }) {
      state[DATA_RESULTS] = payload || DEFAULT_RESULTS;
    },
    updateActionTypes(state, { payload }) {
      state[DATA_ACTION_TYPES] = payload || DEFAULT_ACTION_TYPES;
    },
    updateCategoriesMapping(state, { payload }) {
      state[DATA_CATEGORIES_MAPPING] = payload || DEFAULT_CATEGORIES_MAPPING;
    },
  },
});

const {
  updateTableDetail,
  updateInterfaces,
  updateResults,
  updateActionTypes,
  updateCategoriesMapping,
} = auditLogsSlice.actions;

const defaultFilterApiPayload = {
  endTime: 0,
  page: 0,
  pageSize: 10,
  startTime: 0,
};

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    filters = {},
  }) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    const startTime = dayjs().startOf('day').valueOf();
    const endTime = dayjs().endOf('day').valueOf();

    const payload = {
      ...defaultFilterApiPayload,
      startTime,
      endTime,
      ...filters,
    };

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const getInterfacesEnums = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/action-interfaces';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateInterfaces(response.data || []));
  }
};

export const getResultsEnums = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/action-results';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateResults(response.data || []));
  }
};

export const getActionTypesEnums = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/action-types';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateActionTypes(response.data || []));
  }
};

export const getCategoriesEnumsMapping = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + '/enums/category-mappings';

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateCategoriesMapping(response.data || []));
  }
};

export const downloadAuditLogs =
  ({ filters }) =>
  async () => {
    const requestUrl = API_ENDPOINT + `/export/csv`;

    const payload = { ...filters };

    const response = await http.post(requestUrl, payload);

    if (response?.data) {
      return getAttachmentDetail(response);
    }
  };

export default auditLogsSlice;
