import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  DATA_ACTION_TYPES,
  DATA_CATEGORIES_MAPPING,
  DATA_INTERFACES,
  DATA_RESULTS,
  DATA_TABLE_DETAIL,
  DEFAULT_ACTION_TYPES,
  DEFAULT_CATEGORIES_MAPPING,
  DEFAULT_INTERFACES,
  DEFAULT_RESULTS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';
import { getCategoriesAndSubCategory } from './helper';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectInterfacesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_INTERFACES] || DEFAULT_INTERFACES,
);

export const selectInterfacesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_INTERFACES] || DEFAULT_INTERFACES;

  return getDropDownList({ list: data });
});

export const selectResultsEnums = createSelector(
  baseSelector,
  (state) => state[DATA_RESULTS] || DEFAULT_RESULTS,
);

export const selectResultsEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_RESULTS] || DEFAULT_RESULTS;

  return getDropDownList({ list: data });
});

export const selectActionTypesEnums = createSelector(
  baseSelector,
  (state) => state[DATA_ACTION_TYPES] || DEFAULT_ACTION_TYPES,
);

export const selectActionTypesEnumsList = createSelector(baseSelector, (state) => {
  const data = state[DATA_ACTION_TYPES] || DEFAULT_ACTION_TYPES;

  return getDropDownList({ list: data });
});

export const selectCategoriesMapping = createSelector(
  baseSelector,
  (state) => state[DATA_CATEGORIES_MAPPING] || DEFAULT_CATEGORIES_MAPPING,
);

export const selectCategoriesEnums = createSelector(
  selectCategoriesMapping,
  (categoriesMapping) => {
    const { categories } = getCategoriesAndSubCategory({ categoriesMapping });

    return categories || [];
  },
);

export const selectCategoriesEnumsList = createSelector(selectCategoriesEnums, (categories) =>
  getDropDownList({ list: categories }),
);

export const selectActiveSubCategoriesEnums = (categoriesMapping, activeCategory) => {
  const { activeSubCategory } = getCategoriesAndSubCategory({ categoriesMapping, activeCategory });

  return activeSubCategory || [];
};

export const selectActiveSubCategoriesEnumsList = (categoriesMapping, activeCategory) => {
  const { activeSubCategory } = getCategoriesAndSubCategory({ categoriesMapping, activeCategory });

  return getDropDownList({ list: activeSubCategory || [] });
};
