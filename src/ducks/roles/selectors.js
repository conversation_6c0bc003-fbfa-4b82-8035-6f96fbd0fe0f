import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectRolesList = createSelector(selectTableDetail, ({ data }) =>
  data.map(({ id, name, isSystemRole }) => ({
    label: name,
    value: id,
    type: isSystemRole ? 'SYSTEM' : 'CUSTOM',
  })),
);
