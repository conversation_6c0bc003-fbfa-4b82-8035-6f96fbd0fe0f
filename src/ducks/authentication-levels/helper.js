import { cloneDeep } from 'lodash-es';

export const VALIDITY_TYPE_LIST = [
  { label: 'MINUTES', value: 'MINUTES' },
  { label: 'HOURS', value: 'HOURS' },
  { label: 'DAYS', value: 'DAYS' },
];

export const getAuthenticationListDetail = (list = []) => {
  const listDetail = list.map((levelDetail) => {
    const treeMapping = {};
    const levelDetailMapping = {};

    return getAuthenticationLevelMapping({
      level: '1',
      levelDetail,
      levelDetailMapping,
      treeMapping,
      nesting: 0,
      parentLevel: '',
    });

    // return getAuthenticationLevelDetail({ detail: cloneDeep(item), level: '1' });
  });

  return listDetail;
};

export const getAuthenticationLevelMapping = ({
  level = '1',
  levelDetail = {},
  levelDetailMapping = {},
  treeMapping = {},
  nesting = 0,
  parentLevel = '',
}) => {
  levelDetailMapping[levelDetail?.id] = {
    ...levelDetail,
    ...getValidityDetail(levelDetail),
  };

  const { childAuthenticationLevels = [] } = levelDetail;

  const subLevels = [];

  childAuthenticationLevels.forEach((childLevel, idx) => {
    const newLevel = `${level}.${idx + 1}`;

    subLevels.push(newLevel);

    getAuthenticationLevelMapping({
      treeMapping,
      levelDetail: childLevel,
      levelDetailMapping,
      level: newLevel,
      nesting: nesting + 1,
      parentLevel: level,
    });
  });

  treeMapping[level] = {
    id: levelDetail?.id,
    level,
    nesting,
    parentLevel,
    subLevels,
    isNestingAllowed: nesting !== 3,
    isSubLevel: level !== '1',
    isLevelNew: levelDetail?.isLevelNew,
  };

  return { level, levelDetail, levelDetailMapping, treeMapping };
};

export const getApiPayloadFromMapping = ({
  id = '1',
  treeMapping = {},
  levelDetailMapping = {},
}) => {
  const { id: levelId, subLevels = [], isLevelNew = false } = treeMapping[id];

  const levelDetail = cloneDeep(levelDetailMapping[levelId]);

  const childAuthenticationLevels = [];

  subLevels.forEach((subLevelId) => {
    const subLevelDetail = getApiPayloadFromMapping({
      id: subLevelId,
      treeMapping,
      levelDetailMapping,
    });

    childAuthenticationLevels.push(subLevelDetail);
  });

  levelDetail.childAuthenticationLevels = childAuthenticationLevels;

  levelDetail.timeout = getTimeoutInMinutes(levelDetail);

  if (isLevelNew) {
    delete levelDetail.id;
    delete levelDetail.isLevelNew;
  }

  delete levelDetail.validityType;

  return levelDetail;
};

export const getAuthenticationFlatListDetail = (list = []) => {
  const flatList = [];

  list.forEach((levelDetail) => {
    getAuthenticationLevelFlatMapping({
      levelDetail,
      flatList,
    });
  });

  return flatList;
};

export const getAuthenticationLevelFlatMapping = ({ levelDetail, flatList }) => {
  flatList.push(levelDetail);

  const { childAuthenticationLevels = [] } = levelDetail;

  childAuthenticationLevels.forEach((childLevel) => {
    getAuthenticationLevelFlatMapping({
      levelDetail: childLevel,
      flatList,
    });
  });

  return { levelDetail, flatList };
};

export const getValidityDetail = (levelDetail) => {
  let validityType = VALIDITY_TYPE_LIST[2];

  const { timeout } = levelDetail;

  let calculatedTimeout = timeout || 0;

  if (timeout % (60 * 24) === 0) {
    calculatedTimeout = timeout / (60 * 24);
    validityType = VALIDITY_TYPE_LIST[2];
  } else if (timeout % 60 === 0) {
    calculatedTimeout = timeout / 60;
    validityType = VALIDITY_TYPE_LIST[1];
  } else {
    calculatedTimeout = timeout;
    validityType = VALIDITY_TYPE_LIST[0];
  }

  return { validityType, timeout: calculatedTimeout };
};

export const getTimeoutInMinutes = (levelDetail) => {
  const { timeout, validityType } = levelDetail;

  let calculatedTimeout = timeout || 0;

  const validity = validityType?.value;

  if (validity === VALIDITY_TYPE_LIST[2].value) {
    calculatedTimeout = timeout * 60 * 24;
  } else if (validity === VALIDITY_TYPE_LIST[1].value) {
    calculatedTimeout = timeout * 60;
  } else {
    calculatedTimeout = timeout;
  }

  return calculatedTimeout;
};
