import { createSlice } from '@reduxjs/toolkit';

import { http } from '../../utils/http';

import { API_ENDPOINT, DATA_LIST, DEFAULT_STATE, REDUCER_KEY } from './constants';

const authenticationLevelsSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateList(state, { payload }) {
      state[DATA_LIST] = payload || [];
    },
  },
});

const { updateList } = authenticationLevelsSlice.actions;

export const getList = () => async (dispatch) => {
  let requestUrl = API_ENDPOINT;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateList(response.data || []));
  }
};

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update =
  ({ id, payload }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    const response = await http.put(requestUrl, payload);

    if (response?.data) {
      dispatch(getList());
    }
  };

export const remove =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export default authenticationLevelsSlice;
