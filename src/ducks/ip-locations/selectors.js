import { createSelector } from '@reduxjs/toolkit';

import {
  DATA_TABLE_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectIpLocationsList = createSelector(selectTableDetail, ({ data }) =>
  data.map(({ id, name }) => ({ label: name, value: id })),
);
