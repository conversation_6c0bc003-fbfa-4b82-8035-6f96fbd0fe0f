export const REDUCER_KEY = 'ip-locations';

export const API_ENDPOINT = '/admin/internal-api/v1/locations';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'name',
    accessorFn: (row) => row.name || '---',
    Header: 'NAME',
    minSize: 250,
    size: 250,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'country',
    accessorFn: (row) => row.country?.name || '---',
    Header: 'COUNTRY',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'ipInLocations',
    accessorFn: (row) => row.ipInLocations.join('; ') || '---',
    Header: 'IP_ADDRESS',
    minSize: 250,
    size: 250,
    defaultCanSort: false,
  },
  {
    id: 'source',
    accessorFn: (row) => row.source,
    Header: 'SOURCE',
    minSize: 100,
    size: 100,
    defaultCanSort: false,
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    size: 100,
    minSize: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'name' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
