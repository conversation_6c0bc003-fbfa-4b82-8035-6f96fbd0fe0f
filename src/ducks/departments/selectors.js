import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  DATA_DEPARTMENTS,
  DATA_TABLE_DETAIL,
  DEFAULT_DEPARTMENTS,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ totalRecord, data }) => data?.length === totalRecord,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectDepartmentDetail = createSelector(
  baseSelector,
  (state) => state[DATA_DEPARTMENTS] || DEFAULT_DEPARTMENTS,
);

export const selectDepartmentList = createSelector(selectTableDetail, ({ data }) => {
  return getDropDownList({ list: data });
});
