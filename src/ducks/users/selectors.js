import { createSelector } from '@reduxjs/toolkit';
import { getDropDownList } from '@zscaler/zui-component-library';

import {
  ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL,
  DATA_AUTO_GEN_PASS,
  DATA_TABLE_DETAIL,
  DEFAULT_AUTO_GEN_PASS_DETAIL,
  DEFAULT_TABLE_CONFIG,
  DEFAULT_TABLE_DETAIL,
  ENTITLEMENTS,
  ENTITLEMENT_DEFAULT_TABLE_CONFIG,
  ENTITLEMENT_DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
  SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL,
} from './constants';

const baseSelector = (state) => state[REDUCER_KEY];

export const selectTableDetail = createSelector(
  baseSelector,
  (state) => state[DATA_TABLE_DETAIL] || DEFAULT_TABLE_DETAIL,
);
export const selectEntitlementTableDetail = createSelector(
  baseSelector,
  (state) => state[ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL] || ENTITLEMENT_DEFAULT_TABLE_DETAIL,
);

export const selectServiceEntitlementTableDetail = createSelector(
  baseSelector,
  (state) => state[SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL] || ENTITLEMENT_DEFAULT_TABLE_DETAIL,
);

export const selectIsTotalRecordsFetched = createSelector(
  selectTableDetail,
  ({ hasFetchedAllRecords }) => hasFetchedAllRecords,
);

export const selectTableConfig = createSelector(baseSelector, () => DEFAULT_TABLE_CONFIG);

export const selectEntitlementTableConfig = createSelector(
  baseSelector,
  () => ENTITLEMENT_DEFAULT_TABLE_CONFIG,
);

export const selectAutoGenPass = createSelector(
  baseSelector,
  (state) => state[DATA_AUTO_GEN_PASS] || DEFAULT_AUTO_GEN_PASS_DETAIL,
);

export const getUsersLabel = ({ detail: { displayName, name } }) => {
  return `${displayName} (${name})`;
};

export const selectUsersList = createSelector(selectTableDetail, ({ data }) => {
  return getDropDownList({ list: data, getLabel: getUsersLabel });
});

export const selectEntitlements = createSelector(baseSelector, (state) => state[ENTITLEMENTS]);
