export const REDUCER_KEY = 'users';

export const API_ENDPOINT = '/admin/internal-api/v1/users';

export const DATA_TABLE_DETAIL = 'tableDetail';

export const DATA_AUTO_GEN_PASS = 'autoGenPassword';
export const ENTITLEMENTS = 'entitlements';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'number',
    Header: 'TABLE_NUMBER',
    minSize: 50,
    size: 50,
    enableResizing: false,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'displayName',
    accessorFn: (row) => row.displayName || 'NONE',
    Header: 'NAME',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'loginName',
    accessorFn: (row) => row.loginName || 'NONE',
    Header: 'LOGIN_ID',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'status',
    accessorFn: (row) => row.status || false,
    Header: 'STATUS',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: 'basic',
  },
  {
    id: 'actions',
    Header: 'ACTIONS',
    minSize: 100,
    size: 100,
    enableResizing: false,
    disableSortBy: true,
    defaultCanSort: false,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_AUTO_GEN_PASS_DETAIL = '';

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL = 'adminEntitlementTableDetail';
export const SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL = 'serviceEntitlementTableDetail';

const ENTITLEMENT_DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'serviceName',
    accessorFn: (row) => row?.service?.displayName || 'NONE',
    Header: 'NAME',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'cloudName',
    accessorFn: (row) => {
      return row?.service?.cloudDomainName || row?.service?.cloudName || '-';
    },
    Header: 'CLOUD_NAME',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortType: 'alphanumeric',
  },
  {
    id: 'orgName',
    accessorFn: (row) => row?.service?.orgName || '-',
    Header: 'ORGANIZATION_NAME',
    minSize: 120,
    size: 120,
    defaultCanSort: true,
  },
  {
    id: 'roles',
    accessorFn: (row) => row?.tsRoles?.map((itm) => itm.name)?.join(',') || '-',
    Header: 'ROLES',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
  },
];

export const ENTITLEMENT_DEFAULT_TABLE_CONFIG = {
  columns: ENTITLEMENT_DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: '' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};
export const ENTITLEMENT_DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
  [DATA_AUTO_GEN_PASS]: DEFAULT_AUTO_GEN_PASS_DETAIL,
  [ENTITLEMENTS]: {
    entitlements: {},
    roleAssignments: [],
  },
  [ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL]: ENTITLEMENT_DEFAULT_TABLE_DETAIL,
  [SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL]: ENTITLEMENT_DEFAULT_TABLE_DETAIL,
};
