import { HttpResponse, delay, http } from 'msw';

import { API_ENDPOINT } from './constants';

export const USERS_LIST = {
  totalRecord: 2,
  pageOffset: 0,
  pageSize: 100,
  records: [
    {
      id: 'g400000000081',
      loginName: '<EMAIL>',
      displayName: 'admin',
      firstName: 'admin',
      lastName: '',
      primaryEmail: '<EMAIL>',
      groups: [],
      department: {},
      skipMfaUntil: 0,
      status: true,
      source: 'UI',
      guest: false,
      belongInternalDomain: false,
      hostedIdp: false,
      objectName: '<EMAIL>',
      name: '<EMAIL>',
      entityType: 'USER_PROFILE',
    },
    {
      id: 'g400000000082',
      loginName: '<EMAIL>',
      displayName: 'test1',
      firstName: 'test1',
      lastName: 'test1',
      primaryEmail: '<EMAIL>',
      groups: [],
      department: {},
      skipMfaUntil: 0,
      status: true,
      source: 'UI',
      guest: false,
      belongInternalDomain: false,
      hostedIdp: false,
      objectName: '<EMAIL>',
      name: '<EMAIL>',
      entityType: 'USER_PROFILE',
    },
  ],
};

export const usersMockHandler = [
  http.get(API_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json(USERS_LIST);
  }),

  http.put(API_ENDPOINT, async () => {
    await delay(10);
    return HttpResponse.json({});
  }),
];
