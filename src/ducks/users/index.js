import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import { getAttachmentDetail } from '../helper';
import {
  ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL,
  API_ENDPOINT,
  DATA_AUTO_GEN_PASS,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  ENTITLEMENTS,
  REDUCER_KEY,
  SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL,
} from './constants';

const usersSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload: { records, pageSize, pageOffset, totalRecord } }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      }
    },
    updateEntitlementTableDetail(state, { payload: { entitlements } }) {
      const adminEntitlements = entitlements?.filter((itm) => itm.runtime === false) || [];
      const serviceEntitlements = entitlements?.filter((itm) => itm.runtime) || [];
      const adminTableDetail = state[ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL];
      const serviceTableDetail = state[SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL];

      if (isArray(adminEntitlements)) {
        adminTableDetail.data = [...adminEntitlements];
        state[ADMIN_ENTITLEMENT_DATA_TABLE_DETAIL] = adminTableDetail;
      }

      if (isArray(serviceEntitlements)) {
        serviceTableDetail.data = [...serviceEntitlements];
        state[SERVICE_ENTITLEMENT_DATA_TABLE_DETAIL] = serviceTableDetail;
      }
    },
    updateAutoGenPass(state, { payload }) {
      state[DATA_AUTO_GEN_PASS] = payload;
    },
    updateEntitlements(state, { payload }) {
      state[ENTITLEMENTS] = payload;
    },
  },
});

const { updateAutoGenPass, updateTableDetail, updateEntitlementTableDetail } = usersSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    name = '',
    groupname = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (name) {
      requestUrl += `&name=${encodeURIComponent(name.trim())}`;
    }

    if (groupname) {
      requestUrl += `&groupname=${encodeURIComponent(groupname.trim())}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail(response.data || []));
    }
  };

export const add = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT;
  const payload = { ...detail };

  if (!payload.secondaryEmail) {
    delete payload.secondaryEmail;
  }

  delete payload.confirmPassword;

  const response = await http.post(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const update = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${detail.id}`;
  const payload = { ...detail };

  if (!payload.secondaryEmail) {
    delete payload.secondaryEmail;
  }

  if (!payload.password) {
    delete payload.password;

    if (!payload.pwdConfig?.setByUser) {
      delete payload.pwdConfig;
    }
  }

  delete payload.confirmPassword;

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const remove =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}`;

    await http.delete(requestUrl);

    dispatch(getList());
  };

export const bulkRemove =
  ({ ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/bulk-delete`;

    await http.put(requestUrl, ids);

    dispatch(getList());
  };

export const bulkPasswordReset =
  ({ ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/bulk-password-reset`;

    await http.put(requestUrl, ids);

    dispatch(getList());
  };

export const bulkActivate =
  ({ enable, ids }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${enable ? 'activate' : 'deactivate'}`;

    await http.put(requestUrl, ids);

    dispatch(getList());
  };

export const getAutoGeneratedPassword = () => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/auto-gen-pwd`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateAutoGenPass(response?.data));
  }
};

export const importFromCsvPolling =
  ({ override, filesDetail }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/import-async?override=${override}`;

    const files = new FormData();
    files.append('importcsv', filesDetail[0]);

    const response = await http.post(requestUrl, files, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response?.data?.id) {
      const pollingResponse = await dispatch(
        getPollingStatus({ importId: response?.data.id, timeout: 0 }),
      );

      dispatch(getList());

      return pollingResponse?.importResult;
    }

    return {};
  };

export const getPollingStatus =
  ({ importId, timeout = 2 }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/import-async/${importId}/status`;

    let response = '';

    await new Promise((resolve, reject) => {
      setTimeout(() => {
        http
          .get(requestUrl)
          .then((apiResponse) => {
            response = apiResponse;
            resolve(response);
          })
          .catch((error) => {
            reject(error);
          });
      }, timeout * 1000);
    });

    if (response?.data?.importStatus === 'COMPLETED') {
      return response?.data;
    } else {
      return dispatch(getPollingStatus({ importId }));
    }
  };

export const downloadTemplateCSV = () => async () => {
  const requestUrl = API_ENDPOINT + '/import/download-template';

  const response = await http.get(requestUrl);

  if (response?.data) {
    return getAttachmentDetail(response);
  }
};

export const getUserEntitlements = (userId) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/${userId}/entitlements`;

  const response = await http.get(requestUrl);

  if (response?.data) {
    dispatch(updateEntitlementTableDetail(response?.data));
  }
};

export const updateSecuritySettings = (detail) => async (dispatch) => {
  const requestUrl = API_ENDPOINT + `/security-settings/${detail?.id}`;

  const payload = { ...detail };

  if (!payload.secondaryEmail) {
    delete payload.secondaryEmail;
  }

  if (!payload.password) {
    delete payload.password;

    if (!payload.pwdConfig?.setByUser) {
      delete payload.pwdConfig;
    }
  }

  delete payload.confirmPassword;

  const response = await http.put(requestUrl, payload);

  if (response?.data) {
    dispatch(getList());
  }
};

export const removePasswordForHostedIdpUser =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${id}/pwd-setting`;

    const response = await http.delete(requestUrl);
    if (response?.data) {
      dispatch(getList());
    }
  };

export default usersSlice;
