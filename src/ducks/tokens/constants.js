import dayjs from 'dayjs';

export const REDUCER_KEY = 'tokens';

export const API_ENDPOINT = '/admin/internal-api/v1/oauth2-tokens';

export const DATA_TABLE_DETAIL = 'tableDetail';

const DEFAULT_TABLE_COLUMNS_DETAILS = [
  {
    id: 'clientId',
    accessorFn: (row) => row.clientId || '---',
    Header: 'CLIENT_ID',
    minSize: 150,
    size: 150,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'jti',
    accessorFn: (row) => row.id || '---',
    Header: 'JTI',
    minSize: 350,
    size: 350,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'issuedAt',
    accessorFn: (row) => dayjs(row.createdAt).format('MMMM DD, YYYY - hh:mm A') || 0,
    Header: 'ISSUED_AT',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'expiresAt',
    accessorFn: (row) => dayjs(row.expiresAt).format('MMMM DD, YYYY - hh:mm A') || 0,
    Header: 'EXPIRES_AT',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'jwt',
    accessorFn: (row) => row.jwt || '---',
    Header: 'JWT',
    minSize: 100,
    size: 100,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
  {
    id: 'revoked',
    Header: 'STATUS',
    minSize: 200,
    size: 200,
    defaultCanSort: true,
    sortingFn: 'alphanumeric',
  },
];

export const DEFAULT_TABLE_CONFIG = {
  columns: DEFAULT_TABLE_COLUMNS_DETAILS,
  initialState: {
    sortBy: [{ id: 'name' }],
    hiddenColumns: [],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: false,
  showRowTooltip: false,
};

export const DEFAULT_TABLE_DETAIL = {
  totalRecord: -1,
  pageSize: 100,
  pageOffset: 0,
  hasFetchedAllRecords: false,
  data: [],
  hasData: false,
  hasError: false,
  error: {},
};

export const DEFAULT_STATE = {
  [DATA_TABLE_DETAIL]: DEFAULT_TABLE_DETAIL,
};
