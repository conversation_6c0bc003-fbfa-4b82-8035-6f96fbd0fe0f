import { createSlice } from '@reduxjs/toolkit';

import { isArray } from 'lodash-es';

import { http } from '../../utils/http';

import {
  API_ENDPOINT,
  DATA_TABLE_DETAIL,
  DEFAULT_STATE,
  DEFAULT_TABLE_DETAIL,
  REDUCER_KEY,
} from './constants';
import { decodeJwt } from './helper';

const tokensSlice = createSlice({
  name: REDUCER_KEY,
  initialState: DEFAULT_STATE,
  reducers: {
    updateTableDetail(state, { payload }) {
      const tableDetail = state[DATA_TABLE_DETAIL];

      const {
        data: { records, pageSize, pageOffset, totalRecord },
        id,
      } = payload;

      if (isArray(records)) {
        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = pageSize;
        tableDetail.pageOffset = pageOffset;

        records.forEach((record) => {
          record.decodedJwt = decodeJwt(record?.token);
        });

        if (totalRecord !== undefined) {
          tableDetail.totalRecord = totalRecord;
          tableDetail.data = [...records];
        } else {
          tableDetail.data = [...tableDetail.data, ...records];
        }

        if (tableDetail.totalRecord === tableDetail.data.length) {
          tableDetail.hasFetchedAllRecords = true;
        }

        state[DATA_TABLE_DETAIL] = tableDetail;
      } else if (id) {
        const record = payload?.data || {};
        record.decodedJwt = decodeJwt(record?.token);

        tableDetail.error = {};
        tableDetail.hasError = false;
        tableDetail.hasData = true;

        tableDetail.pageSize = 100;
        tableDetail.pageOffset = 0;

        tableDetail.totalRecord = 1;
        tableDetail.data = [record];
        tableDetail.hasFetchedAllRecords = true;
      }
    },
  },
});

const { updateTableDetail } = tokensSlice.actions;

export const getList =
  ({
    pageSize = DEFAULT_TABLE_DETAIL.pageSize,
    pageOffset = DEFAULT_TABLE_DETAIL.pageOffset,
    requireTotal = true,
    clientId = '',
    status = '',
    id = '',
    expiresAfter = '',
    expiresBefore = '',
    issuedBefore = '',
    issuedAfter = '',
  } = {}) =>
  async (dispatch) => {
    let requestUrl = API_ENDPOINT + `?limit=${pageSize}&offset=${pageOffset}`;

    if (requireTotal) {
      requestUrl += `&requireTotal=${requireTotal}`;
    }

    if (clientId) {
      requestUrl += `&clientId=${encodeURIComponent(clientId.trim())}`;
    }

    if (status) {
      requestUrl += `&status=${status}`;
    }

    if (expiresAfter) {
      requestUrl += `&expiresAfter=${expiresAfter}`;
    }

    if (expiresBefore) {
      requestUrl += `&expiresBefore=${expiresBefore}`;
    }

    if (issuedAfter) {
      requestUrl += `&issuedAfter=${issuedAfter}`;
    }

    if (issuedBefore) {
      requestUrl += `&issuedBefore=${issuedBefore}`;
    }

    if (id) {
      requestUrl = API_ENDPOINT + `/${id}`;
    }

    const response = await http.get(requestUrl);

    if (response?.data) {
      dispatch(updateTableDetail({ data: response.data || {}, id }));
    }
  };

export const revoke =
  ({ id }) =>
  async (dispatch) => {
    const requestUrl = API_ENDPOINT + `/${encodeURIComponent(id)}/revoke/`;

    await http.post(requestUrl);

    dispatch(getList());
  };

export default tokensSlice;
