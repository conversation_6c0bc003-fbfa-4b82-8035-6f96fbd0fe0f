export const decodeJwt = (token) => {
  if (token) {
    const [headerB64 = '', payloadB64 = '', signature = ''] = token.split('.') || [];

    const header = headerB64.replace(/-/g, '+').replace(/_/g, '/');
    const payload = payloadB64.replace(/-/g, '+').replace(/_/g, '/');

    try {
      const jsonHeader = decodeURIComponent(
        atob(header)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join(''),
      );
      const jsonPayload = decodeURIComponent(
        atob(payload)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join(''),
      );
      return { header: JSON.parse(jsonHeader), payload: JSON.parse(jsonPayload), signature };

      // eslint-disable-next-line no-unused-vars
    } catch (error) {
      return null;
    }
  }
  return null;
};
