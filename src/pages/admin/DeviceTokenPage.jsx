import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  <PERSON><PERSON>,
  Card,
  DropDown,
  Field,
  HelpContainer,
  MultiSelection,
  ToggleButton,
  defaultValidationDetail,
  getApiPUTNotificationOptions,
  useApiCall,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import { isEqual, noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import { getSettings, update } from '../../ducks/device-tokens';
import { selectSettings } from '../../ducks/device-tokens/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/tenant-domains';
import { selectDomainNameList, selectTableDetail } from '../../ducks/tenant-domains/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const DeviceToken = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_METHODS));

  const { hasFullAccess, noAccess } = privileges;

  const settings = useSelector(selectSettings);

  const tenantTableDetail = useSelector(selectTableDetail);
  const domainNameList = useSelector(selectDomainNameList);

  const [formValues, setFormValues] = useState({ ...settings });
  const [validationDetail, setValidationDetail] = useState({});

  useEffect(() => {
    apiCall(getSettings()).catch(noop);
  }, []);

  useEffect(() => {
    setFormValues((prevState) => ({ ...prevState, ...settings }));

    setSelectedDomainName(settings.domains.map(({ name, id }) => ({ label: name, value: id })));
  }, [settings]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({ formValues });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const { isDropDownLoading, onDropDownOpen } = useDropDownActions({
    detail: tenantTableDetail,
    apiCallFunc: getList,
    fetchOnce: true,
    defaultOnOpenApiPayload: { domainType: 'registered' },
  });

  const [selectedDomainName, setSelectedDomainName] = useState(
    formValues.domains.map(({ name, id }) => ({ label: name, value: id })),
  );

  const onDomainSelection = (detail) => {
    setSelectedDomainName(detail);

    setFormValues((prevState) => ({
      ...prevState,
      domains: detail.map(({ label, value }) => ({ name: label, id: value })),
    }));
  };

  const onToggleStatusClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      jitEnabled: !formValues?.jitEnabled,
    }));
  };

  const onToggleAllAllowedClick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      allAllowed: !formValues?.allAllowed,
    }));
  };

  const onSaveClick = () => {
    const payload = { ...formValues };

    apiCall(update(payload), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    })
      .then()
      .catch(noop);
  };

  const onCancelCLick = () => {
    setFormValues((prevState) => ({
      ...prevState,
      ...settings,
    }));

    setSelectedDomainName(settings.domains.map(({ name, id }) => ({ label: name, value: id })));
  };

  const renderActionsSection = () => {
    const enableAction = !isEqual(formValues, settings);

    let enable = enableAction;

    if (!formValues.allAllowed && formValues.jitEnabled && formValues.domains.length == 0) {
      enable = false;
    }

    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button onClick={onSaveClick} disabled={!enable}>
          {t('SAVE')}
        </Button>
        <Button
          type="tertiary"
          containerClass="no-p-l"
          onClick={onCancelCLick}
          disabled={!enableAction}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.DEVICE_TOKEN} />
      <article id="device-token" className="page-container">
        <section className="typography-header3 page-title">
          {t('CLIENT_CONNECTOR_DEVICE_TOKEN')}
        </section>

        <Card>
          <div className="is-flex">
            <Field label="ENABLE_TO_ALLOW_ALL_DOMAINS">
              <ToggleButton
                isOn={formValues.allAllowed}
                onToggleClick={onToggleAllAllowedClick}
                showLabel={false}
                disabled={!hasFullAccess}
              />
            </Field>

            {!formValues.allAllowed && (
              <Field
                label="DOMAINS"
                htmlFor="domains"
                containerClass="full-width"
                info={validationDetail}
              >
                <DropDown
                  list={domainNameList}
                  selectedList={selectedDomainName}
                  onSelection={onDomainSelection}
                  onOpen={onDropDownOpen}
                  renderItemsSelection={(props) => (
                    <MultiSelection
                      unselectedTitle="Unselected Domains"
                      selectedTitle="Selected Domains"
                      {...props}
                    />
                  )}
                  hasSearch
                  isMulti
                  loading={isDropDownLoading}
                  disabled={!hasFullAccess}
                />
              </Field>
            )}
          </div>

          <Field label="ENABLE_FOR_JIT_PROVISIONING">
            <ToggleButton
              isOn={formValues.jitEnabled}
              onToggleClick={onToggleStatusClick}
              showLabel={false}
              disabled={!hasFullAccess}
            />
          </Field>
        </Card>

        {renderActionsSection()}
      </article>
    </NavLayout>
  );
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { allAllowed, domains, jitEnabled } = formValues || {};

  if (!allAllowed && jitEnabled && domains.length == 0) {
    validationDetail.isValid = false;
    validationDetail.context = 'domains';
    validationDetail.type = 'error';
    validationDetail.message = 'Domain is Required';

    return validationDetail;
  }

  return validationDetail;
};

export default DeviceToken;
