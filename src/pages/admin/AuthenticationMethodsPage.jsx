import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  Button,
  CRUDModal,
  Card,
  Field,
  FieldGroup,
  HelpContainer,
  // Input,
  ToggleButton,
  defaultValidationDetail,
  getApiPUTNotificationOptions,
  // mergeFormValues,
  useApiCall,
} from '@zscaler/zui-component-library';

import { isEqual, noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import {
  getMultifactorSettings,
  updateMultifactorSettings,
} from '../../ducks/authentication-methods';
import { selectMultifactorSettings } from '../../ducks/authentication-methods/selectors';
import { getTenantFeatures } from '../../ducks/features';
import { selectIsAdminsMFAEnabled, selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const AuthenticationMethodsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_METHODS));

  const { hasFullAccess, noAccess } = privileges;

  const multifactorSettings = useSelector(selectMultifactorSettings);

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);
  const isAdminsMFAEnabled = useSelector(selectIsAdminsMFAEnabled);

  const [modalMode, setModalMode] = useState('');

  useEffect(() => {
    apiCall(getTenantFeatures()).catch(noop);
    apiCall(getMultifactorSettings()).catch(noop);
  }, []);

  const [formValues, setFormValues] = useState(multifactorSettings);
  const [validationDetail, setValidationDetail] = useState({});

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({ formValues });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  // const onFormFieldChange = (evt) => {
  //   setFormValues(mergeFormValues(evt));
  // };

  const reset = () => {
    setFormValues(multifactorSettings);
  };

  useEffect(() => {
    reset();
  }, [multifactorSettings]);

  const onToggleFido = (formName) => {
    if (formValues.fidoPrimary) {
      setModalMode('fidoPrimary');
    }

    setFormValues((prevState) => ({ ...prevState, [formName]: !prevState[formName] }));
  };

  const onToogleMultifactor = (formName) => {
    setFormValues((prevState) => ({ ...prevState, [formName]: !prevState[formName] }));
  };

  const updateMFASettings = () => {
    apiCall(updateMultifactorSettings(formValues), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    }).catch(noop);
  };

  const cancelUpdate = () => {
    reset();
  };

  const onConfirmClick = () => {
    setModalMode('');
  };

  const onCloseClick = () => {
    setModalMode('');
    reset();
  };

  const modalModeDetail = {
    fidoPrimary: {
      headerText: 'DISABLE_FIDO',
      confirmationMessage: 'DISABLE_FIDO_MSG',
    },
    mfaEnabled: {
      headerText: 'DISABLE_MULTIFACTOR',
      confirmationMessage: 'DISABLE_MFA_MSG',
    },
  };

  const getTooltipDetail = (name) => {
    const tooltipDetail = {
      content: '',
    };

    if (name === 'mfaEnabled') {
      tooltipDetail.content = 'MFA_TOGGLE_TOOLTIP';
    }
    if (name === 'fidoPrimary') {
      tooltipDetail.content = 'FIDO_TOGGLE_TOOLTIP';
    }

    return tooltipDetail;
  };

  if (noAccess) {
    return <NoAccess />;
  }

  const showFidoOption = isUserSSOEnabled || isAdminsMFAEnabled || formValues?.adminMfaEnabled;

  const isFidoOptionDisabled = !formValues?.userMfaEnabled && !formValues?.adminMfaEnabled;

  const canUpdate = validationDetail.isValid && !isEqual(formValues, multifactorSettings);

  const renderActionSection = () => {
    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button onClick={updateMFASettings} disabled={!canUpdate}>
          {t('SAVE')}
        </Button>
        <Button type="tertiary" onClick={cancelUpdate} disabled={!canUpdate}>
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.AUTHENTICATION_METHODS} />

      <article id="admin-authentication-methods" className="page-container">
        <section className="typography-header3 page-title">{t('AUTHENTICATION_METHODS')}</section>

        <Card>
          <FieldGroup>
            {isUserSSOEnabled && (
              <Field
                label="ENABLE_MULTIFACTOR_AUTH_USER"
                containerClass="no-m"
                tooltip={!hasFullAccess ? {} : getTooltipDetail('mfaEnabled')}
              >
                <ToggleButton
                  isOn={formValues?.userMfaEnabled}
                  showLabel={false}
                  disabled={!hasFullAccess}
                  onToggleClick={() => {
                    onToogleMultifactor('userMfaEnabled');
                  }}
                />
              </Field>
            )}

            {isAdminsMFAEnabled && (
              <Field
                label="ENABLE_MULTIFACTOR_AUTH_ADMIN"
                containerClass="no-m"
                tooltip={!hasFullAccess ? {} : getTooltipDetail('mfaEnabled')}
              >
                <ToggleButton
                  isOn={formValues?.adminMfaEnabled}
                  showLabel={false}
                  disabled={!hasFullAccess}
                  onToggleClick={() => {
                    onToogleMultifactor('adminMfaEnabled');
                  }}
                />
              </Field>
            )}
          </FieldGroup>
          {!isFidoOptionDisabled && (
            <>
              {/* {showFidoOption && (
                <FieldGroup>
                  <Input
                    label="MFA_ENROLLMENT_GRACE_PERIOD"
                    name="mfaGracePeriod"
                    onChange={onFormFieldChange}
                    value={formValues.mfaGracePeriod}
                    info={validationDetail}
                    maxLength="128"
                    tooltip={
                      !hasFullAccess
                        ? {}
                        : {
                            content: 'Set the MFA enrollement grace period',
                          }
                    }
                    readOnly={!hasFullAccess}
                    disabled={!hasFullAccess || isFidoOptionDisabled}
                    containerStyle={{ maxWidth: '260px' }}
                  />
                </FieldGroup>
              )} */}

              {showFidoOption && (
                <FieldGroup>
                  <Field
                    label="ALLOW_FIDO_AS_PRIMARY"
                    tooltip={!hasFullAccess ? {} : getTooltipDetail('fidoPrimary')}
                  >
                    <ToggleButton
                      isOn={formValues?.fidoPrimary}
                      showLabel={false}
                      disabled={!hasFullAccess || isFidoOptionDisabled}
                      onToggleClick={() => {
                        onToggleFido('fidoPrimary');
                      }}
                    />
                  </Field>
                </FieldGroup>
              )}
            </>
          )}

          {renderActionSection()}
        </Card>
      </article>

      {modalMode && (
        <CRUDModal
          mode={'actionConfirmation'}
          onSaveClick={onConfirmClick}
          onCloseClick={onCloseClick}
          headerText={modalModeDetail[modalMode].headerText}
          confirmationMessage={modalModeDetail[modalMode].confirmationMessage}
          saveText={'CONFIRM'}
        />
      )}
    </NavLayout>
  );
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { mfaGracePeriod } = formValues || {};

  if (!mfaGracePeriod) {
    validationDetail.isValid = false;
    validationDetail.context = 'mfaGracePeriod';
    validationDetail.type = 'error';
    validationDetail.message = 'Grace Period is Required';

    return validationDetail;
  }

  if (mfaGracePeriod < 14 || mfaGracePeriod > 30) {
    validationDetail.isValid = false;
    validationDetail.context = 'mfaGracePeriod';
    validationDetail.type = 'error';
    validationDetail.message = 'Grace period should be between 14(min) to 30(max) days';

    return validationDetail;
  }

  return validationDetail;
};

export default AuthenticationMethodsPage;
