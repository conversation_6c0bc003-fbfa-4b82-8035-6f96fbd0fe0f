import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  DiffViewerModal,
  DownloadFile,
  DropDown,
  HelpContainer,
  RowNumber,
  Search,
  StatusTag,
  TableContainer,
  TextWithTooltip,
  getCalendarDDList,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import Filters from '../../components/audit-logs/Filters';
import { getFilterApiPayload } from '../../components/audit-logs/helper';
import NoAccess from '../../components/no-access/NoAccess';

import { downloadAuditLogs, getList } from '../../ducks/audit-logs';
import { selectTableConfig, selectTableDetail } from '../../ducks/audit-logs/selectors';
import { showErrorNotification } from '../../ducks/global';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const columnConfigIgnoreList = ['number'];

const searchOptions = [
  { label: 'RESOURCE', value: 'RESOURCE' },
  { label: 'ADMIN_ID', value: 'ADMIN_ID' },
  { label: 'CLIENT_IP', value: 'CLIENT_IP' },
];

const getDefaultTimeRange = () => {
  const newList = getCalendarDDList({});

  const defaultTimeRange = newList[0] ? [newList[0]] : [];

  return defaultTimeRange;
};

const defaultSelectedItem = [{ label: 'ALL', value: 'ALL' }];

const defaultSelectedFilter = () => ({
  timeRange: getDefaultTimeRange(),
  category: [defaultSelectedItem[0]],
  actionInterface: [defaultSelectedItem[0]],
  actionResult: [defaultSelectedItem[0]],
});

const AuditLogsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.AUDIT_LOGS_POLICY));

  const { noAccess } = privileges;

  const [selectedFilter, setSelectedFilter] = useState({ ...defaultSelectedFilter() });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSearchField, setSelectedSearchField] = useState(searchOptions[0]);

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'number') {
        columnDetail.cell = RowNumber;
      }

      if (columnDetail.id === 'resourceName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { objectName } = props?.row?.original || {};

          return <TextWithTooltip text={objectName} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'userName') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { userName } = props?.row?.original || {};

          return <TextWithTooltip text={userName} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'dataBeforeAfter') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { dataBefore, dataAfter, ...rest } = props?.row?.original || {};

          if (dataBefore || dataAfter) {
            return <DiffViewerModal oldValue={dataBefore} newValue={dataAfter} {...rest} />;
          }

          return null;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'actionResult') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { actionResult } = props?.row?.original || {};

          return (
            <StatusTag
              value={actionResult === 'SUCCESS'}
              truthyLabel={actionResult}
              falsyLabel={actionResult}
              type="ALLOWED_BLOCKED"
            />
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (
        ['actionType', 'actionInterface', 'category', 'subcategory'].indexOf(columnDetail.id) >= 0
      ) {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          return <TextWithTooltip text={props?.getValue?.()} />;
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const onRefreshClick = () => {
    setSearchTerm('');
    setSelectedSearchField(searchOptions[0]);

    setSelectedFilter({ timeRange: getDefaultTimeRange() });
  };

  const onDownloadLogsClick = async () => {
    const filters = getFilterApiPayload({ selectedFilter });

    return await apiCall(downloadAuditLogs({ filters })).catch(noop);
  };

  const getSearchField = () => {
    const { value } = selectedSearchField || {};

    let searchField = '';

    if (value === 'RESOURCE') {
      searchField = 'objectName';
    }

    if (value === 'ADMIN_ID') {
      searchField = 'userName';
    }

    if (value === 'CLIENT_IP') {
      searchField = 'clientIP';
    }

    return searchField;
  };

  const onSearchEnter = (term) => {
    const filters = getFilterApiPayload({ selectedFilter });

    const searchField = getSearchField();

    const payload = {};

    if (searchField && term) {
      payload.filters = { ...filters, [searchField]: term };
    } else {
      payload.filters = { ...filters };
    }

    apiCall(getList({ ...payload })).catch(noop);

    setSearchTerm(term);
  };

  useEffect(() => {
    const filters = getFilterApiPayload({ selectedFilter });

    const searchField = getSearchField();

    const payload = {};

    if (searchField && searchTerm) {
      payload.filters = { ...filters, [searchField]: searchTerm };
    } else {
      payload.filters = { ...filters };
    }

    if (payload.filters.isDateRangeValid) {
      delete payload.filters.isDateRangeValid;

      apiCall(getList({ ...payload })).catch(noop);
    } else {
      dispatch(showErrorNotification({ message: 'START_TIME_BEFORE_END_TIME' }));
    }
  }, [selectedFilter]);

  const onLoadMoreClick = () => {
    const filters = getFilterApiPayload({ selectedFilter });

    const { pageSize, pageOffset } = tableDetail;

    const searchField = getSearchField();

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    if (searchField && searchTerm) {
      payload.filters = { ...filters, [searchField]: searchTerm };
    } else {
      payload.filters = { ...filters };
    }

    apiCall(getList({ ...payload })).catch(noop);
  };

  const renderFilterSection = () => {
    return (
      <div className="audit-logs-filter-search-container is-flex full-width has-jc-sb has-ai-c">
        <Filters selectedFilter={selectedFilter} setSelectedFilter={setSelectedFilter} />

        <div className="is-flex has-ai-c">
          <Button
            type="tertiary"
            onClick={onRefreshClick}
            containerClass="no-p-l content-width"
            style={{ paddingRight: '8px', marginRight: '8px' }}
          >
            <FontAwesomeIcon icon={faSync} />
          </Button>

          <div className="dropdown-with-search">
            <DropDown
              list={searchOptions}
              selectedList={[selectedSearchField]}
              onSelection={(payload) => {
                setSelectedSearchField(payload[0]);
              }}
              selectedItemsProps={{
                kind: 'tertiary',
                containerStyle: {
                  justifyContent: 'center',
                  paddingLeft: '4px',
                  paddingRight: '4px',
                  minWidth: '70px',
                },
              }}
              itemsSelectionProps={{
                containerStyle: {
                  minWidth: '130px',
                },
              }}
            />

            <Search
              onSearch={onSearchEnter}
              term={searchTerm}
              containerClass="no-m-r"
              containerStyle={{ minWidth: '160px' }}
            />
          </div>
          <DownloadFile
            variantType="iconWithText"
            label=""
            containerStyle={{ marginLeft: '8px' }}
            buttonContainerClass="content-width"
            onDownloadClick={onDownloadLogsClick}
          />
        </div>
      </div>
    );
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.AUDIT_LOGS} />
      <article id="admin-audit-logs" className="page-container">
        <section className="typography-header3 page-title">{t('AUDIT_LOGS')}</section>

        <Card>
          {renderFilterSection()}

          <TableContainer
            {...tableConfig}
            columns={tableColumnConfig}
            data={tableDetail.data}
            containerClass="full-width"
            pagination={{ ...tableDetail, onLoadMoreClick }}
            columnConfigIgnoreList={columnConfigIgnoreList}
          />
        </Card>
      </article>
    </NavLayout>
  );
};

export default AuditLogsPage;
