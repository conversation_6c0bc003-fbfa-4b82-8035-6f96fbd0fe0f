import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';

import { testRender } from '../../test/testRender';
import MyProfilePage from './MyProfilePage';

describe('<MyProfilePage />', () => {
  it('should render', async () => {
    testRender(<MyProfilePage />);

    expect(await screen.findByText('Login ID')).toBeInTheDocument();
  });

  it('should give correct id', async () => {
    testRender(<MyProfilePage />);

    expect(await screen.findByText('<EMAIL>')).toBeInTheDocument();
  });
});
