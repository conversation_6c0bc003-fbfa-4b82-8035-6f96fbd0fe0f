import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import AuthenticationLevelsActions from '../../components/authentication-levels/AuthenticationLevelsActions';
import AuthenticationLevelsCRUDContainer from '../../components/authentication-levels/AuthenticationLevelsCRUDContainer';
import EmptyAuthenticationLevel from '../../components/authentication-levels/EmptyAuthenticationLevel';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/authentication-levels';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import AuthenticationLevelsPageContextProvider from '../../contexts/AuthenticationLevelsPageContextProvider';

const AuthenticationLevelsPage = () => {
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_METHODS));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.MULTIFACTOR_SETTINGS} />

      <article id="authentication-levels" className="page-container">
        <AuthenticationLevelsPageContextProvider privileges={privileges}>
          <AuthenticationLevelsActions />

          <AuthenticationLevelsCRUDContainer />

          <EmptyAuthenticationLevel />
        </AuthenticationLevelsPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default AuthenticationLevelsPage;
