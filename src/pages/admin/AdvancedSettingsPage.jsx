import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import {
  <PERSON><PERSON>,
  Card,
  Field,
  HelpContainer,
  Input,
  ToggleButton,
  getApiPUTNotificationOptions,
  mergeFormValues,
  replaceNonNumericDigit,
  useApiCall,
} from '@zscaler/zui-component-library';

import { cloneDeep, isEqual, noop } from 'lodash-es';

import { getFormValidationDetail } from '../../components/advanced-settings/helper';

import { getAdvanceSettings, updateAdvanceSettings } from '../../ducks/advanced-settings';
import { selectAdvanceSettings } from '../../ducks/advanced-settings/selectors';
import { selectIsUserSSOEnabled } from '../../ducks/features/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const AdvancedSettingsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const isOneUIContext = process.env.IS_ONEUI;

  const {
    sessionIdleTimeout,
    serviceAuthnSessionEnabled,
    serviceSessionTimeout,
    forceAuthnSettings,
  } = useSelector(selectAdvanceSettings);

  const authenticationSessionPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY),
  );

  const isUserSSOEnabled = useSelector(selectIsUserSSOEnabled);

  const { hasFullAccess, hasViewAccess } = authenticationSessionPrivileges;

  const idleTimeoutInMinutes = sessionIdleTimeout ? sessionIdleTimeout / 60 : 0;
  const serviceSessionTimeoutInMinutes = serviceSessionTimeout ? serviceSessionTimeout / 60 : 0;

  useEffect(() => {
    apiCall(getAdvanceSettings()).catch(noop);
  }, []);

  const [formValues, setFormValues] = useState({
    sessionIdleTimeout: idleTimeoutInMinutes,
    serviceAuthnSessionEnabled,
    serviceSessionTimeout: serviceSessionTimeoutInMinutes,
    forceAuthnSettings,
  });

  const [validationDetail, setValidationDetail] = useState({});

  useEffect(() => {
    setFormValues((prevState) => ({
      ...prevState,
      sessionIdleTimeout: idleTimeoutInMinutes,
      serviceAuthnSessionEnabled,
      serviceSessionTimeout: serviceSessionTimeoutInMinutes,
      forceAuthnSettings,
    }));
  }, [
    sessionIdleTimeout,
    serviceAuthnSessionEnabled,
    serviceSessionTimeoutInMinutes,
    forceAuthnSettings,
  ]);

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
      isUserSSOEnabled,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onFormFieldChange = (evt) => {
    const { name, value } = evt?.target || {};

    if (name == 'sessionIdleTimeout' || name == 'serviceSessionTimeout') {
      const inputNum = replaceNonNumericDigit({ value, allowDecimal: false });

      if (Number(inputNum) !== Number(formValues[name])) {
        setFormValues((prevState) => ({
          ...prevState,
          [name]: inputNum,
        }));
      }
    } else {
      setFormValues(mergeFormValues(evt));
    }
  };

  const onToggleClick = (formName) => {
    setFormValues((prevState) => ({
      ...prevState,
      [formName]: !prevState[formName],
    }));
  };

  const update = () => {
    apiCall(
      updateAdvanceSettings({
        ...formValues,
        sessionIdleTimeout: formValues.sessionIdleTimeout * 60,
        serviceSessionTimeout: formValues.serviceSessionTimeout * 60,
      }),
      {
        successNotificationPayload: {
          ...getApiPUTNotificationOptions(),
          ...(isOneUIContext ? { message: 'CONFIG_TAKES_EFFECT_FROM_NEXT_LOGIN' } : {}),
        },
      },
    ).catch(noop);
  };

  const cancelUpdate = () => {
    setFormValues((prevState) => ({
      ...prevState,
      sessionIdleTimeout: idleTimeoutInMinutes,
      serviceAuthnSessionEnabled,
      serviceSessionTimeout: serviceSessionTimeoutInMinutes,
      forceAuthnSettings,
    }));
  };

  const canUpdate =
    validationDetail.isValid &&
    !isEqual(
      {
        sessionIdleTimeout: idleTimeoutInMinutes,
        serviceAuthnSessionEnabled,
        serviceSessionTimeout: serviceSessionTimeoutInMinutes,
        forceAuthnSettings,
      },
      formValues,
    );

  const renderActionsSection = () => {
    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button onClick={update} disabled={!canUpdate}>
          {t('SAVE')}
        </Button>
        <Button type="tertiary" onClick={cancelUpdate} disabled={!canUpdate}>
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  const renderForceAuthnSettingsSection = () => {
    return formValues.forceAuthnSettings.map((setting, idx) => {
      const { forceAuthnRequired, name, id } = setting;

      const onToggleClick = () => {
        setFormValues((prevState) => {
          const forceAuthnSettings = cloneDeep(prevState.forceAuthnSettings);

          forceAuthnSettings[idx].forceAuthnRequired = !forceAuthnSettings[idx].forceAuthnRequired;

          return cloneDeep({ ...prevState, forceAuthnSettings });
        });
      };

      return (
        <Field key={id} label={name} containerClass="field-stacked has-jc-sb">
          <ToggleButton
            isOn={forceAuthnRequired}
            showLabel={false}
            onToggleClick={onToggleClick}
            disabled={!hasFullAccess}
          />
        </Field>
      );
    });
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.ADVANCED_SETTINGS} />
      <article id="admin-advanced-settings" className="page-container">
        {hasFullAccess || hasViewAccess ? (
          <>
            <section className="typography-header3 page-title">
              {t(isOneUIContext ? 'SETTINGS' : 'AUTHENTICATION_SESSION')}
            </section>
            <Card>
              <div>
                <Input
                  label="ADMINISTRATOR_INACTIVITY_TIMEOUT_DURATION_IN_MIN"
                  name="sessionIdleTimeout"
                  value={formValues.sessionIdleTimeout}
                  info={validationDetail}
                  onChange={onFormFieldChange}
                  containerStyle={{ maxWidth: '380px' }}
                  tooltip={
                    !hasFullAccess
                      ? {}
                      : {
                          content:
                            'Enter the timeout period after which the user is logged out from the ZIdentity session',
                        }
                  }
                  disabled={!hasFullAccess}
                />

                {isUserSSOEnabled && (
                  <Field
                    label="AUTHENTICATION_SESSION_FOR_SERVICE_ENTITLEMENT"
                    tooltip={
                      !hasFullAccess
                        ? {}
                        : {
                            content: `Enable to set ZIdentity's authenticated session to Zscaler services for enrolling users. Disable if you want the ZIdentity service to send users to the IdP for authentication`,
                          }
                    }
                  >
                    <ToggleButton
                      isOn={formValues?.serviceAuthnSessionEnabled}
                      onToggleClick={() => {
                        onToggleClick('serviceAuthnSessionEnabled');
                      }}
                      showLabel={false}
                      disabled={!hasFullAccess}
                    />
                  </Field>
                )}

                {formValues?.serviceAuthnSessionEnabled && (
                  <Input
                    label="SERVICE_ENTITLEMENT_SESSION_TIMEOUT_DURATION_IN_MIN"
                    name="serviceSessionTimeout"
                    info={validationDetail}
                    value={formValues.serviceSessionTimeout}
                    onChange={onFormFieldChange}
                    containerStyle={{ maxWidth: '380px' }}
                    tooltip={
                      !hasFullAccess
                        ? {}
                        : {
                            content:
                              'Enter the timeout period after which the user is logged out from the service session',
                          }
                    }
                    disabled={!hasFullAccess}
                  />
                )}
              </div>

              {formValues.forceAuthnSettings?.length > 0 && isUserSSOEnabled && (
                <div>
                  <Field
                    label="FORCE_AUTHENTICATION_FOR_PRIVATE_ACCESS_REAUTHENTICATION"
                    tooltip={
                      !hasFullAccess
                        ? {}
                        : {
                            content: `Enable this option for your configured IdPs or hosted users for which you want to force authentication when the ZPA service requests reauthentication. When disabled, the ZIdentity service allows the IdP's settings to determine whether or not to prompt the user for authentication`,
                          }
                    }
                  />

                  {renderForceAuthnSettingsSection()}
                </div>
              )}
            </Card>
          </>
        ) : null}
        {renderActionsSection()}
      </article>
    </NavLayout>
  );
};

export default AdvancedSettingsPage;
