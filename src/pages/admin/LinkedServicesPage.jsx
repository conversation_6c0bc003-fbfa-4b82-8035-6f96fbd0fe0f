import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  <PERSON>ton,
  Card,
  HelpContainer,
  TableContainer,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';

import { STATUS_ENUMS } from '../../ducks/migration/constants';
import { selectStatus } from '../../ducks/migration/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/services';
import { selectLinkedTenantsTableConfig, selectTableDetail } from '../../ducks/services/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const LinkedTenantsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const tableConfig = useSelector(selectLinkedTenantsTableConfig);
  const tableDetail = useSelector(selectTableDetail);

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.LINKED_TENATS_POLICY));

  const { noAccess } = privileges;

  const status = useSelector(selectStatus);

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'inMigration') {
        const StausCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { inMigration } = props?.row?.original || {};

          if (inMigration) {
            const { currentStatus } = status || {};

            if (currentStatus === STATUS_ENUMS.PROVISIONED) {
              return 'Admins Provisioned';
            }

            if (currentStatus === STATUS_ENUMS.ADMIN_LINKED) {
              return 'Admins Linked';
            }
          }

          return '';
        };

        columnDetail.cell = StausCellComponent;
      }

      if (columnDetail.id === 'status') {
        const StausCellComponent = (props) =>
          // eslint-disable-next-line react/prop-types
          t(props?.row?.original?.linkedState ? 'LINKED' : 'UNLINKED');

        columnDetail.cell = StausCellComponent;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns, status]);

  useEffect(() => {
    apiCall(
      getList({ linkedServicesOnly: false, includeDefaultServices: false, includeDepSvces: false }),
    ).catch(noop);
  }, []);

  const onRefreshClick = () => {
    apiCall(
      getList({ linkedServicesOnly: false, includeDefaultServices: false, includeDepSvces: false }),
    ).catch(noop);
  };

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    apiCall(
      getList({
        requireTotal: false,
        pageOffset: pageOffset + pageSize,
        pageSize,
        linkedServicesOnly: false,
        includeDefaultServices: false,
        includeDepSvces: false,
      }),
    ).catch(noop);
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.LINKED_SERVICES} />

      <article id="linked-tenants" className="page-container">
        <section className="is-flex has-jc-sb">
          <div className="typography-header3 page-title">{t('LINKED_SERVICES')}</div>
        </section>

        <Card>
          <div className="is-flex has-jc-e full-width">
            <Button
              type="tertiary"
              onClick={onRefreshClick}
              style={{ minWidth: '0px', paddingRight: '0px' }}
            >
              <FontAwesomeIcon icon={faSync} className="icon left" />
              <span>{t('SYNC')}</span>
            </Button>
          </div>

          <TableContainer
            {...tableConfig}
            columns={tableColumnConfig}
            data={tableDetail.data}
            pagination={{ ...tableDetail, onLoadMoreClick }}
          />
        </Card>
      </article>
    </NavLayout>
  );
};

export default LinkedTenantsPage;
