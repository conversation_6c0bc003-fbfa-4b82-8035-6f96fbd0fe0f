import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  Field,
  HelpContainer,
  ListBuilder,
  ToggleButton,
  getApiPUTNotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import { getTooltipDetail } from '../../components/tenant-domains/helper';
import { domainNameValidationRegex } from '../../components/user/helper';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { syncDomain } from '../../ducks/services';
import { getList, updateGuestDomains } from '../../ducks/tenant-domains';
import { selectDomainsByType } from '../../ducks/tenant-domains/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const DomainsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const domains = useSelector(selectDomainsByType);

  const [guestDomainsList, setGuestDomainsList] = useState([]);
  const [includeArbitraryDomains, setIncludeArbitraryDomains] = useState(false);
  const [saveButtonDisabled, setSaveButtonDisabled] = useState(true);
  const [tempArbitraryDomainsValue, setTempArbitraryDomainsValue] = useState(null);

  useEffect(() => {
    setGuestDomainsList(domains?.guestDomains);
    setIncludeArbitraryDomains(
      tempArbitraryDomainsValue !== null
        ? tempArbitraryDomainsValue
        : domains?.arbitraryDomains?.length > 0,
    );
  }, [domains]);
  const authenticationSessionPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_SESSION_POLICY),
  );

  const guestDomainsPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.GUEST_DOMAIN_POLICY),
  );

  const { noAccess: authenticationSessionNoAccess } = authenticationSessionPrivileges;
  const { hasFullAccess: guestDomainsFullAccess, noAccess: guestDomainsNoAccess } =
    guestDomainsPrivileges;

  useEffect(() => {
    apiCall(getList({ requireTotal: true, all: true })).catch(noop);
  }, []);

  const onRefreshClick = () => {
    apiCall(syncDomain())
      .catch(noop)
      .finally(apiCall(getList({ requireTotal: true, all: true })).catch(noop));
  };

  const update = () => {
    const payload = {
      guestDomains: guestDomainsList,
      includeArbitraryDomain: includeArbitraryDomains,
    };
    apiCall(updateGuestDomains(payload), {
      successNotificationPayload: { ...getApiPUTNotificationOptions() },
    })
      .then(() => {
        setSaveButtonDisabled(true);
        setTempArbitraryDomainsValue(null);
      })
      .catch(() => {
        setSaveButtonDisabled(false);
      });
  };

  const cancelUpdate = () => {
    setSaveButtonDisabled(true);
    setTempArbitraryDomainsValue(null);
    apiCall(getList({ requireTotal: true, all: true })).catch(noop);
  };

  const onArbitraryDomainToggle = () => {
    if (saveButtonDisabled) {
      setSaveButtonDisabled(false);
    }
    setTempArbitraryDomainsValue(!includeArbitraryDomains);
    setIncludeArbitraryDomains(!includeArbitraryDomains);
  };

  const domainValidator = ({ item }) => {
    if (!item || !domainNameValidationRegex.test(item)) {
      return false;
    }
    return true;
  };

  const getValidationMessage = ({ invalidItems }) => {
    return `Domain name must be valid and must not exceed 63 characters - ${invalidItems?.join?.(', ')}`;
  };

  const renderDomainsSection = () => {
    return (
      <div className="domains-container">
        {domains?.primaryDomains?.map?.(({ id, name }) => (
          <span key={id} className="domain">
            {name} &nbsp;
          </span>
        ))}
      </div>
    );
  };

  const renderGuestDomainsSection = () => {
    return (
      <Field
        label="GUEST_DOMAINS"
        containerClass="guest-domains-field full-width"
        tooltip={!guestDomainsFullAccess ? {} : getTooltipDetail('guestDomains')}
      >
        <ListBuilder
          buttonText={'ADD'}
          list={guestDomainsList}
          setList={(data) => {
            if (saveButtonDisabled) {
              setSaveButtonDisabled(false);
            }
            setGuestDomainsList(data);
          }}
          separator={/[\r\n,\s]+/}
          getValidationMessage={getValidationMessage}
          validator={domainValidator}
          disabled={!guestDomainsFullAccess}
        />
      </Field>
    );
  };

  const renderArbitraryDomainsSection = () => {
    return (
      <Field containerClass="arbitrary-domain-field" label={'ARBITRARY_GUEST_DOMAINS'}>
        <ToggleButton
          disabled={!guestDomainsFullAccess}
          type="success"
          showLabel={false}
          isOn={includeArbitraryDomains}
          onToggleClick={onArbitraryDomainToggle}
        />
      </Field>
    );
  };

  const renderActionsSection = () => {
    if (guestDomainsFullAccess) {
      return (
        <div className="action-section buttons">
          <Button disabled={saveButtonDisabled} onClick={update}>
            {t('SAVE')}
          </Button>
          <Button disabled={saveButtonDisabled} type="tertiary" onClick={cancelUpdate}>
            {t('CANCEL')}
          </Button>
        </div>
      );
    }

    return null;
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.DOMAINS} />
      {(!authenticationSessionNoAccess || !guestDomainsNoAccess) && (
        <section className="typography-header3 page-title">{t('DOMAINS')}</section>
      )}

      <article id="admin-domains" className="page-container">
        {!authenticationSessionNoAccess && (
          <>
            <div className="is-flex has-jc-s has-ai-c">
              <section className="typography-paragraph1-uppercase">{t('DOMAINS')}</section> &nbsp;
              <Button
                type="tertiary"
                onClick={onRefreshClick}
                containerClass="content-width no-p-l"
                containerStyle={{ marginTop: '8px', marginLeft: '4px' }}
              >
                <FontAwesomeIcon icon={faSync} className="icon left" />
              </Button>
            </div>

            <Card>{renderDomainsSection()}</Card>
          </>
        )}

        {!guestDomainsNoAccess && (
          <>
            <div className="is-flex has-jc-s has-ai-c">
              <section className="typography-paragraph1-uppercase">{t('GUEST_DOMAINS')}</section>{' '}
              &nbsp;
            </div>

            <Card containerClass="is-flex has-jc-sb">
              {renderGuestDomainsSection()}
              {renderArbitraryDomainsSection()}
            </Card>
            {renderActionsSection()}
          </>
        )}
      </article>
    </NavLayout>
  );
};

export default DomainsPage;
