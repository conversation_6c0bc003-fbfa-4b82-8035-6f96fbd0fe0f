import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faPencilAlt } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  Card,
  DropDown,
  Field,
  HelpContainer,
  TextWithTooltip,
  useDropDownActions,
} from '@zscaler/zui-component-library';

import ChangeEmailModal from '../../components/profile/ChangeEmailModal';
import ChangePasswordModal from '../../components/profile/ChangePasswordModal';

import { getLanguages, updateProfile } from '../../ducks/profile';
import { DEFAULT_LANGUAGES } from '../../ducks/profile/constants';
import { selectLanguagesList, selectMyProfileDetail } from '../../ducks/profile/selectors';

import NavLayout from '../../layout/NavLayout';

import { DEFAULT_LOCALE, LOCALES_MAPPING } from '../../utils/i18n/locales';

import { HELP_ARTICLES } from '../../config';

const MyProfilePage = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  const profileDetail = useSelector(selectMyProfileDetail);
  const languageList = useSelector(selectLanguagesList);

  const [showChangeEmailModal, setShowChangeEmailModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  const [selectedLanguage, setSelectedLanguage] = useState([]);

  const { isDropDownLoading, onDropDownOpen } = useDropDownActions({
    fetchOnce: true,
    apiCallFunc: getLanguages,
  });

  useEffect(() => {
    setSelectedLanguage([{ label: profileDetail.language, value: profileDetail.language }]);

    const selectedLocale = LOCALES_MAPPING[profileDetail.language] || DEFAULT_LOCALE;

    localStorage?.setItem?.('locale', selectedLocale);

    i18n.changeLanguage(selectedLocale);
  }, [profileDetail]);

  const onChangeEmailClick = () => {
    setShowChangeEmailModal(true);
  };

  const onChangePasswordClick = () => {
    setShowChangePasswordModal(true);
  };

  const onLanguageSelection = (payload) => {
    setSelectedLanguage(payload);

    const language = payload?.[0]?.value || DEFAULT_LANGUAGES[0];

    dispatch(updateProfile({ ...profileDetail, language }));
  };

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USER_PROFILE} />
      <article id="admin-my-profile" className="page-container">
        <section className="typography-header3 page-title">{t('MY_PROFILE')}</section>
        <Card>
          <Field label="LOGIN_ID_LABEL">
            <TextWithTooltip containerStyle={{ marginTop: '1rem' }}>
              {profileDetail.loginName}
            </TextWithTooltip>
          </Field>

          <Field label="EMAIL_ADDRESS">
            <div className="is-flex has-ai-c" style={{ maxWidth: '100%' }}>
              <TextWithTooltip containerClass={'email-address-container'}>
                {profileDetail.primaryEmail}
              </TextWithTooltip>
              <Button
                type="tertiary"
                containerClass="no-p-l content-width"
                onClick={onChangeEmailClick}
              >
                <FontAwesomeIcon icon={faPencilAlt} className="icon right" />
              </Button>
            </div>
          </Field>

          <Field label="LANGUAGE_LABEL" containerStyle={{ marginRight: '0', display: 'none' }}>
            <DropDown
              list={languageList}
              selectedList={selectedLanguage}
              onSelection={onLanguageSelection}
              selectedItemsContainerStyle={{
                top: '38px',
                width: '250px',
                boxShadow: 'none',
              }}
              onOpen={onDropDownOpen}
              loading={isDropDownLoading}
            />
          </Field>

          <Field label="PASSWORD_LABEL">
            <Button type="secondary" onClick={onChangePasswordClick}>
              {t('CHANGE_PASSWORD')}
            </Button>
          </Field>
        </Card>

        {showChangeEmailModal && (
          <ChangeEmailModal
            show={showChangeEmailModal}
            detail={profileDetail}
            onCloseClick={() => {
              setShowChangeEmailModal(false);
            }}
          />
        )}

        {showChangePasswordModal && (
          <ChangePasswordModal
            show={showChangePasswordModal}
            onCloseClick={() => {
              setShowChangePasswordModal(false);
            }}
          />
        )}
      </article>
    </NavLayout>
  );
};

export default MyProfilePage;
