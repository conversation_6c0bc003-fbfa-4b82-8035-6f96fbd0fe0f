import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import IpLocationsAction from '../../components/ip-locations/IpLocationsActions';
import IpLocationsCRUD from '../../components/ip-locations/IpLocationsCRUD';
import IpLocationsTable from '../../components/ip-locations/IpLocationsTable';
import NoAccess from '../../components/no-access/NoAccess';

import { getList } from '../../ducks/ip-locations';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const IpLocationsPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.IP_LOCATION_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.IP_LOCATIONS} />

      <article id="admin-ip-locations" className="page-container">
        <section className="typography-header3 page-title">{t('IP_LOCATIONS')}</section>

        <CRUDPageContextProvider privileges={privileges}>
          <Card>
            <IpLocationsAction />
            <IpLocationsTable />
          </Card>

          <IpLocationsCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default IpLocationsPage;
