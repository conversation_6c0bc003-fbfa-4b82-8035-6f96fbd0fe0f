import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faSync } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Button,
  CRUDModal,
  Card,
  DropDown,
  HelpContainer,
  Search,
  StatusTag,
  TableContainer,
  TextWithTooltip,
  getApiDELETENotificationOptions,
  useApiCall,
} from '@zscaler/zui-component-library';

import { isEmpty, noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import Filters from '../../components/tokens/Filters';
import {
  getDefaultTimeRange,
  getFilterApiPayload,
  getIconClassAndLabel,
  modalModeDetail,
} from '../../components/tokens/helper';

import { showErrorNotification } from '../../ducks/global';
import { selectPermissions<PERSON>y<PERSON>ey } from '../../ducks/permissions/selectors';
import { getList, revoke } from '../../ducks/tokens';
import { selectTableConfig, selectTableDetail } from '../../ducks/tokens/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const defaultSelectedItem = [{ label: 'ALL', value: 'ALL' }];

const defaultSelectedFilter = () => ({
  expiresAt: getDefaultTimeRange('EXPIRY'),
  issuedAt: getDefaultTimeRange('TOKEN'),
  status: [defaultSelectedItem[0]],
});

const searchOptions = [
  { label: 'CLIENT_ID', value: 'CLIENT_ID' },
  { label: 'JTI', value: 'JTI' },
];

const TokensPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const queryParams = new URLSearchParams(window.location.search);
  const clientIdQueryParam = queryParams.get('clientId');

  const dispatch = useDispatch();

  const [detail, setDetail] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSearchField, setSelectedSearchField] = useState(searchOptions[0]);
  const [modalMode, setModalMode] = useState('');

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);
  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES));

  const { hasFullAccess, noAccess } = privileges;

  const [selectedFilter, setSelectedFilter] = useState({});

  useEffect(() => {
    if (clientIdQueryParam) {
      setSelectedSearchField(searchOptions[0]);
      setSearchTerm(clientIdQueryParam);
      window.history.replaceState(null, '', window.location.pathname);
    }
    setSelectedFilter({ ...defaultSelectedFilter() });
  }, [clientIdQueryParam]);

  const onRefreshClick = () => {
    setSearchTerm('');
    setSelectedSearchField(searchOptions[0]);
    setSelectedFilter({ ...defaultSelectedFilter() });
  };

  const getSearchField = () => {
    const { value } = selectedSearchField || {};

    let searchField = '';

    if (value === 'CLIENT_ID') {
      searchField = 'clientId';
    }

    if (value === 'JTI') {
      searchField = 'id';
    }

    return searchField;
  };

  const onSearchEnter = (term) => {
    const filters = getFilterApiPayload({ selectedFilter });

    const searchField = getSearchField();

    const payload = { ...filters };

    if (term) {
      payload[searchField] = term;
    }

    delete payload?.isExpiredDateRangeValid;
    delete payload?.isIssuedDateRangeValid;

    apiCall(getList({ ...payload })).catch(noop);

    setSearchTerm(term);
  };

  useEffect(() => {
    if (!isEmpty(selectedFilter)) {
      const filters = getFilterApiPayload({ selectedFilter });

      const searchField = getSearchField();

      const payload = { ...filters };

      if (searchTerm) {
        payload[searchField] = searchTerm;
      }

      if (payload.isIssuedDateRangeValid && payload.isExpiredDateRangeValid) {
        delete payload.isIssuedDateRangeValid;
        delete payload.isExpiredDateRangeValid;

        apiCall(getList({ ...payload }))
          .catch(noop)
          .then(() => {
            window.history.replaceState({}, '');
          });
      } else {
        dispatch(showErrorNotification({ message: 'START_TIME_BEFORE_END_TIME' }));
      }
    }
  }, [selectedFilter]);

  const onLoadMoreClick = () => {
    const filters = getFilterApiPayload({ selectedFilter });

    const { pageSize, pageOffset } = tableDetail;

    const searchField = getSearchField();

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
      ...filters,
    };

    if (searchTerm) {
      payload[searchField] = searchTerm;
    }

    delete payload?.isExpiredDateRangeValid;
    delete payload?.isIssuedDateRangeValid;

    apiCall(getList({ ...payload })).catch(noop);
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'revoked') {
        const StatusCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const row = props?.row?.original || {};
          const { truthyIcon, truthyIconClass, truthyLabel } = getIconClassAndLabel(row);
          return (
            <>
              <StatusTag
                value={true}
                truthyIcon={truthyIcon}
                truthyIconClass={truthyIconClass}
                truthyLabel={truthyLabel}
                type="ENABLED_DISABLED"
              />
              {truthyLabel === 'ACTIVE' && hasFullAccess && (
                <Button
                  type="tertiary"
                  onClick={() => {
                    revokeToken(row);
                  }}
                >
                  {t('REVOKE')}
                </Button>
              )}
            </>
          );
        };

        columnDetail.cell = StatusCellComponent;
      }

      if (columnDetail.id === 'jti') {
        const TextWithTooltipComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { id } = props?.row?.original || {};

          return (
            <>
              <TextWithTooltip containerClass="login-name-container">{id || '--'}</TextWithTooltip>
            </>
          );
        };

        columnDetail.cell = TextWithTooltipComponent;
      }

      if (columnDetail.id === 'jwt') {
        const jwtCellComponent = (props) => {
          // eslint-disable-next-line react/prop-types
          const { decodedJwt } = props?.row?.original || {};

          return (
            <>
              <Button
                disabled={!decodedJwt}
                type="tertiary"
                containerClass="no-p-l"
                onClick={() => {
                  viewJwt(decodedJwt);
                }}
              >
                {t('VIEW_JWT')}
              </Button>
            </>
          );
        };

        columnDetail.cell = jwtCellComponent;
      }
      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [tableConfig?.columns]);

  const viewJwt = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('view');
    }
  };
  const revokeToken = (detail) => {
    if (detail) {
      setDetail(detail);
      setModalMode('delete');
    }
  };

  const onCloseClick = () => {
    setModalMode('');
    setDetail({});
  };

  const onSaveClick = () => {
    if (modalMode === 'delete') {
      apiCall(revoke(detail), {
        successNotificationPayload: {
          ...getApiDELETENotificationOptions(),
          message: 'Token revoked successfully',
        },
      })
        .then(onCloseClick)
        .catch(noop);
    }
  };

  const renderFiltersSection = () => {
    return (
      <div className="is-flex full-width has-jc-sb has-ai-c">
        <Filters selectedFilter={selectedFilter} setSelectedFilter={setSelectedFilter} />

        <div className="is-flex full-width has-jc-e">
          <div className="buttons">
            <Button
              type="tertiary"
              onClick={onRefreshClick}
              style={{ minWidth: '0px', paddingRight: '0px' }}
            >
              <FontAwesomeIcon icon={faSync} />
            </Button>

            <div className="dropdown-with-search">
              <DropDown
                list={searchOptions}
                selectedList={[selectedSearchField]}
                onSelection={(payload) => {
                  setSelectedSearchField(payload[0]);
                }}
                selectedItemsProps={{
                  kind: 'tertiary',
                  containerStyle: {
                    justifyContent: 'center',
                    paddingLeft: '4px',
                    paddingRight: '4px',
                    minWidth: '70px',
                  },
                }}
                itemsSelectionProps={{
                  containerStyle: {
                    minWidth: '130px',
                  },
                }}
              />

              <Search
                onSearch={onSearchEnter}
                term={searchTerm}
                containerClass="no-m-r"
                containerStyle={{ minWidth: '160px' }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.TOKENS} />

      <article id="admin-tokens" className="page-container">
        <section className="typography-header3 page-title">{t('ZIDENTITY_ISSUED_TOKENS')}</section>

        <>
          <Card>
            {renderFiltersSection()}
            <TableContainer
              {...tableConfig}
              columns={tableColumnConfig}
              data={tableDetail.data}
              pagination={{ ...tableDetail, onLoadMoreClick }}
            />
          </Card>

          <CRUDModal
            mode={modalMode}
            renderFormSection={() => (
              <Card>
                {<pre>{JSON.stringify(detail?.header || {}, null, 2)}</pre>}
                {
                  <pre style={{ marginTop: '10px' }}>
                    {JSON.stringify(detail?.payload || {}, null, 2)}
                  </pre>
                }
              </Card>
            )}
            showSave={modalMode === 'delete'}
            cancelText={'CLOSE'}
            onCloseClick={onCloseClick}
            onSaveClick={onSaveClick}
            saveText={'REVOKE'}
            headerText={detail?.clientId}
            {...modalModeDetail[modalMode]}
          />
        </>
      </article>
    </NavLayout>
  );
};

export default TokensPage;
