import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Card, HelpContainer, TableContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import UsersAndGroupsTabs from '../../components/admin-entitlements/UsersAndGroupsTabs';
import NoAccess from '../../components/no-access/NoAccess';

import { getList, resetTableData } from '../../ducks/admin-entitlements';
import { selectTableConfig, selectTableDetail } from '../../ducks/admin-entitlements/selectors';
import { STATUS_ENUMS } from '../../ducks/migration/constants';
import { selectStatus } from '../../ducks/migration/selectors';
import { getAdminEntitlementServicePermissions } from '../../ducks/permissions';
import {
  selectAdminEntitlementServicePermissions,
  selectPermissionsByKey,
} from '../../ducks/permissions/selectors';
import { selectMyProfileDetail } from '../../ducks/profile/selectors';

import NavLayout from '../../layout/NavLayout';

import { getAppIcon } from '../../utils/generic';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const AdminEntitlementsPage = () => {
  const { apiCall } = useApiCall();
  const dispatch = useDispatch();

  const tableConfig = useSelector(selectTableConfig);
  const tableDetail = useSelector(selectTableDetail);
  const userDetails = useSelector(selectMyProfileDetail);

  const adminServicePermission = useSelector(selectAdminEntitlementServicePermissions);

  const status = useSelector(selectStatus);
  const { currentStatus } = status || {};

  const isAdminProvisioned = currentStatus === STATUS_ENUMS.PROVISIONED;

  const privileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.ADMINISTRATIVE_ENTITLEMENTS_POLICY),
  );

  const userAndGroupPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY),
  );

  const { noAccess, hasRestrictedFullAccess } = privileges;

  const [showUsersAndGroupsTabs, setShowUsersAndGroupsTabs] = useState(false);
  const [usersAndGroupsTabsServiceDetail, setUsersAndGroupsTabsServiceDetail] = useState({});
  const [servicePermissions, setServicePermissions] = useState({});

  const { t } = useTranslation();

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  useEffect(() => {
    if (userDetails?.id) {
      apiCall(getAdminEntitlementServicePermissions(userDetails.id)).catch(noop);
    }
  }, [hasRestrictedFullAccess, userDetails?.id]);

  const hideUsersAndGroupsTabs = () => {
    dispatch(resetTableData());
    setShowUsersAndGroupsTabs(false);
  };

  const tableColumnConfig = useMemo(() => {
    tableConfig?.columns?.map((columnDetail) => {
      if (columnDetail.id === 'serviceName') {
        const ServiceName = (props) => {
          const {
            // eslint-disable-next-line react/prop-types
            row: {
              // eslint-disable-next-line react/prop-types
              original: { serviceDescription, serviceName },
            },
          } = props;
          // eslint-disable-next-line react/prop-types
          const serviceDetail = props.row.original;

          const onServiceClick = (detail) => {
            const permissions = adminServicePermission[detail.id] || {};
            if (!userAndGroupPrivileges.noAccess && !permissions.noAccess) {
              setUsersAndGroupsTabsServiceDetail(detail);
              setServicePermissions(permissions);
              setShowUsersAndGroupsTabs(true);
            }
          };

          return (
            <>
              <div
                className="is-flex has-ai-c pointer"
                onClick={() => {
                  onServiceClick(serviceDetail);
                }}
                onKeyDown={noop}
              >
                <span
                  style={{
                    width: /TRUST/.test(serviceName) ? '39px' : '32px',
                    height: /TRUST/.test(serviceName) ? '39px' : '32px',
                    margin: '0 1em',
                    marginLeft: '2.5em',
                  }}
                >
                  <img
                    src={getAppIcon(serviceName)}
                    style={{ height: '100%', width: '100%', objectFit: 'contain' }}
                    alt="service name"
                  />
                </span>
                <span> {serviceDescription}</span>
              </div>
            </>
          );
        };

        columnDetail.cell = ServiceName;
      }

      return columnDetail;
    });

    return [...(tableConfig?.columns || [])];
  }, [
    tableConfig?.columns,
    userAndGroupPrivileges?.noAccess,
    Object.keys(adminServicePermission).length,
  ]);

  if (noAccess) {
    return <NoAccess />;
  }

  const onLoadMoreClick = () => {
    const { pageSize, pageOffset } = tableDetail;

    const payload = {
      requireTotal: false,
      pageOffset: pageOffset + pageSize,
      pageSize,
    };

    apiCall(getList({ ...payload })).catch(noop);
  };

  return (
    <NavLayout>
      {!showUsersAndGroupsTabs && <HelpContainer src={HELP_ARTICLES.ADMINISTRATIVE_ENTITLEMENTS} />}

      <article
        id="entitlements"
        className="page-container"
        style={isAdminProvisioned ? { marginTop: '6px' } : {}}
      >
        {!showUsersAndGroupsTabs && (
          <>
            <section className="typography-header3 page-title">
              {t('ADMINISTRATIVE_ENTITLEMENTS')}
            </section>
            {isAdminProvisioned && (
              <div className="admin-entitlements-info-container is-flex">
                <span className="has-color-primary has-as-fs">
                  <FontAwesomeIcon icon={faInfoCircle} />
                </span>
                ZIdentity assigned administrative entitlements will override the underlying linked
                service (e.g. Internet Access, Private Access) admin entitlements.
              </div>
            )}
          </>
        )}

        {showUsersAndGroupsTabs ? (
          <UsersAndGroupsTabs
            serviceDetail={usersAndGroupsTabsServiceDetail}
            onBackClick={hideUsersAndGroupsTabs}
            privileges={userAndGroupPrivileges}
            servicePermissions={servicePermissions}
          />
        ) : (
          <Card>
            <TableContainer
              {...tableConfig}
              columns={tableColumnConfig}
              data={tableDetail.data}
              pagination={{ ...tableDetail, onLoadMoreClick }}
              containerClass="full-width"
            />
          </Card>
        )}
      </article>
    </NavLayout>
  );
};

export default AdminEntitlementsPage;
