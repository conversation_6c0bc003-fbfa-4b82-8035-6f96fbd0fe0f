import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import UserAction from '../../components/user/UserAction';
import UserCRUD from '../../components/user/UserCRUD';
import UserTable from '../../components/user/UserTable';
import { searchOptions } from '../../components/user/helper';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/users';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const UsersPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.USERS_AND_GROUPS_POLICY));

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.USERS} />

      <article id="admin-users" className="page-container">
        <section className="typography-header3 page-title">{t('USERS')}</section>

        <CRUDPageContextProvider defaultSearchOption={searchOptions[0]} privileges={privileges}>
          <Card>
            <UserAction />

            <UserTable />
          </Card>

          <UserCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default UsersPage;
