import { useTranslation } from 'react-i18next';

import { HelpContainer } from '@zscaler/zui-component-library';

import DashboardChartsContainer from '../../components/dashboard/DashboardChartsContainer';
import InfoCardContainer from '../../components/dashboard/InfoCardContainer';
import DashboardContextProvider from '../../components/dashboard/context/DashboardContextProvider';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES } from '../../config';

// Note: Very poor api contract by the API team, not willing to change,
// don't blame me :(
const DashboardPage = () => {
  const { t } = useTranslation();

  return (
    <NavLayout>
      <DashboardContextProvider>
        <HelpContainer src={HELP_ARTICLES.DASHBOARD} />

        <article id="dashboard" className="page-container">
          <section className="is-flex has-jc-sb">
            <div className="typography-header3 page-title">{t('DASHBOARD')}</div>
          </section>

          <InfoCardContainer />

          <DashboardChartsContainer />
        </article>
      </DashboardContextProvider>
    </NavLayout>
  );
};

export default DashboardPage;
