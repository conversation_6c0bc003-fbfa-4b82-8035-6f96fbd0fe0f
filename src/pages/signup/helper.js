import { defaultValidationDetail, validEmail } from '@zscaler/zui-component-library';

export const getFormValidationDetail = ({ formValues, isEmailVerified, isEmailOTPSent }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { pseudoDomainPrefix, primaryEmail } = formValues || {};

  if (pseudoDomainPrefix) {
    const blackList =
      /(^sso$)|(^admin$)|(^portal$)|(^opsadmin$)|(^www$)|(^test$)|(^my$)|(^opsadmin-admin[a-z0-9]*)|(^[a-z0-9-]*-admin)/;

    if (blackList.test(pseudoDomainPrefix)) {
      validationDetail.isValid = false;
      validationDetail.context = 'pseudoDomainPrefix';
      validationDetail.type = 'error';
      validationDetail.message =
        "Initial Domain Name can't be restricted words sso, admin, portal, opsadmin, opsadmin-admin, *-admin, www, my, test";

      return validationDetail;
    }

    const allowedChars = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;

    if (!allowedChars.test(pseudoDomainPrefix)) {
      validationDetail.isValid = false;
      validationDetail.context = 'pseudoDomainPrefix';
      validationDetail.type = 'error';
      validationDetail.message =
        "Allowed characters in the name are 'a' to 'z', '0' to '9' and '-' (hyphen, allowed only in-between)";

      return validationDetail;
    }
  }

  if (primaryEmail && validEmail(primaryEmail)) {
    validationDetail.isValid = false;
    validationDetail.context = 'primaryEmail';
    validationDetail.type = 'error';
    validationDetail.message = 'Email Address is not valid';

    return validationDetail;
  }

  if (!isEmailVerified && isEmailOTPSent) {
    validationDetail.isValid = false;
    validationDetail.context = 'primaryEmail';
    validationDetail.type = 'error';
    validationDetail.message = 'Verify Email Address';

    return validationDetail;
  }

  return validationDetail;
};
