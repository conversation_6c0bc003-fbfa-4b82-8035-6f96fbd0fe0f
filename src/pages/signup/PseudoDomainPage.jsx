import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  <PERSON><PERSON>,
  Card,
  Field,
  Input,
  defaultValidationDetail,
  mergeFormValues,
  useApiCall,
} from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import Logo from '../../components/logo/Logo';

import { updatePseudoDomain } from '../../ducks/tenant';

import { ZSCALER_FAQ } from '../../config';

const PseudoDomainPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const { domain = '', pseudoDomainPrefix = '' } = serverConfig || {};

  const [formState, setFormState] = useState(1);

  const [formValues, setFormValues] = useState({
    pseudoDomainPrefix: '',
    currentDomainPrefix: pseudoDomainPrefix,
    domain,
  });

  const [validationDetail, setValidationDetail] = useState({});

  const onFormFieldChange = (evt) => {
    setFormValues(mergeFormValues(evt));
  };

  useEffect(() => {
    const formValidationDetail = getFormValidationDetail({
      formValues,
    });

    setValidationDetail(formValidationDetail);
  }, [formValues]);

  const onUpdateClick = () => {
    apiCall(updatePseudoDomain(formValues))
      .then(() => {
        setFormState(2);
      })
      .catch(noop);
  };

  const getBodyContent = () => {
    if (formState === 2) {
      return (
        <Field label="NEW_PSEUDO_DOMAIN_NAME" htmlFor="currentDomainPrefix">
          <div>
            {formValues.pseudoDomainPrefix}.{formValues.domain}
          </div>
        </Field>
      );
    }

    return (
      <form>
        <Field label="CURRENT_PSEUDO_DOMAIN_NAME" htmlFor="currentDomainPrefix">
          <div>
            {formValues.currentDomainPrefix}.{formValues.domain}
          </div>
        </Field>

        <Field label="NEW_PSEUDO_DOMAIN_NAME" htmlFor="pseudoDomainPrefix" info={validationDetail}>
          <Field isStacked containerClass="no-m">
            <Input
              name="pseudoDomainPrefix"
              containerClass="no-m"
              onChange={onFormFieldChange}
              value={formValues.pseudoDomainPrefix}
              info={validationDetail}
              placeholder="Enter new pseudo domain"
              maxLength="57"
            />

            <div className="text-gray">.{formValues.domain}</div>
          </Field>
        </Field>
      </form>
    );
  };

  return (
    <>
      <article id="signup-page" className="page-container is-flex has-jc-c">
        <Card containerClass="small-card">
          <section className="header">
            <Logo isFull />
            <div className="intro-container" style={{ marginTop: '16px' }}>
              <div className="typography-header3">
                {' '}
                {t(formState === 1 ? 'UPDATE_PSEUDO_DOMAIN' : 'UPDATE_PSEUDO_DOMAIN_SUCCESS')}{' '}
              </div>
            </div>
          </section>
          <section className="body" style={{ marginTop: '48px' }}>
            {getBodyContent()}
          </section>
          <section className="footer has-ai-c" style={{ marginTop: '32px' }}>
            {formState === 1 && (
              <Button
                containerClass="full-width"
                disabled={!validationDetail.isValid}
                onClick={onUpdateClick}
              >
                {t('UPDATE')}
              </Button>
            )}
          </section>
        </Card>
      </article>
      <article id="signup-page-faq" className="page-container is-flex has-jc-c">
        <a href={ZSCALER_FAQ} className="has-color-primary" target={'_blank'} rel="noreferrer">
          Zscaler Privacy FAQ
        </a>
      </article>
    </>
  );
};

export const getFormValidationDetail = ({ formValues }) => {
  const validationDetail = { ...defaultValidationDetail };

  const { pseudoDomainPrefix } = formValues || {};

  if (!pseudoDomainPrefix) {
    validationDetail.isValid = false;
    validationDetail.context = 'pseudoDomainPrefix';
    validationDetail.type = 'error';
    validationDetail.message = 'Pseudo Domain Name is Required';

    return validationDetail;
  }

  if (pseudoDomainPrefix) {
    const blackList =
      /(^sso$)|(^admin$)|(^portal$)|(^opsadmin$)|(^www$)|(^test$)|(^my$)|(^opsadmin-admin[a-z0-9]*)|(^[a-z0-9-]*-admin)/;

    if (blackList.test(pseudoDomainPrefix)) {
      validationDetail.isValid = false;
      validationDetail.context = 'pseudoDomainPrefix';
      validationDetail.type = 'error';
      validationDetail.message =
        "Pseduo Domain Name can't be restricted words sso, admin, portal, opsadmin, opsadmin-admin, *-admin, www, my, test";

      return validationDetail;
    }

    const allowedChars = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;

    if (!allowedChars.test(pseudoDomainPrefix)) {
      validationDetail.isValid = false;
      validationDetail.context = 'pseudoDomainPrefix';
      validationDetail.type = 'error';
      validationDetail.message =
        "Allowed characters in the name are 'a' to 'z', '0' to '9' and '-' (hyphen, allowed only in-between)";

      return validationDetail;
    }
  }

  return validationDetail;
};

export default PseudoDomainPage;
