import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Card, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import SignonPolicyActions from '../../components/signon-policies/SignonPolicyActions';
import SignonPolicyCRUD from '../../components/signon-policies/SignonPolicyCRUD';
import SignonPolicyTable from '../../components/signon-policies/SignonPolicyTable';

import { selectPermissionsByKey } from '../../ducks/permissions/selectors';
import { getList } from '../../ducks/signon-policies';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';
import CRUDPageContextProvider from '../../contexts/CRUDPageContextProvider';

const SignonPolicyPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.SIGN_ON_POLICY));

  const ipLocationPrivileges = useSelector(
    selectPermissionsByKey(PERMISSIONS_KEY.IP_LOCATION_POLICY),
  );

  const { noAccess } = privileges;

  useEffect(() => {
    apiCall(getList()).catch(noop);
  }, []);

  if (noAccess || ipLocationPrivileges?.noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.SIGNON_POLICY} />

      <article id="signon-policy" className="page-container">
        <section className="typography-header3 page-title">{t('SIGN_ON_POLICIES')}</section>

        <CRUDPageContextProvider privileges={privileges}>
          <Card>
            <SignonPolicyActions />

            <SignonPolicyTable />
          </Card>

          <SignonPolicyCRUD />
        </CRUDPageContextProvider>
      </article>
    </NavLayout>
  );
};

export default SignonPolicyPage;
