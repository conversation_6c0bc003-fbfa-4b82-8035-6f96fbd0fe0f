import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { <PERSON><PERSON>, HelpContainer, useApiCall } from '@zscaler/zui-component-library';

import { noop } from 'lodash-es';

import NoAccess from '../../components/no-access/NoAccess';
import PasswordComplexity from '../../components/password-policy/PasswordComplexity';
import PasswordCriteria from '../../components/password-policy/PasswordCriteria';
import PasswordType from '../../components/password-policy/PasswordType';
import { getFormValidationDetail } from '../../components/password-policy/helper';

import { createPasswordPolicy, getPasswordPolicy } from '../../ducks/password-policy';
import { CONFIGURATION_TYPES } from '../../ducks/password-policy/constants';
import {
  selectActiveConfigDetail,
  selectActiveConfigurationType,
  selectCustomPasswordPolicyDetail,
  selectRecommendedPasswordPolicyDetail,
} from '../../ducks/password-policy/selectors';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import NavLayout from '../../layout/NavLayout';

import { HELP_ARTICLES, PERMISSIONS_KEY } from '../../config';

const PasswordPage = () => {
  const { t } = useTranslation();
  const { apiCall } = useApiCall();

  const activeConfigurationType = useSelector(selectActiveConfigurationType);
  const activeConfigDetail = useSelector(selectActiveConfigDetail);

  const recommendedPolicyDetail = useSelector(selectRecommendedPasswordPolicyDetail);
  const customPolicyDetail = useSelector(selectCustomPasswordPolicyDetail);
  const privileges = useSelector(selectPermissionsByKey(PERMISSIONS_KEY.AUTHENTICATION_METHODS));

  const { hasFullAccess, noAccess } = privileges;

  const [selectedConfigurationType, setSelectedConfigurationType] =
    useState(activeConfigurationType);

  const [isPolicyUpdated, setIsPolicyUpdated] = useState(false);

  const isRecommended = selectedConfigurationType === 'RECOMMENDED';

  const [formValues, setFormValues] = useState({ expiryAge: 15 });
  const [validationDetail, setValidationDetail] = useState({});

  const [selectedListsDetail, setSelectedListsDetail] = useState({
    minLength: [{ label: formValues.minLength, value: formValues.minLength }],
    minLowerCase: [{ label: formValues.minLowerCase, value: formValues.minLowerCase }],
    minUpperCase: [{ label: formValues.minUpperCase, value: formValues.minUpperCase }],
    minNumeric: [{ label: formValues.minNumeric, value: formValues.minNumeric }],
    minSpecialChar: [{ label: formValues.minSpecialChar, value: formValues.minSpecialChar }],
    expiryAge: [{ label: formValues.expiryAge, value: formValues.expiryAge }],
  });

  useEffect(() => {
    apiCall(getPasswordPolicy()).catch(noop);
  }, []);

  useEffect(() => {
    if (!isRecommended) {
      apiCall(getPasswordPolicy({ configurationType: 'RECOMMENDED' })).catch(noop);
    }
  }, [isRecommended]);

  useEffect(() => {
    setValidationDetail(getFormValidationDetail({ formValues }));
  }, [formValues]);

  const updateActiveDetail = () => {
    setSelectedConfigurationType(activeConfigurationType);

    resetForm(activeConfigDetail);
  };

  const resetForm = (configDetail) => {
    setFormValues({ ...configDetail });

    setSelectedListsDetail({
      minLength: [{ label: configDetail.minLength, value: configDetail.minLength }],
      minLowerCase: [{ label: configDetail.minLowerCase, value: configDetail.minLowerCase }],
      minUpperCase: [{ label: configDetail.minUpperCase, value: configDetail.minUpperCase }],
      minNumeric: [{ label: configDetail.minNumeric, value: configDetail.minNumeric }],
      minSpecialChar: [{ label: configDetail.minSpecialChar, value: configDetail.minSpecialChar }],
      expiryAge: [{ label: configDetail.expiryAge, value: configDetail.expiryAge }],
    });
  };

  useEffect(() => {
    updateActiveDetail();
    setIsPolicyUpdated(false);
  }, [activeConfigDetail]);

  useEffect(() => {
    if (selectedConfigurationType === CONFIGURATION_TYPES.RECOMMENDED) {
      resetForm(recommendedPolicyDetail);
    }

    if (selectedConfigurationType === CONFIGURATION_TYPES.CUSTOM) {
      resetForm(customPolicyDetail);
    }
  }, [selectedConfigurationType, recommendedPolicyDetail, customPolicyDetail]);

  const updateConfigurationType = (newType) => {
    setSelectedConfigurationType(newType);

    if (newType !== activeConfigurationType) {
      setIsPolicyUpdated(true);
    } else {
      setIsPolicyUpdated(false);
    }

    apiCall(getPasswordPolicy({ configurationType: newType })).catch(noop);
  };

  const onSelection = (name, detail) => {
    const { value } = detail[0] || {};

    setFormValues((prevState) => {
      setSelectedListsDetail((prevState) => ({
        ...prevState,
        [name]: [{ label: value, value: value }],
      }));

      return { ...prevState, [name]: value };
    });

    setIsPolicyUpdated(true);
  };

  const savePassword = () => {
    apiCall(
      createPasswordPolicy({ configurationType: selectedConfigurationType, config: formValues }),
    ).catch(noop);
  };

  const cancelPassword = () => {
    updateActiveDetail();
    setIsPolicyUpdated(false);
  };

  const renderActionsSection = () => {
    const enableAction = isPolicyUpdated && validationDetail.isValid;

    if (!hasFullAccess) {
      return null;
    }

    return (
      <div className="action-section buttons">
        <Button
          onClick={() => {
            savePassword();
          }}
          disabled={!enableAction}
        >
          {t('SAVE')}
        </Button>
        <Button
          type="tertiary"
          containerClass="no-p-l"
          onClick={() => {
            cancelPassword();
          }}
          disabled={!isPolicyUpdated}
        >
          {t('CANCEL')}
        </Button>
      </div>
    );
  };

  if (noAccess) {
    return <NoAccess />;
  }

  return (
    <NavLayout>
      <HelpContainer src={HELP_ARTICLES.PASSWORD_POLICY} />
      <article id="password-policy" className="page-container">
        <section className="typography-header3 page-title">{t('PASSWORD_POLICY')}</section>

        <div className={`config-content ${isPolicyUpdated ? 'has-update-section' : ''}`}>
          <PasswordType
            formValues={formValues}
            isRecommended={isRecommended}
            updateConfigurationType={updateConfigurationType}
            hasFullAccess={hasFullAccess}
          />

          <PasswordComplexity
            selectedListsDetail={selectedListsDetail}
            onSelection={onSelection}
            isRecommended={isRecommended}
            hasFullAccess={hasFullAccess}
          />

          <PasswordCriteria
            formValues={formValues}
            setFormValues={setFormValues}
            validationDetail={validationDetail}
            setIsPolicyUpdated={setIsPolicyUpdated}
            isRecommended={isRecommended}
            hasFullAccess={hasFullAccess}
          />
        </div>

        {renderActionsSection()}
      </article>
    </NavLayout>
  );
};

export default PasswordPage;
