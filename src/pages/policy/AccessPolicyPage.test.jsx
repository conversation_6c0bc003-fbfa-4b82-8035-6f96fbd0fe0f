import { Provider } from 'react-redux';

import { render, screen } from '@testing-library/react';
import { useApiCall } from '@zscaler/zui-component-library';

import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';

import { getCriteriaValues, getList } from '../../ducks/access-policies';
import { selectPermissionsByKey } from '../../ducks/permissions/selectors';

import { PERMISSIONS_KEY } from '../../config';
import AccessPolicyPage from './AccessPolicyPage';

jest.mock('@zscaler/zui-component-library', () => ({
  useApiCall: jest.fn(),
}));

jest.mock('../../ducks/access-policies', () => ({
  getCriteriaValues: jest.fn(),
  getList: jest.fn(),
}));

jest.mock('../../ducks/permissions/selectors', () => ({
  selectPermissionsByKey: jest.fn(),
}));

const mockStore = configureStore([thunk]);

describe('AccessPolicyPage', () => {
  let store;
  let apiCallMock;

  beforeEach(() => {
    apiCallMock = jest.fn().mockResolvedValue({});
    useApiCall.mockReturnValue({ apiCall: apiCallMock });

    store = mockStore({
      permissions: {
        [PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES]: {
          noAccess: false,
        },
      },
    });

    selectPermissionsByKey.mockImplementation((key) => (state) => state.permissions[key]);
  });

  it('should render AccessPolicyPageContainer when user has access', () => {
    render(
      <Provider store={store}>
        <AccessPolicyPage />
      </Provider>,
    );

    expect(screen.getByText('AccessPolicyPageContainer')).toBeInTheDocument();
  });

  it('should render NoAccess when user has no access', () => {
    store = mockStore({
      permissions: {
        [PERMISSIONS_KEY.API_CLIENTS_AND_RESOURCES]: {
          noAccess: true,
        },
      },
    });

    render(
      <Provider store={store}>
        <AccessPolicyPage />
      </Provider>,
    );

    expect(screen.getByText('NoAccess')).toBeInTheDocument();
  });

  it('should call apiCall with getCriteriaValues and getList', async () => {
    render(
      <Provider store={store}>
        <AccessPolicyPage />
      </Provider>,
    );

    expect(apiCallMock).toHaveBeenCalledWith(getCriteriaValues());
    await screen.findByText('AccessPolicyPageContainer');
    expect(apiCallMock).toHaveBeenCalledWith(getList());
  });
});
