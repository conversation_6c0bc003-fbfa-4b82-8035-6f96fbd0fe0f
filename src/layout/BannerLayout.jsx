import PropTypes from 'prop-types';

import EUSABanner from '../components/banners/EUSABanner';
import MigrationBanner from '../components/banners/MigrationBanner';
import UIBanner from '../components/banners/UIBanner';

const DEFAULT_PROPS = {
  isAppSetupDone: false,
  isAuthFreePage: false,
};

const BannerLayout = ({
  isAppSetupDone = DEFAULT_PROPS.isAppSetupDone,
  isAuthFreePage = DEFAULT_PROPS.isAuthFreePage,
}) => {
  if (isAuthFreePage) {
    return null;
  }

  return (
    <div id="banner-layout" className="is-flex has-fd-c has-jc-c has-ai-c">
      <EUSABanner isAppSetupDone={isAppSetupDone} isAuthFreePage={isAuthFreePage} />
      <MigrationBanner />
      <UIBanner />
    </div>
  );
};

BannerLayout.propTypes = {
  isAppSetupDone: PropTypes.bool,
  isAuthFreePage: PropTypes.bool,
};

export default BannerLayout;
