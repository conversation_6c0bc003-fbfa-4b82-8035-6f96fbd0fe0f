import { useEffect, useMemo, useState } from 'react';

const DEFAULT_MODAL_MODE = '';
const DEFAULT_DETAIL = {};
const DEFAULT_BULK_ACTION_OPTION = {
  label: 'ACTIONS',
  value: 'ACTIONS',
};
const DEFAULT_SEARCH_OPTION = {};
const DEFAULT_CSV_IMPORT_DETAIL = {};
const DEFAULT_CSV_IMPORT_RESULT_DETAIL = null;

const DEFAULT_PRIVILEGES = {
  hasFullAccess: false,
  noAccess: false,
};

export const useCRUDPageContext = ({
  defaultModalMode = DEFAULT_MODAL_MODE,
  defaultDetail = DEFAULT_DETAIL,
  defaultBulkActionOption = DEFAULT_BULK_ACTION_OPTION,
  defaultSearchOption = DEFAULT_SEARCH_OPTION,
  defaultCsvImportDetail = DEFAULT_CSV_IMPORT_DETAIL,
  defaultCsvImportResultDetail = DEFAULT_CSV_IMPORT_RESULT_DETAIL,
  privileges = DEFAULT_PRIVILEGES,
} = {}) => {
  const [modalMode, setModalMode] = useState(defaultModalMode);
  const [isFormReadOnly, setIsFormReadOnly] = useState(false);
  const [detail, setDetail] = useState(defaultDetail);
  const [selectedRowDetail, setSelectedRowDetail] = useState([]);
  const [selectedBulkAction, setSelectedBulkAction] = useState(defaultBulkActionOption);
  const [selectedSearchField, setSelectedSearchField] = useState(defaultSearchOption);
  const [searchTerm, setSearchTerm] = useState('');
  const [csvImportDetail, setCsvImportDetail] = useState(defaultCsvImportDetail);
  const [csvImportResult, setCsvImportResult] = useState(defaultCsvImportResultDetail);

  useEffect(() => {
    const { hasFullAccess } = privileges;

    if (modalMode === 'view' || !hasFullAccess) {
      setIsFormReadOnly(true);
    } else {
      setIsFormReadOnly(false);
    }
  }, [modalMode, privileges]);

  const contextValue = useMemo(() => {
    return {
      modalMode,
      setModalMode,
      isFormReadOnly,
      setIsFormReadOnly,
      detail,
      setDetail,
      selectedRowDetail,
      setSelectedRowDetail,
      selectedBulkAction,
      setSelectedBulkAction,
      selectedSearchField,
      setSelectedSearchField,
      searchTerm,
      setSearchTerm,
      csvImportDetail,
      setCsvImportDetail,
      csvImportResult,
      setCsvImportResult,
      defaultModalMode,
      defaultDetail,
      defaultBulkActionOption,
      defaultSearchOption,
      defaultCsvImportDetail,
      defaultCsvImportResultDetail,
      privileges,
    };
  }, [
    modalMode,
    setModalMode,
    isFormReadOnly,
    setIsFormReadOnly,
    detail,
    setDetail,
    selectedRowDetail,
    setSelectedRowDetail,
    selectedBulkAction,
    setSelectedBulkAction,
    selectedSearchField,
    setSelectedSearchField,
    searchTerm,
    setSearchTerm,
    csvImportDetail,
    setCsvImportDetail,
    csvImportResult,
    setCsvImportResult,
    defaultModalMode,
    defaultDetail,
    defaultBulkActionOption,
    defaultSearchOption,
    defaultCsvImportDetail,
    defaultCsvImportResultDetail,
    privileges,
  ]);

  return contextValue;
};
