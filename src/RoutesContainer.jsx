import { Suspense } from 'react';
import { Route, Routes } from 'react-router-dom';

import { Spinner } from '@zscaler/zui-component-library';

import AppLayout from './layout/AppLayout';

import AdminEntitlementsPage from './pages/admin/AdminEntitlementsPage';
import AdvancedSettingsPage from './pages/admin/AdvancedSettingsPage';
import ApiClientsPage from './pages/admin/ApiClientsPage';
import ApiResourcesPage from './pages/admin/ApiResourcesPage';
import AttributesPage from './pages/admin/AttributesPage';
import AuditLogsPage from './pages/admin/AuditLogsPage';
import AuthenticationLevelsPage from './pages/admin/AuthenticationLevels';
import AuthenticationMethodsPage from './pages/admin/AuthenticationMethodsPage';
import BrandingPage from './pages/admin/BrandingPage';
import DepartmentsPage from './pages/admin/DepartmentsPage';
import DeviceTokenPage from './pages/admin/DeviceTokenPage';
import DomainsPage from './pages/admin/DomainsPage';
import ExternalIdentitesPage from './pages/admin/ExternalIdentitiesPage';
import IpLocationGroupsPage from './pages/admin/IpLocationGroupsPage';
import IpLocationsPage from './pages/admin/IpLocationsPage';
import LinkedServicesPage from './pages/admin/LinkedServicesPage';
import MyProfilePage from './pages/admin/MyProfilePage';
import RolesPage from './pages/admin/RolesPage';
import ServiceEntitlementsPage from './pages/admin/ServiceEntitlementsPage';
import TokenValidatorsPage from './pages/admin/TokenValidatorsPage';
import TokensPage from './pages/admin/TokensPage';
import UserGroupsPage from './pages/admin/UserGroupsPage';
import UsersPage from './pages/admin/UsersPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import RemoteAssistancePage from './pages/help/RemoteAssistancePage';
import AccessPolicyPage from './pages/policy/AccessPolicyPage';
import PasswordPage from './pages/policy/PasswordPage';
import SignonPolicyPage from './pages/policy/SignonPolicyPage';
import PseudoDomainPage from './pages/signup/PseudoDomainPage';
import SignupPage from './pages/signup/SignupPage';

const RoutesContainer = () => {
  return (
    <Suspense fallback={<Spinner />}>
      <Routes>
        <Route path="/" element={<AppLayout />}>
          <Route index element={<DashboardPage />} />

          <Route path="signup" element={<SignupPage />} />
          <Route path="pseudo-domain" element={<PseudoDomainPage />} />

          <Route path="dashboard" element={<DashboardPage />} />

          <Route path="policy/signon" element={<SignonPolicyPage />} />
          <Route path="policy/password" element={<PasswordPage />} />
          <Route path="policy/access" element={<AccessPolicyPage />} />

          <Route path="admin/advanced-settings" element={<AdvancedSettingsPage />} />
          <Route path="admin/audit-logs" element={<AuditLogsPage />} />
          <Route path="admin/user-groups" element={<UserGroupsPage />} />
          <Route path="admin/attributes" element={<AttributesPage />} />
          <Route path="admin/departments" element={<DepartmentsPage />} />
          <Route path="admin/users" element={<UsersPage />} />
          <Route path="admin/linked-services" element={<LinkedServicesPage />} />
          <Route path="admin/external-identities" element={<ExternalIdentitesPage />} />
          <Route path="admin/my-profile" element={<MyProfilePage />} />
          <Route path="admin/branding" element={<BrandingPage />} />
          <Route path="admin/ip-locations" element={<IpLocationsPage />} />
          <Route path="admin/ip-location-groups" element={<IpLocationGroupsPage />} />
          <Route path="admin/service-entitlements" element={<ServiceEntitlementsPage />} />
          <Route path="admin/admin-entitlements" element={<AdminEntitlementsPage />} />
          <Route path="admin/roles" element={<RolesPage />} />
          <Route path="admin/authentication-methods" element={<AuthenticationMethodsPage />} />
          <Route path="admin/device-token" element={<DeviceTokenPage />} />
          <Route path="admin/token-validators" element={<TokenValidatorsPage />} />
          <Route path="admin/authentication-levels" element={<AuthenticationLevelsPage />} />
          <Route path="admin/tokens" element={<TokensPage />} />
          <Route path="admin/domains" element={<DomainsPage />} />
          <Route path="admin/api-clients" element={<ApiClientsPage />} />
          <Route path="admin/api-resources" element={<ApiResourcesPage />} />

          <Route path="help/remote-assistance" element={<RemoteAssistancePage />} />

          {/* <Route path="*" element={<Navigate to="admin/my-profile" />} /> */}
          <Route path="*" element={<MyProfilePage />} />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default RoutesContainer;
