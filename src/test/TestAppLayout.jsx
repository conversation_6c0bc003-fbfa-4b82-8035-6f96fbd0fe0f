import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';

import { noop } from 'lodash-es';

import { appSetupDone } from '../ducks/global';
import { selectIsAppSetupDone } from '../ducks/global/selectors';
import { getMyPermissions } from '../ducks/permissions';
import { getProfile } from '../ducks/profile';

import {
  setupApiMethodMessageOption,
  setupFloatingPortalRootId,
  setupUseApiCallFunctionsRegistry,
} from '../setup';

setupApiMethodMessageOption();
setupUseApiCallFunctionsRegistry();
setupFloatingPortalRootId();

const TestAppLayout = ({ children }) => {
  const dispatch = useDispatch();

  const isAppSetupDone = useSelector(selectIsAppSetupDone);

  useEffect(() => {
    dispatch(getMyPermissions())
      .catch(noop)
      .finally(() => {
        dispatch(getProfile()).catch(noop);

        dispatch(appSetupDone());
      });
  }, []);

  const renderRoutesSection = () => {
    if (!isAppSetupDone) {
      return null;
    }

    return children;
  };

  return renderRoutesSection();
};

export default TestAppLayout;
