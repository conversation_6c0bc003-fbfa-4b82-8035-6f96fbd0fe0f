# Main registry for dependencies
@zscaler:registry=https://nexus.corp.zscaler.com/repository/npm-releases/
@zscaler/zui-component-library:registry=https://nexus.corp.zscaler.com/repository/npm-releases/
//nexus.corp.zscaler.com/repository/npm-releases/:_auth="ZW5nLXJlYWRvbmx5OmVuZy1yZWFkb25seQ=="

@zs-nimbus:registry=https://nexus.corp.zscaler.com/repository/zscaler-npm-registry/
@zs-nimbus/foundations:registry=https://nexus.corp.zscaler.com/repository/zscaler-npm-registry/

@fortawesome:registry=https://nexus.corp.zscaler.com/repository/zscaler-npm-registry/
//nexus.corp.zscaler.com/repository/zscaler-npm-registry/:_auth="ZW5nLXJlYWRvbmx5OmVuZy1yZWFkb25seQ=="

# Publishing registry with auth token
//nexus.corp.zscaler.com/repository/zuxp-npm-releases/:_auth="ZG5wZXR6WVY6MU5QTXBjRHNvUndrMjVFaFBFRzJiMXdXcVNjTmg2RmN4LXZXYlpuSG00aUY="