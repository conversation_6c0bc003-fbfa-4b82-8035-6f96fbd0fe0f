{"singleQuote": true, "trailingComma": "all", "tabWidth": 2, "printWidth": 100, "importOrder": ["^react(.*)", "^@(.*)/(.*)$", "<THIRD_PARTY_MODULES>", "^([.]+/)*components/(.*)$", "^([.]+/)*config/(.*)$", "^([.]+/)*ducks/(.*)$", "^([.]+/)*layout/(.*)$", "^([.]+/)*pages/(.*)$", "^([.]+/)*scss/(.*)$", "^([.]+/)*utils/(.*)$", "^[./]"], "importOrderSortSpecifiers": true, "importOrderSeparation": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrderParserPlugins": ["jsx", "typescript", "classProperties"]}