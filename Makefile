# common script

start:
	npm start

update:
	echo $@
	npm run update

install:
	echo $@
	pnpm i

build: install
	echo $@
	npm run build

lib: install
	echo $@
	npm run build-lib

zip: build
	echo $@
	tar --no-mac-metadata -czf admin.dist.tar.gz -C ./dist .

cp-dev: zip
	echo $@
	scp admin.dist.tar.gz deploy-admin.sh guest@************:~

cp-local: zip
	echo $@
	tar xvzf admin.dist.tar.gz  -C ../oidp/cust_manage/management/src/main/resources/static/iam
	cp ../oidp/cust_manage/management/src/main/resources/static/iam/index.html ../oidp/cust_manage/management/src/main/resources/templates/

ssh-dev:
	echo $@
	ssh guest@************

deploy-dev: cp-dev ssh-dev
