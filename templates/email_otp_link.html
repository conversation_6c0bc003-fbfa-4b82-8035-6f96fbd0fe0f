<!doctype html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>User Email Authentication</title>
  </head>
  <body style="font-family: Arial; font-size: 14px; margin: 0">
    <table cellspacing="0" cellpadding="0" width="100%" align="center">
      <tbody>
        <tr>
          <td align="center">
            <!-- Header Section -->
            <table cellspacing="0" cellpadding="0" width="600">
              <tr>
                <td style="padding: 12px; background-color: #2160e1">
                  <table cellspacing="0" cellpadding="0">
                    <tbody>
                      <tr>
                        <td>
                          <table cellspacing="0" cellpadding="0" width="382" style="width: 382px">
                            <tbody>
                              <tr>
                                <td>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="140"
                                    height="29"
                                    viewBox="0 0 140 29"
                                    fill="none"
                                  >
                                    <g clip-path="url(#clip0_2467_100552)">
                                      <path
                                        d="M46.6799 14.68C47.4395 19.2939 43.4101 21.8922 39.3299 22.1919C36.7328 27.5044 27.5729 31.0904 19.6415 27.2237C16.2466 27.7366 14.3857 26.8923 12.9493 25.2798C15.8519 21.565 24.6384 14.8806 35.3727 18.3315C41.1016 20.172 42.5869 15.8451 41.1504 14.222C35.744 8.11378 23.5393 13.6121 23.0979 14.0701C27.8933 6.77352 45.0461 4.72618 46.6799 14.68ZM30.2421 6.2944C30.2718 6.28384 26.3443 4.90347 20.8509 7.26319C20.6294 7.1825 20.4126 7.08949 20.2016 6.98458C25.485 3.49566 30.1169 2.01187 34.0847 2.58808C31.6871 -0.155778 20.1656 -2.17357 13.5646 4.40325C5.40405 2.80548 -0.303653 9.67989 0.0209863 15.9654C0.345625 22.251 7.23519 26.1599 11.1606 25.1004C11.255 25.0888 11.3505 25.0888 11.4449 25.1004C12.3127 20.9487 15.8583 11.2903 30.2421 6.2944ZM63.1049 12.0354L63.8369 8.83774H51.5495L51.2715 10.0767C50.9511 11.1721 51.8211 11.9467 52.7568 11.9214H58.4496L48.3582 20.7862L47.6771 23.758H60.6945L61.4435 20.5097H53.2108L63.1049 12.0354ZM71.3036 14.6167C70.7265 14.4711 67.4844 13.9835 67.6965 12.8754C67.9236 11.6301 69.4555 11.5119 70.2003 11.5331C71.7344 11.5774 72.2181 12.2486 72.2988 12.485C72.5109 13.1182 73.0032 13.439 73.7183 13.439H76.1414C76.4066 11.9446 76.4788 8.50426 70.8496 8.50426C69.0418 8.50426 66.6335 8.95383 65.04 10.7817C64.1913 11.761 62.7166 14.1904 64.221 16.0203C65.0082 16.9427 67.06 17.6645 69.2624 18.093C70.6862 18.3695 71.4394 18.9372 71.2824 19.6042C71.1869 20.0538 70.6204 21.0669 68.524 21.0648C67.5862 21.0648 65.5535 20.9191 65.9418 18.8233H62.1098C61.6599 20.7229 61.4944 24.0366 67.3379 24.0366C69.5913 24.0366 74.1787 23.7538 75.3733 18.9879C76.2347 15.5581 72.3476 14.8784 71.3036 14.6167ZM84.8493 19.8934C84.5186 20.1967 84.131 20.4323 83.7086 20.5865C83.2863 20.7408 82.8376 20.8108 82.388 20.7925C79.4939 20.8178 79.8206 17.9706 80.2153 16.2504C80.6099 14.5302 81.5414 11.7589 84.2934 11.7652C86.2434 11.7652 86.4683 13.0211 86.568 13.3904H90.5125C90.3725 9.24932 87.1812 8.45782 84.9257 8.5127C78.7427 8.66467 76.954 13.8062 76.4003 16.1955C74.7855 23.1733 79.2732 24.045 81.4205 24.045C83.1837 24.045 87.037 23.6229 89.3009 19.1673H86.4938C85.6726 19.1293 85.2992 19.4902 84.8493 19.8765V19.8934ZM100.956 21.3265C99.819 22.6616 98.2378 23.5461 96.5003 23.8192C93.0735 24.3827 88.3631 24.2413 89.5492 19.3362C89.929 17.7679 90.5167 16.7443 91.6986 15.9718C93.6783 14.6779 96.3093 14.4943 97.1602 14.4014C97.4509 14.3677 99.6066 14.2073 99.9758 12.9388C100.337 11.7061 98.359 11.6048 97.8052 11.5985C96.1077 11.5774 95.5327 12.2528 95.259 12.6538H91.4822C93.0078 8.66044 97.2111 8.50637 98.3569 8.50637C99.63 8.50637 104.735 8.50637 103.761 12.715C102.263 19.1335 102.208 19.7731 100.956 21.3181V21.3265ZM99.2586 16.2504C98.6518 16.5817 97.1368 16.9068 96.8949 16.9764C96.1077 17.2149 94.631 17.3057 93.9032 17.9853C93.2857 18.5658 92.2057 20.9635 94.7859 21.0141C96.39 21.0458 98.393 20.1488 98.9616 17.5442C99.0974 16.9469 99.2671 16.2419 99.2671 16.2419L99.2586 16.2504ZM108.985 4.46868H106.948L102.522 23.758H106.466L110.47 6.31972C110.632 5.76884 110.519 4.51933 108.985 4.46868ZM119.21 19.161H122.03C121.143 20.9424 118.847 24.0429 114.145 24.0387C108.145 24.0387 108.514 18.7156 109.127 16.1892C110.825 9.16911 115.552 8.50637 117.655 8.50637C120.46 8.50637 124.854 9.6609 122.624 17.3078H112.864C112.626 18.323 112.422 20.8157 115.113 20.7862C116.528 20.7693 117.059 20.2606 117.186 20.1952C117.549 20.0073 117.897 19.1821 119.202 19.1589L119.21 19.161ZM113.441 14.7856H119.189C119.681 12.0417 117.975 11.7568 117.01 11.7568C116.184 11.7568 114.22 12.0523 113.441 14.7856ZM127.657 10.4672C125.015 12.2591 124.143 15.1402 123.838 16.5248C123.596 17.6117 122.176 23.7559 122.176 23.7559H126.125C126.125 23.7559 127.621 17.0018 127.884 16.0752C128.196 14.9945 129.212 12.0649 133.146 12.1852L134.059 8.83774C130.876 8.73432 128.775 9.70733 127.657 10.4672ZM134.545 5.92714L134.644 5.46068H136.766L136.666 5.92714H135.903L135.478 7.99348H134.914L135.349 5.92714H134.545ZM138.037 5.46068L138.232 7.29485L139.213 5.46068H139.993L139.454 7.99348H138.93L139.395 5.9778L138.319 7.99348H137.895L137.649 5.9778L137.267 7.99348H136.739L137.273 5.46068H138.037Z"
                                        fill="white"
                                      />
                                    </g>
                                    <defs>
                                      <clipPath id="clip0_2467_100552">
                                        <rect width="140" height="28.8485" fill="white" />
                                      </clipPath>
                                    </defs>
                                  </svg>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </table>

            <!-- Content Section - START -->
            <table cellspacing="0" cellpadding="0" width="600">
              <tbody>
                <tr>
                  <td
                    style="padding: 10px 16px; font-size: 16px; font-family: Helvetica"
                    th:text="'Hello'"
                  >
                    Welcome
                  </td>
                </tr>
              </tbody>
            </table>

            <table cellspacing="0" cellpadding="0" width="600">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 16px; border-spacing: 0">
                    <table cellspacing="0" cellpadding="0">
                      <tbody>
                        <tr>
                          <td
                            style="
                              padding-top: 6px;
                              padding-bottom: 10px;
                              font-size: 0.88em;
                              font-family: Helvetica;
                            "
                          >
                            Your Link : <b th:text="${authnOtpLink}"></b>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- Content Section - END -->

            <!-- Footer Section -->
            <table cellspacing="0" cellpadding="0" width="600">
              <tr>
                <td
                  align="left"
                  style="
                    padding: 12px 16px 4px;
                    color: #2160e1;
                    background-color: #f7f9fa;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 24px;
                  "
                >
                  <a href="www.zscaler.com">www.zscaler.com </a>
                </td>
              </tr>
              <tr>
                <td
                  align="left"
                  style="
                    padding: 4px 16px;
                    color: #464646;
                    background-color: #f7f9fa;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 132%;
                  "
                >
                  Please do not reply to this automated email.
                </td>
              </tr>
              <tr>
                <td
                  id="company-info"
                  align="left"
                  style="
                    padding: 4px 16px 12px;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 132%;
                    color: #767676;
                    background-color: #f7f9fa;
                  "
                ></td>
              </tr>
            </table>
          </td>
        </tr>
      </tbody>
    </table>

    <script type="text/javascript">
      var year = new Date().getFullYear();
      var companyInfoEle = document.getElementById('company-info');
      var copyRightTemplate = 'Copyright©2007-{{year}} Zscaler Inc. All rights reserved.';
      copyRightTemplate = copyRightTemplate.replace(/{{(.*?)}}/, year);
      companyInfoEle.textContent = copyRightTemplate;
    </script>
  </body>
</html>
